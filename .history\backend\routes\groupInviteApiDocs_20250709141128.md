# /api/group-savings/group-plans/invite Endpoint Implementation Plan

## Requirements
- POST endpoint: `/api/group-savings/group-plans/invite`
- Authenticated (uses `authenticateToken`)
- Request body: `{ groupId, email }`
- Finds group by `groupId`, checks if user is owner
- Generates invite link using group's `inviteCode`
- Calls `sendGroupInvite(email, group.title, inviteLink)`
- Returns success or error

## Steps
1. Validate input (groupId, email)
2. Find group by groupId
3. Check if requester is group owner
4. Generate invite link: `${process.env.FRONTEND_URL}/join?inviteCode=${group.inviteCode}`
5. Call `sendGroupInvite`
6. Return 200 on success, 400/403/404/500 on error

---

## Example Request
POST /api/group-savings/group-plans/invite
Authorization: Bearer ...
{
  "groupId": "...",
  "email": "<EMAIL>"
}

## Example Response
{
  "message": "Invite sent successfully."
}

## Error Cases
- 400: Missing params
- 403: Not group owner
- 404: Group not found
- 500: Mailer error
