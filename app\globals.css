/* Font Imports - Must come first */
@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Fugaz+One&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
    /* 3-Color System: Dark, Light, Brand Green */
    --brand-green: #22c55e;
    --brand-green-dark: #16a34a;
    --brand-green-light: #4ade80;
    --brand-green-rgb: 34, 197, 94;

    /* Light Theme Colors */
    --light-bg: #ffffff;
    --light-bg-secondary: #f8fafc;
    --light-text: #020b09;
    --light-text-secondary: #64748b;
    --light-border: #e2e8f0;

    /* Dark Theme Colors */
    --dark-bg: #000000;
    --dark-bg-secondary: #010101;
    --dark-text: #f8fafc;
    --dark-text-secondary: #94a3b8;
    --dark-border: #22c55e;

    /* Legacy support */
    --foreground-rgb: 248, 250, 252;
    --background-start-rgb: 15, 23, 42;
    --background-end-rgb: 15, 23, 42;
    --green-primary: 34, 197, 94;
    --green-secondary: 22, 163, 74;
    --green-accent: 34, 197, 94;
}

@layer utilities {
    .text-balance {
        text-wrap: balance;
    }

    /* Font utilities */
    .font-display {
        font-family: var(--font-fugaz), 'Fugaz One', cursive;
    }

    .font-inter {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    }

    /* 3-Color System Utilities */
    .bg-brand {
        background-color: var(--brand-green);
    }

    .bg-brand-dark {
        background-color: var(--brand-green-dark);
    }

    .bg-brand-light {
        background-color: var(--brand-green-light);
    }

    .text-brand {
        color: var(--brand-green);
    }

    .text-brand-dark {
        color: var(--brand-green-dark);
    }

    .border-brand {
        border-color: var(--brand-green);
    }

    .ring-brand {
        --tw-ring-color: var(--brand-green);
    }

    /* Theme-aware utilities */
    .bg-theme {
        background-color: var(--dark-bg);
    }

    .bg-theme-secondary {
        background-color: var(--dark-bg-secondary);
    }

    .text-theme {
        color: var(--dark-text);
    }

    .text-theme-secondary {
        color: var(--dark-text-secondary);
    }

    .border-theme {
        border-color: var(--dark-border);
    }

    /* Light theme overrides */
    .light .bg-theme {
        background-color: var(--light-bg);
    }

    .light .bg-theme-secondary {
        background-color: var(--light-bg-secondary);
    }

    .light .text-theme {
        color: var(--light-text);
    }

    .light .text-theme-secondary {
        color: var(--light-text-secondary);
    }

    .light .border-theme {
        border-color: var(--light-border);
    }

    /* Text shadow utilities */
    .text-shadow-sm {
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    }

    .text-shadow {
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }

    .text-shadow-lg {
        text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.7);
    }

    .text-shadow-green {
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5), 0 0 20px rgba(34, 197, 94, 0.3);
    }

    .text-shadow-green-strong {
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7), 0 0 30px rgba(34, 197, 94, 0.5);
    }
}

@layer base {
    :root {
        --background: 0 0% 0%;
        --foreground: 0 0% 100%;
        --card: 0 0% 0%;
        --card-foreground: 0 0% 100%;
        --popover: 0 0% 0%;
        --popover-foreground: 0 0% 100%;
        --primary: 142 76% 36%;
        --primary-foreground: 0 0% 100%;
        --secondary: 0 0% 0%;
        --secondary-foreground: 0 0% 100%;
        --muted: 0 0% 5%;
        --muted-foreground: 0 0% 65%;
        --accent: 0 0% 5%;
        --accent-foreground: 0 0% 100%;
        --destructive: 0 84% 60%;
        --destructive-foreground: 0 0% 100%;
        --border: 0 0% 10%;
        --input: 0 0% 5%;
        --ring: 142 76% 36%;
        --chart-1: 142 76% 36%;
        --chart-2: 142 69% 58%;
        --chart-3: 142 47% 24%;
        --chart-4: 142 74% 66%;
        --chart-5: 142 87% 67%;
        --radius: 0.75rem;
    }
}

@layer base {
    * {
        @apply border-border;
        box-sizing: border-box;
    }

    html {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        font-feature-settings: "cv02", "cv03", "cv04", "cv11", "rlig" 1, "calt" 1;
        font-variation-settings: normal;
    }

    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        background-color: var(--dark-bg);
        color: var(--dark-text);
        transition: background-color 0.3s ease, color 0.3s ease;
        font-feature-settings: "cv02", "cv03", "cv04", "cv11", "rlig" 1, "calt" 1;
        font-variation-settings: normal;
        line-height: 1.5;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    body.light {
        background-color: var(--light-bg);
        color: var(--light-text);
    }
}

/* Onlook-style background animation */
@keyframes mesh-gradient {
    0%, 100% {
        transform: translate(0%, 0%) rotate(0deg);
    }
    33% {
        transform: translate(30%, -30%) rotate(120deg);
    }
    66% {
        transform: translate(-20%, 20%) rotate(240deg);
    }
}

@keyframes mesh-gradient-2 {
    0%, 100% {
        transform: translate(0%, 0%) rotate(0deg);
    }
    33% {
        transform: translate(-30%, 30%) rotate(-120deg);
    }
    66% {
        transform: translate(20%, -20%) rotate(-240deg);
    }
}

@keyframes mesh-gradient-3 {
    0%, 100% {
        transform: translate(0%, 0%) scale(1) rotate(0deg);
    }
    33% {
        transform: translate(20%, 20%) scale(1.1) rotate(90deg);
    }
    66% {
        transform: translate(-20%, -20%) scale(0.9) rotate(180deg);
    }
}

.mesh-gradient {
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at 20% 80%, rgba(34, 197, 94, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(34, 197, 94, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(34, 197, 94, 0.08) 0%, transparent 50%);
    animation: mesh-gradient 20s ease infinite;
}

.mesh-gradient-2 {
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at 60% 60%, rgba(22, 163, 74, 0.12) 0%, transparent 50%),
                radial-gradient(circle at 90% 10%, rgba(22, 163, 74, 0.08) 0%, transparent 50%);
    animation: mesh-gradient-2 25s ease infinite;
}

.mesh-gradient-3 {
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at 10% 90%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(16, 185, 129, 0.06) 0%, transparent 50%);
    animation: mesh-gradient-3 30s ease infinite;
}

/* Floating particles animation */
@keyframes float-particle {
    0%, 100% {
        transform: translateY(0px) translateX(0px) rotate(0deg);
        opacity: 0.3;
    }
    25% {
        transform: translateY(-20px) translateX(10px) rotate(90deg);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-40px) translateX(-5px) rotate(180deg);
        opacity: 1;
    }
    75% {
        transform: translateY(-20px) translateX(-15px) rotate(270deg);
        opacity: 0.6;
    }
}

.floating-particle {
    animation: float-particle 8s ease-in-out infinite;
}

/* Simple particle animation for login/signup style */
@keyframes particle {
    0%, 100% {
        transform: translateY(0px) translateX(0px);
        opacity: 0.3;
    }
    50% {
        transform: translateY(-10px) translateX(5px);
        opacity: 0.8;
    }
}

.particle {
    animation: particle 4s ease-in-out infinite;
}

/* Animated gradient background */
@keyframes gradient {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

.animate-gradient {
    background-size: 200% 200%;
    animation: gradient 8s ease infinite;
}

/* Advanced card animations */
@keyframes card-glow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(34, 197, 94, 0.1);
    }
    50% {
        box-shadow: 0 0 40px rgba(34, 197, 94, 0.2), 0 0 60px rgba(34, 197, 94, 0.1);
    }
}

@keyframes pulse-border {
    0%, 100% {
        border-color: rgba(34, 197, 94, 0.3);
    }
    50% {
        border-color: rgba(34, 197, 94, 0.6);
    }
}

.app-card {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(17, 17, 17, 0.9) 100%);
    border: 1px solid rgba(34, 197, 94, 0.2);
    backdrop-filter: blur(20px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.app-card:hover {
    transform: translateY(-8px) scale(1.02);
    animation: card-glow 2s ease-in-out infinite;
    border: 1px solid rgba(34, 197, 94, 0.4);
}

.app-card-premium {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(0, 0, 0, 0.9) 50%, rgba(22, 163, 74, 0.1) 100%);
    animation: pulse-border 3s ease-in-out infinite;
}

/* Glassmorphism enhanced */
.glass-advanced {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, rgba(255, 255, 255, 0.01) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Gradient text animations */
@keyframes gradient-text {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

.gradient-text {
    background: linear-gradient(-45deg, #22c55e, #16a34a, #10b981, #22c55e);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradient-text 3s ease infinite;
}

/* Button enhancements */
.btn-advanced {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-advanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-advanced:hover::before {
    left: 100%;
}

.btn-advanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(34, 197, 94, 0.3);
}

/* Infographic styles */
.infographic-node {
    position: relative;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(0, 0, 0, 0.8) 100%);
    border: 2px solid rgba(34, 197, 94, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.infographic-node:hover {
    border-color: rgba(34, 197, 94, 0.6);
    transform: scale(1.05);
    box-shadow: 0 0 30px rgba(34, 197, 94, 0.2);
}

.infographic-connector {
    position: absolute;
    background: linear-gradient(90deg, rgba(34, 197, 94, 0.6), rgba(34, 197, 94, 0.2));
    height: 2px;
    z-index: 1;
}

.infographic-connector::after {
    content: '';
    position: absolute;
    right: -6px;
    top: -4px;
    width: 0;
    height: 0;
    border-left: 8px solid rgba(34, 197, 94, 0.6);
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
}

/* Flow animation */
@keyframes flow {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(100%);
        opacity: 0;
    }
}

.flow-animation {
    position: relative;
    overflow: hidden;
}

.flow-animation::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 20px;
    height: 2px;
    background: linear-gradient(90deg, transparent, #22c55e, transparent);
    animation: flow 2s ease-in-out infinite;
}

/* Screen mockup styles */
.screen-mockup {
    background: linear-gradient(135deg, #000000 0%, #111111 100%);
    border: 8px solid #1a1a1a;
    border-radius: 24px;
    position: relative;
    overflow: hidden;
}

.screen-mockup::before {
    content: '';
    position: absolute;
    top: 8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: #333;
    border-radius: 2px;
}

.screen-content {
    padding: 24px 16px;
    background: linear-gradient(180deg, #000000 0%, #0a0a0a 100%);
    min-height: 400px;
}

/* Advanced hover effects */
.hover-lift-advanced {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift-advanced:hover {
    transform: translateY(-12px) rotateX(5deg);
    box-shadow: 0 20px 40px rgba(34, 197, 94, 0.15);
}

/* Loading animations */
@keyframes shimmer-advanced {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.shimmer-advanced {
    background: linear-gradient(90deg, transparent 25%, rgba(34, 197, 94, 0.1) 50%, transparent 75%);
    background-size: 200% 100%;
    animation: shimmer-advanced 2s infinite;
}

/* Responsive enhancements */
@media (max-width: 768px) {
    .app-card:hover {
        transform: translateY(-4px) scale(1.01);
    }
    
    .hover-lift-advanced:hover {
        transform: translateY(-6px);
    }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #000000;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, #22c55e, #16a34a);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, #16a34a, #15803d);
}

/* Selection color */
::selection {
    background-color: rgba(34, 197, 94, 0.3);
    color: white;
}

/* Focus states */
.focus-visible:focus-visible {
    outline: 2px solid #22c55e;
    outline-offset: 2px;
}

/* Typography enhancements */
.text-glow {
    text-shadow: 0 0 10px rgba(34, 197, 94, 0.5);
}

/* Advanced grid patterns */
.grid-pattern {
    background-image: 
        linear-gradient(rgba(34, 197, 94, 0.03) 1px, transparent 1px),
        linear-gradient(90deg, rgba(34, 197, 94, 0.03) 1px, transparent 1px);
    background-size: 50px 50px;
}

/* Dot pattern */
.dot-pattern {
    background-image: radial-gradient(circle, rgba(34, 197, 94, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
}

/* Hero section enhancements */
.hero-bg {
    background: radial-gradient(ellipse at center, rgba(34, 197, 94, 0.1) 0%, transparent 70%);
}

/* Card stack effect */
.card-stack {
    position: relative;
}

.card-stack::before,
.card-stack::after {
    content: '';
    position: absolute;
    inset: 4px;
    background: rgba(34, 197, 94, 0.05);
    border-radius: inherit;
    z-index: -1;
}

.card-stack::after {
    inset: 8px;
    background: rgba(34, 197, 94, 0.03);
}

/* Magnetic effect */
@keyframes magnetic {
    0%, 100% {
        transform: translate(0, 0);
    }
    25% {
        transform: translate(2px, -2px);
    }
    50% {
        transform: translate(-2px, 2px);
    }
    75% {
        transform: translate(-2px, -2px);
    }
}

.magnetic:hover {
    animation: magnetic 0.3s ease-in-out;
}

/* Progress indicators */
.progress-ring {
    stroke: #22c55e;
    stroke-width: 4;
    fill: transparent;
    stroke-dasharray: 283;
    stroke-dashoffset: 283;
    transition: stroke-dashoffset 0.5s ease-in-out;
}

/* Notification styles */
.notification-dot {
    position: relative;
}

.notification-dot::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: #22c55e;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
    }
    70% {
        transform: scale(1);
        box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
    }
    100% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
    }
}

/* 3D Image Effects */
.image-3d-container {
  transform-style: preserve-3d;
  perspective: 1000px;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-3d-wrapper {
  transform-style: preserve-3d;
  backface-visibility: hidden;
  will-change: transform;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  display: block;
  margin: 0 auto;
}

/* Mobile optimizations for 3D effects */
@media (max-width: 768px) {
  .image-3d-wrapper {
    transform: perspective(300px) rotateX(10deg) rotateZ(-3deg) !important;
  }

  .image-3d-wrapper:hover {
    transform: perspective(300px) rotateX(5deg) rotateY(8deg) rotateZ(-1deg) !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .image-3d-wrapper {
    transform: none !important;
    transition: none !important;
  }

  .image-3d-wrapper:hover {
    transform: none !important;
  }
}

/* Enhanced 3D Card Effects */
.card-3d {
    perspective: 1000px;
    transform-style: preserve-3d;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.card-3d::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 50%, rgba(255,255,255,0.02) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    border-radius: inherit;
}

.card-3d:hover {
    transform: perspective(1000px) rotateX(3deg) rotateY(5deg) translateZ(20px) scale(1.02);
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.card-3d:hover::before {
    opacity: 1;
}

/* 3D Card Variants */
.card-3d-subtle {
    perspective: 800px;
    transform-style: preserve-3d;
    transition: all 0.3s ease;
}

.card-3d-subtle:hover {
    transform: perspective(800px) rotateX(2deg) rotateY(3deg) translateZ(10px) scale(1.01);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.card-3d-strong {
    perspective: 1200px;
    transform-style: preserve-3d;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-3d-strong:hover {
    transform: perspective(1200px) rotateX(5deg) rotateY(8deg) translateZ(30px) scale(1.03);
    box-shadow:
        0 35px 70px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.15);
}

/* Hero Section 3D Card Effect */
.hero-card {
  height: 250px;
  width: 190px;
  background-image: linear-gradient(to top, #210c93, #0d117a, #011160, #010e47, #07052e);
  box-shadow: rgba(0, 0, 0, 0.24) -20px 30px 10px;
  transform-style: preserve-3d;
  transform: perspective(400px) rotateX(60deg) rotateZ(-30deg);
  border-radius: 10px;
  transition: transform 2s;
}

.hero-card:hover {
  transform: rotateY(19deg);
}
