const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const User = require('../models/user');

const router = express.Router();

// Get all staff (admins and staff)
router.get('/staff', async (req, res) => {
  try {
    // Find users with role 'admin' or 'staff'
    const staff = await User.find({ role: { $in: ['admin', 'staff'] } });
    res.json({ staff });
  } catch (err) {
    console.error('Error fetching staff:', err);
    res.status(500).json({ error: 'Failed to fetch staff' });
  }
});


// Signup
router.post('/signup', async (req, res) => {
  try {
    const { firstName, lastName, email, phoneNumber, password, role } = req.body;
    console.log('Signup request received:', req.body); // Log the incoming request body

    // Optional: prevent duplicate email
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      console.log('Signup failed: Email already in use:', email);
      return res.status(400).json({ error: 'Email already in use' });
    }

    // Validate required fields
    if (!firstName || !lastName || !email || !phoneNumber || !password) {
      return res.status(400).json({ error: 'All fields are required.' });
    }
    // Validate email format
    if (!/^[^@\s]+@[^@\s]+\.[^@\s]+$/.test(email)) {
      return res.status(400).json({ error: 'Please enter a valid email address.' });
    }
    // Validate phone number (example: must be 11 digits for Nigeria)
    if (!/^\d{11}$/.test(phoneNumber)) {
      return res.status(400).json({ error: 'Phone number must be 11 digits.' });
    }
    // Validate password length
    if (password.length < 6) {
      return res.status(400).json({ error: 'Password must be at least 6 characters.' });
    }


    // Use the role provided by the frontend if present and valid, otherwise default to 'user'
    const allowedRoles = ['user', 'admin', 'staff'];
    let userRole = 'user';
    if (role && allowedRoles.includes(role)) {
      userRole = role;
    }

    const newUser = new User({
      firstName,
      lastName,
      email,
      phoneNumber,
      password,
      role: userRole,
    });
    await newUser.save();

    // Log the saved user to verify the hash in the database
    const savedUser = await User.findOne({ email });
    console.log('Saved user from database:', savedUser);

    // After saving the user, generate a JWT token and return user info (like login)
    const token = jwt.sign({ id: savedUser._id }, process.env.JWT_SECRET, { expiresIn: '3d' });
    console.log(`User account created: ${savedUser.email} (ID: ${savedUser._id})`);
    res.status(201).json({
      success: true,
      message: 'Signup successful',
      data: {
        token,
        user: {
          id: savedUser._id,
          email: savedUser.email,
          firstName: savedUser.firstName,
          lastName: savedUser.lastName,
          phoneNumber: savedUser.phoneNumber,
          role: savedUser.role
        }
      }
    });
  } catch (err) {
    console.error('Error during signup:', err); // Log any unexpected errors
    res.status(400).json({ error: err.message });
  }
});

// Login
router.post('/login', async (req, res) => {
  try {
    console.log('Login request received:', req.body); // Log the incoming request body

    const { email, password } = req.body;
    // Validate required fields
    if (!email || !password) {
      return res.status(400).json({ success: false, message: 'Email and password are required.' });
    }
    // Validate email format
    if (!/^[^@\s]+@[^@\s]+\.[^@\s]+$/.test(email)) {
      return res.status(400).json({ success: false, message: 'Please enter a valid email address.' });
    }

    // Ensure the password and role fields are fetched from the database
    const user = await User.findOne({ email }).select('+password +role');
    if (!user) {
      console.log('User not found:', email); // Log if user is not found
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    console.log('Stored hash:', user.password); // Log the stored hash
    const match = await bcrypt.compare(password, user.password);
    console.log('Password match result:', match); // Log the result of password comparison

    if (!match) {
      console.log('Invalid credentials for user:', email); // Log invalid credentials
      return res.status(401).json({ success: false, message: 'Incorrect password. Please try again.' });
    }

    const token = jwt.sign({ id: user._id }, process.env.JWT_SECRET, { expiresIn: '3d' });
    console.log('Login successful for user:', email); // Log successful login

    // Log the role during login
    console.log('User role during login:', user.role);

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        token,
        user: {
          id: user._id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          phoneNumber: user.phoneNumber,
          role: user.role // Include the user's role in the response
        }
      }
    });
  } catch (err) {
    console.error('Error during login:', err); // Log any unexpected errors
    res.status(500).json({ success: false, message: err.message });
  }
});

// Enhanced logging for debugging bcrypt comparison
router.post('/test-bcrypt', async (req, res) => {
  try {
    const { plainPassword, hashedPassword } = req.body;
    console.log('Received plainPassword:', plainPassword);
    console.log('Received hashedPassword:', hashedPassword);

    // Check if inputs are valid
    if (!plainPassword || !hashedPassword) {
      console.error('Missing plainPassword or hashedPassword in request body');
      return res.status(400).json({ error: 'Both plainPassword and hashedPassword are required' });
    }

    const match = await bcrypt.compare(plainPassword, hashedPassword);
    console.log('Bcrypt comparison result:', match);

    res.json({ match });
  } catch (err) {
    console.error('Error during bcrypt test:', err);
    res.status(500).json({ error: err.message });
  }
});

// Role validation route
router.post('/validate-role', async (req, res) => {
  try {
    const { role } = req.body;

    if (!role) {
      return res.status(400).json({ error: 'Role is required' });
    }

    // Example logic: Check if the role is valid (you can customize this as needed)
    const validRoles = ['user', 'admin', 'staff'];
    const isValid = validRoles.includes(role);

    res.json({ isValid });
  } catch (err) {
    console.error('Error during role validation:', err);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Route to fetch user information
router.get('/user-info', async (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    // console.log('Authorization header:', authHeader); // Log the Authorization header

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Authorization token is required and must be in the format: Bearer <token>' });
    }

    const token = authHeader.split(' ')[1];

    // Verify the token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const userId = decoded.id;

    // Fetch the user from the database
    const user = await User.findById(userId).select('-password');

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({ user });
  } catch (err) {
    if (err.name === 'JsonWebTokenError') {
      console.error('Invalid JWT:', err.message);
      return res.status(401).json({ error: 'Invalid token' });
    }

    console.error('Error fetching user information:', err);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Route to fetch all users with pagination and search functionality
router.get('/users', async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '' } = req.query;

    // Convert page and limit to integers
    const pageNumber = parseInt(page, 10);
    const limitNumber = parseInt(limit, 10);

    // Build the search query
    const searchQuery = search
      ? {
          $or: [
            { firstName: { $regex: search, $options: 'i' } },
            { lastName: { $regex: search, $options: 'i' } },
            { email: { $regex: search, $options: 'i' } },
          ],
        }
      : {};

    // Fetch users with pagination and search
    const users = await User.find(searchQuery)
      .skip((pageNumber - 1) * limitNumber)
      .limit(limitNumber);

    // Get total count for pagination
    const totalUsers = await User.countDocuments(searchQuery);

    res.json({
      users,
      totalUsers,
      totalPages: Math.ceil(totalUsers / limitNumber),
      currentPage: pageNumber,
    });
  } catch (err) {
    console.error('Error fetching users:', err);
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});

// Route to fetch recent users (for admin dashboard)
router.get('/recent', async (req, res) => {
  try {
    const { limit = 6 } = req.query;
    const users = await User.find()
      .sort({ createdAt: -1 })
      .limit(parseInt(limit))
      .select('firstName lastName email role createdAt');
    res.json(users);
  } catch (err) {
    console.error('Error fetching recent users:', err);
    res.status(500).json({ error: 'Failed to fetch recent users' });
  }
});

// PUT: Update user role (admin only)
router.put('/:id/role', async (req, res) => {
  console.log('PUT /users/:id/role called');
  console.log('Params:', req.params);
  console.log('Body:', req.body);
  try {
    const { id } = req.params;
    const { role } = req.body;
    if (!role || !['user', 'admin'].includes(role)) {
      console.log('Invalid role:', role);
      return res.status(400).json({ error: 'Invalid role' });
    }
    const user = await User.findByIdAndUpdate(id, { role }, { new: true });
    if (!user) {
      console.log('User not found:', id);
      return res.status(404).json({ error: 'User not found' });
    }
    console.log('User role updated:', user);
    res.json({ message: 'User role updated', user });
  } catch (err) {
    console.error('Failed to update user role:', err);
    res.status(500).json({ error: 'Failed to update user role' });
  }
});

module.exports = router;
