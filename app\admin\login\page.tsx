"use client";

import { motion } from "framer-motion";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import {
  FiArrowLeft,
  FiEye,
  FiEyeOff,
  FiLock,
  FiMail,
  FiShield,
} from "react-icons/fi";
import { BetterInterestLogo } from "../../../src/components/ui/BetterInterestLogo";
import { useAuth } from "../../../src/hooks/use-auth";
import { AdminLoginCredentials } from "../../../src/types/auth";

export default function AdminLoginPage() {
  const router = useRouter();
  const { adminLogin, isLoading, error, clearError, isAuthenticated, user } =
    useAuth();

  const [formData, setFormData] = useState<AdminLoginCredentials>({
    email: "",
    password: "",
    adminCode: "",
  });

  const [showPassword, setShowPassword] = useState(false);
  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({});

  // Redirect if already authenticated as admin
  useEffect(() => {
    if (isAuthenticated && user?.role === "ADMIN") {
      router.push("/admin/dashboard");
    } else if (isAuthenticated && user?.role === "USER") {
      router.push("/dashboard");
    }
  }, [isAuthenticated, user, router]);

  // Clear errors when form data changes
  useEffect(() => {
    if (error) clearError();
    setValidationErrors({});
  }, [formData, error, clearError]);

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.email.trim()) {
      errors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = "Email is invalid";
    }

    if (!formData.password) {
      errors.password = "Password is required";
    }

    if (!formData.adminCode?.trim()) {
      errors.adminCode = "Admin code is required";
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    try {
      await adminLogin(formData);
      router.push("/admin/dashboard");
    } catch (error) {
      // Error is handled by the auth context
      console.error("Admin login failed:", error);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  return (
    <div className="min-h-screen bg-black text-white flex">
      {/* Left Side - Form */}
      <div className="flex-1 flex items-center justify-center p-8">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="w-full max-w-md"
        >
          {/* Header */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center mb-4">
              <BetterInterestLogo size="lg" />
              <FiShield className="text-red-400 ml-2" size={24} />
            </div>
            <h1 className="text-2xl font-bold mb-2">Admin Access</h1>
            <p className="text-gray-400">Secure administrative portal</p>
          </div>

          {/* Error Display */}
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-6"
            >
              <p className="text-red-400 text-sm">{error}</p>
            </motion.div>
          )}

          {/* Admin Login Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Email */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Admin Email
              </label>
              <div className="relative">
                <FiMail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-4 py-3 bg-gray-900/50 border border-gray-700 rounded-lg focus:border-red-500 focus:outline-none transition-colors"
                  placeholder="<EMAIL>"
                />
              </div>
              {validationErrors.email && (
                <p className="text-red-400 text-xs mt-1">
                  {validationErrors.email}
                </p>
              )}
            </div>

            {/* Password */}
            <div>
              <label className="block text-sm font-medium mb-2">Password</label>
              <div className="relative">
                <FiLock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type={showPassword ? "text" : "password"}
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-12 py-3 bg-gray-900/50 border border-gray-700 rounded-lg focus:border-red-500 focus:outline-none transition-colors"
                  placeholder="••••••••"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                >
                  {showPassword ? <FiEyeOff /> : <FiEye />}
                </button>
              </div>
              {validationErrors.password && (
                <p className="text-red-400 text-xs mt-1">
                  {validationErrors.password}
                </p>
              )}
            </div>

            {/* Admin Code */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Admin Access Code
              </label>
              <div className="relative">
                <FiShield className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="password"
                  name="adminCode"
                  value={formData.adminCode || ""}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-4 py-3 bg-gray-900/50 border border-gray-700 rounded-lg focus:border-red-500 focus:outline-none transition-colors"
                  placeholder="Enter admin code"
                />
              </div>
              {validationErrors.adminCode && (
                <p className="text-red-400 text-xs mt-1">
                  {validationErrors.adminCode}
                </p>
              )}
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-semibold py-3 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? "Authenticating..." : "Access Admin Panel"}
            </button>
          </form>

          {/* User Login Link */}
          <div className="text-center mt-6">
            <p className="text-gray-400">
              Not an admin?{" "}
              <Link
                href="/auth/login"
                className="text-green-400 hover:text-green-300 font-medium"
              >
                User Login
              </Link>
            </p>
          </div>

          {/* Back to Home */}
          <div className="text-center mt-4">
            <Link
              href="/"
              className="inline-flex items-center text-gray-400 hover:text-white transition-colors"
            >
              <FiArrowLeft className="mr-2" />
              Back to Home
            </Link>
          </div>
        </motion.div>
      </div>

      {/* Right Side - Security Visual */}
      <div className="hidden lg:flex flex-1 bg-gradient-to-br from-red-900/20 to-red-600/10 items-center justify-center p-8">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.2 }}
          className="text-center"
        >
          <div className="w-64 h-64 bg-gradient-to-r from-red-400/20 to-red-600/20 rounded-full flex items-center justify-center mb-8">
            <FiShield size={120} className="text-red-400" />
          </div>
          <h2 className="text-3xl font-bold mb-4">Secure Admin Access</h2>
          <p className="text-gray-300 text-lg max-w-md">
            Administrative portal with enhanced security measures and
            comprehensive platform oversight.
          </p>
        </motion.div>
      </div>
    </div>
  );
}
