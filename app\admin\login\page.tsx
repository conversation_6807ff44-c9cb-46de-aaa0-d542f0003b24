"use client";

import { motion } from "framer-motion";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import {
    FiArrowLeft,
    FiEye,
    FiEyeOff,
    FiLock,
    FiMail,
    FiShield,
} from "react-icons/fi";
import { BackgroundAnimation } from "../../../src/components/ui/BackgroundAnimation";
import { BetterInterestLogo } from "../../../src/components/ui/BetterInterestLogo";
import { Button } from "../../../src/components/ui/Button";
import { useAuth } from "../../../src/hooks/use-auth";
import { AdminLoginCredentials } from "../../../src/types/auth";

export default function AdminLoginPage() {
  const router = useRouter();
  const { adminLogin, isLoading, error, clearError, isAuthenticated, user } =
    useAuth();

  const [formData, setFormData] = useState<AdminLoginCredentials>({
    email: "",
    password: "",
    adminCode: "",
  });

  const [showPassword, setShowPassword] = useState(false);
  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({});

  // Redirect if already authenticated as admin
  useEffect(() => {
    if (isAuthenticated && user?.role === "ADMIN") {
      router.push("/admin/dashboard");
    } else if (isAuthenticated && user?.role === "USER") {
      router.push("/dashboard");
    }
  }, [isAuthenticated, user, router]);

  // Clear errors when form data changes
  useEffect(() => {
    if (error) clearError();
    setValidationErrors({});
  }, [formData, error, clearError]);

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.email.trim()) {
      errors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = "Email is invalid";
    }

    if (!formData.password) {
      errors.password = "Password is required";
    }

    if (!formData.adminCode?.trim()) {
      errors.adminCode = "Admin code is required";
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    try {
      await adminLogin(formData);
      router.push("/admin/dashboard");
    } catch (error) {
      // Error is handled by the auth context
      console.error("Admin login failed:", error);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-green-900 to-black text-white overflow-hidden relative">
      {/* Background Animation */}
      <BackgroundAnimation variant="default" />

      {/* Navigation */}
      <nav className="relative z-50 px-6 py-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <Link href="/" className="flex items-center">
            <BetterInterestLogo size="md" variant="light" />
          </Link>
          <Link
            href="/"
            className="inline-flex items-center text-gray-400 hover:text-white transition-colors"
          >
            <FiArrowLeft className="mr-2" />
            Back to Home
          </Link>
        </div>
      </nav>

      {/* Main Content */}
      <div className="relative z-10 flex items-center justify-center min-h-[calc(100vh-80px)] p-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="w-full max-w-md bg-gray-900/80 backdrop-blur-lg border border-gray-800/50 rounded-2xl p-8 shadow-2xl"
        >
          {/* Header */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center mb-4">
              <BetterInterestLogo size="lg" />
              <FiShield className="text-red-400 ml-2" size={24} />
            </div>
            <h1 className="text-2xl font-bold mb-2">Admin Access</h1>
            <p className="text-gray-400">Secure administrative portal</p>
          </div>

          {/* Error Display */}
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-6"
            >
              <p className="text-red-400 text-sm">{error}</p>
            </motion.div>
          )}

          {/* Admin Login Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Email */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Admin Email
              </label>
              <div className="relative">
                <FiMail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-4 py-3 bg-gray-900/50 border border-gray-700 rounded-lg focus:border-red-500 focus:outline-none transition-colors"
                  placeholder="<EMAIL>"
                />
              </div>
              {validationErrors.email && (
                <p className="text-red-400 text-xs mt-1">
                  {validationErrors.email}
                </p>
              )}
            </div>

            {/* Password */}
            <div>
              <label className="block text-sm font-medium mb-2">Password</label>
              <div className="relative">
                <FiLock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type={showPassword ? "text" : "password"}
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-12 py-3 bg-gray-900/50 border border-gray-700 rounded-lg focus:border-red-500 focus:outline-none transition-colors"
                  placeholder="••••••••"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                >
                  {showPassword ? <FiEyeOff /> : <FiEye />}
                </button>
              </div>
              {validationErrors.password && (
                <p className="text-red-400 text-xs mt-1">
                  {validationErrors.password}
                </p>
              )}
            </div>

            {/* Admin Code */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Admin Access Code
              </label>
              <div className="relative">
                <FiShield className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="password"
                  name="adminCode"
                  value={formData.adminCode || ""}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-4 py-3 bg-gray-900/50 border border-gray-700 rounded-lg focus:border-red-500 focus:outline-none transition-colors"
                  placeholder="Enter admin code"
                />
              </div>
              {validationErrors.adminCode && (
                <p className="text-red-400 text-xs mt-1">
                  {validationErrors.adminCode}
                </p>
              )}
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              disabled={isLoading}
              className="w-full"
              variant="primary"
            >
              {isLoading ? "Authenticating..." : "Access Admin Panel"}
            </Button>
          </form>

          {/* User Login Link */}
          <div className="text-center mt-6">
            <p className="text-gray-400">
              Not an admin?{" "}
              <Link
                href="/auth/login"
                className="text-green-400 hover:text-green-300 font-medium"
              >
                User Login
              </Link>
            </p>
          </div>

          {/* Back to Home */}
          <div className="text-center mt-4">
            <Link
              href="/"
              className="inline-flex items-center text-gray-400 hover:text-white transition-colors"
            >
              <FiArrowLeft className="mr-2" />
              Back to Home
            </Link>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
