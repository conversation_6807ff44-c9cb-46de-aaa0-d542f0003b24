"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '../../src/contexts/ThemeContext';
import { FiMenu, FiX, FiHome, FiSettings } from 'react-icons/fi';

export default function SimpleDashboardPage() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { theme, toggleTheme } = useTheme();

  return (
    <div className={`min-h-screen flex ${
      theme === 'light' ? 'bg-gray-50 text-gray-900' : 'bg-gray-900 text-white'
    }`}>
      {/* Mobile backdrop */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black/50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'} 
        lg:translate-x-0 
        fixed lg:relative 
        inset-y-0 left-0 
        z-50 lg:z-10
        w-80 
        transition-transform duration-300 ease-in-out
        ${theme === 'light' ? 'bg-white border-gray-200' : 'bg-gray-800 border-gray-700'}
        border-r flex flex-col
      `}>
        {/* Sidebar Header */}
        <div className="flex items-center justify-between h-16 px-6 border-b border-gray-700">
          <h1 className="text-xl font-bold">Simple Dashboard</h1>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden p-2 rounded hover:bg-gray-700"
          >
            <FiX className="w-5 h-5" />
          </button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-6 space-y-2">
          <a href="#" className="flex items-center space-x-3 p-3 rounded hover:bg-gray-700">
            <FiHome className="w-5 h-5" />
            <span>Home</span>
          </a>
          <a href="#" className="flex items-center space-x-3 p-3 rounded hover:bg-gray-700">
            <FiSettings className="w-5 h-5" />
            <span>Settings</span>
          </a>
        </nav>

        {/* Theme Toggle */}
        <div className="p-6 border-t border-gray-700">
          <button
            onClick={toggleTheme}
            className="w-full p-3 rounded bg-green-600 hover:bg-green-700 text-white"
          >
            Toggle Theme ({theme})
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <header className={`h-16 px-6 flex items-center justify-between border-b ${
          theme === 'light' ? 'bg-white border-gray-200' : 'bg-gray-800 border-gray-700'
        }`}>
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setSidebarOpen(true)}
              className="lg:hidden p-2 rounded hover:bg-gray-700"
            >
              <FiMenu className="w-5 h-5" />
            </button>
            <h1 className="text-xl font-semibold">Dashboard</h1>
          </div>
        </header>

        {/* Content */}
        <main className="flex-1 p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className={`p-6 rounded-lg ${
              theme === 'light' ? 'bg-white shadow' : 'bg-gray-800'
            }`}>
              <h3 className="text-lg font-semibold mb-2">Card 1</h3>
              <p>This is a test card to verify the layout is working.</p>
            </div>
            <div className={`p-6 rounded-lg ${
              theme === 'light' ? 'bg-white shadow' : 'bg-gray-800'
            }`}>
              <h3 className="text-lg font-semibold mb-2">Card 2</h3>
              <p>The sidebar should be visible on large screens and hidden on mobile.</p>
            </div>
            <div className={`p-6 rounded-lg ${
              theme === 'light' ? 'bg-white shadow' : 'bg-gray-800'
            }`}>
              <h3 className="text-lg font-semibold mb-2">Card 3</h3>
              <p>Theme switching should work properly.</p>
            </div>
          </div>

          <div className="mt-8">
            <h2 className="text-2xl font-bold mb-4">Responsive Test</h2>
            <div className="space-y-4">
              <div className="block lg:hidden p-4 bg-red-500 text-white rounded">
                Mobile Only: This shows only on small screens
              </div>
              <div className="hidden lg:block p-4 bg-green-500 text-white rounded">
                Desktop Only: This shows only on large screens (lg and up)
              </div>
              <div className="p-4 bg-blue-500 text-white rounded">
                Always Visible: This shows on all screen sizes
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
