"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  <PERSON>Bell, 
  <PERSON>X, 
  <PERSON>Check, 
  FiAlertTriangle,
  FiInfo,
  FiDollarSign,
  FiCreditCard,
  FiUsers,
  FiShield,
  FiSettings,
  FiGift,
  FiExternalLink,
  FiCheckCircle
} from 'react-icons/fi';
import { useRealTimeNotifications, RealTimeNotification } from '../../hooks/useRealTimeNotifications';
import { Button } from './Button';
import { Badge } from './Badge';
import { Card } from './Card';

interface RealTimeNotificationsProps {
  className?: string;
  maxVisible?: number;
  showConnectionStatus?: boolean;
}

const notificationIcons = {
  DEPOSIT: FiDollarSign,
  WITHDRAWAL: FiCreditCard,
  CONTRIBUTION: FiUsers,
  SYSTEM: FiSettings,
  SECURITY: FiShield,
  PROMOTIONAL: FiGift
};

const priorityColors = {
  LOW: 'border-gray-600 bg-gray-800',
  MEDIUM: 'border-blue-600 bg-blue-600/10',
  HIGH: 'border-yellow-600 bg-yellow-600/10',
  URGENT: 'border-red-600 bg-red-600/10'
};

const priorityTextColors = {
  LOW: 'text-gray-400',
  MEDIUM: 'text-blue-400',
  HIGH: 'text-yellow-400',
  URGENT: 'text-red-400'
};

export function RealTimeNotifications({ 
  className = '', 
  maxVisible = 5,
  showConnectionStatus = true 
}: RealTimeNotificationsProps) {
  const {
    notifications,
    unreadCount,
    isConnected,
    markAsRead,
    markAllAsRead,
    clearNotification,
    clearAllNotifications
  } = useRealTimeNotifications();

  const [isExpanded, setIsExpanded] = useState(false);
  const [showAll, setShowAll] = useState(false);

  const visibleNotifications = showAll 
    ? notifications 
    : notifications.slice(0, maxVisible);

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInSeconds = Math.floor((now.getTime() - time.getTime()) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    return `${Math.floor(diffInSeconds / 86400)}d ago`;
  };

  const handleNotificationClick = (notification: RealTimeNotification) => {
    if (!notification.read) {
      markAsRead(notification.id);
    }

    if (notification.actionUrl) {
      window.open(notification.actionUrl, '_blank');
    }
  };

  return (
    <div className={`relative ${className}`}>
      {/* Notification Bell */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsExpanded(!isExpanded)}
        className="relative"
      >
        <FiBell className="w-5 h-5" />
        {unreadCount > 0 && (
          <Badge 
            variant="error" 
            className="absolute -top-2 -right-2 w-5 h-5 text-xs flex items-center justify-center"
          >
            {unreadCount > 99 ? '99+' : unreadCount}
          </Badge>
        )}
      </Button>

      {/* Connection Status Indicator */}
      {showConnectionStatus && (
        <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full ${
          isConnected ? 'bg-green-500' : 'bg-red-500'
        }`} title={isConnected ? 'Connected' : 'Disconnected'} />
      )}

      {/* Notifications Panel */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            className="absolute right-0 top-full mt-2 w-96 max-h-96 z-50"
          >
            <Card className="bg-gray-800 border-gray-700 shadow-xl">
              {/* Header */}
              <div className="p-4 border-b border-gray-700">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <FiBell className="text-gray-400" />
                    <h3 className="font-semibold text-white">Notifications</h3>
                    {unreadCount > 0 && (
                      <Badge variant="info">{unreadCount} new</Badge>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    {notifications.length > 0 && (
                      <>
                        {unreadCount > 0 && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={markAllAsRead}
                          >
                            <FiCheckCircle className="w-4 h-4 mr-1" />
                            Mark all read
                          </Button>
                        )}
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={clearAllNotifications}
                        >
                          Clear all
                        </Button>
                      </>
                    )}
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setIsExpanded(false)}
                    >
                      <FiX className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                {/* Connection Status */}
                <div className="flex items-center space-x-2 mt-2">
                  <div className={`w-2 h-2 rounded-full ${
                    isConnected ? 'bg-green-500' : 'bg-red-500'
                  }`} />
                  <span className="text-xs text-gray-400">
                    {isConnected ? 'Real-time updates active' : 'Reconnecting...'}
                  </span>
                </div>
              </div>

              {/* Notifications List */}
              <div className="max-h-80 overflow-y-auto">
                {notifications.length === 0 ? (
                  <div className="p-8 text-center">
                    <FiBell className="mx-auto text-4xl text-gray-600 mb-2" />
                    <p className="text-gray-400">No notifications yet</p>
                    <p className="text-sm text-gray-500 mt-1">
                      You'll see real-time updates here
                    </p>
                  </div>
                ) : (
                  <div className="p-2">
                    <AnimatePresence>
                      {visibleNotifications.map((notification) => {
                        const Icon = notificationIcons[notification.type] || FiInfo;
                        
                        return (
                          <motion.div
                            key={notification.id}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            exit={{ opacity: 0, x: 20 }}
                            className={`p-3 mb-2 rounded-lg border cursor-pointer transition-colors hover:bg-gray-700/50 ${
                              priorityColors[notification.priority]
                            } ${!notification.read ? 'border-l-4 border-l-green-500' : ''}`}
                            onClick={() => handleNotificationClick(notification)}
                          >
                            <div className="flex items-start space-x-3">
                              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                                notification.priority === 'URGENT' ? 'bg-red-600/20' :
                                notification.priority === 'HIGH' ? 'bg-yellow-600/20' :
                                notification.priority === 'MEDIUM' ? 'bg-blue-600/20' :
                                'bg-gray-600/20'
                              }`}>
                                <Icon className={`w-4 h-4 ${priorityTextColors[notification.priority]}`} />
                              </div>
                              
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center justify-between">
                                  <h4 className={`font-medium text-sm ${
                                    notification.read ? 'text-gray-400' : 'text-white'
                                  }`}>
                                    {notification.title}
                                  </h4>
                                  <div className="flex items-center space-x-2">
                                    {notification.actionRequired && (
                                      <FiExternalLink className="w-3 h-3 text-gray-400" />
                                    )}
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        clearNotification(notification.id);
                                      }}
                                      className="w-6 h-6 p-0"
                                    >
                                      <FiX className="w-3 h-3" />
                                    </Button>
                                  </div>
                                </div>
                                
                                <p className={`text-sm mt-1 ${
                                  notification.read ? 'text-gray-500' : 'text-gray-300'
                                }`}>
                                  {notification.message}
                                </p>
                                
                                <div className="flex items-center justify-between mt-2">
                                  <span className="text-xs text-gray-500">
                                    {formatTimeAgo(notification.timestamp)}
                                  </span>
                                  
                                  <div className="flex items-center space-x-2">
                                    <Badge 
                                      variant={
                                        notification.priority === 'URGENT' ? 'error' :
                                        notification.priority === 'HIGH' ? 'warning' :
                                        notification.priority === 'MEDIUM' ? 'info' :
                                        'secondary'
                                      }
                                      className="text-xs"
                                    >
                                      {notification.priority}
                                    </Badge>
                                    
                                    {notification.actionRequired && (
                                      <Badge variant="warning" className="text-xs">
                                        Action Required
                                      </Badge>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </motion.div>
                        );
                      })}
                    </AnimatePresence>

                    {/* Show More/Less Button */}
                    {notifications.length > maxVisible && (
                      <div className="text-center pt-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setShowAll(!showAll)}
                        >
                          {showAll 
                            ? `Show less` 
                            : `Show ${notifications.length - maxVisible} more`
                          }
                        </Button>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

// Floating notification component for urgent notifications
export function FloatingNotification({ 
  notification, 
  onClose, 
  onAction 
}: {
  notification: RealTimeNotification;
  onClose: () => void;
  onAction?: () => void;
}) {
  const Icon = notificationIcons[notification.type] || FiInfo;

  return (
    <motion.div
      initial={{ opacity: 0, y: -50, scale: 0.9 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -50, scale: 0.9 }}
      className="fixed top-4 right-4 z-50 w-96"
    >
      <Card className={`p-4 border-l-4 ${
        notification.priority === 'URGENT' ? 'border-l-red-500 bg-red-600/10' :
        notification.priority === 'HIGH' ? 'border-l-yellow-500 bg-yellow-600/10' :
        'border-l-blue-500 bg-blue-600/10'
      } bg-gray-800 border-gray-700 shadow-xl`}>
        <div className="flex items-start space-x-3">
          <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
            notification.priority === 'URGENT' ? 'bg-red-600/20' :
            notification.priority === 'HIGH' ? 'bg-yellow-600/20' :
            'bg-blue-600/20'
          }`}>
            <Icon className={`w-5 h-5 ${
              notification.priority === 'URGENT' ? 'text-red-400' :
              notification.priority === 'HIGH' ? 'text-yellow-400' :
              'text-blue-400'
            }`} />
          </div>
          
          <div className="flex-1">
            <h4 className="font-semibold text-white">{notification.title}</h4>
            <p className="text-gray-300 text-sm mt-1">{notification.message}</p>
            
            {notification.actionRequired && onAction && (
              <div className="flex space-x-2 mt-3">
                <Button
                  size="sm"
                  onClick={onAction}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {notification.actionText || 'Take Action'}
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={onClose}
                >
                  Dismiss
                </Button>
              </div>
            )}
          </div>
          
          <Button
            size="sm"
            variant="outline"
            onClick={onClose}
            className="w-8 h-8 p-0"
          >
            <FiX className="w-4 h-4" />
          </Button>
        </div>
      </Card>
    </motion.div>
  );
}
