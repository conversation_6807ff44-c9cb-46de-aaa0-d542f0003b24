"use client";

import { ArrowR<PERSON>, ChevronDown } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

export const HeroSection = () => {
  return (
    <section
      className="relative min-h-screen flex items-center justify-center px-4 pt-16"
      data-oid="3u_gl6l"
    >
      <div
        className="absolute inset-0 bg-gradient-to-br from-green-900/20 via-transparent to-black/40"
        data-oid="9c6pg2a"
      ></div>
      <div
        className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(34,197,94,0.3),transparent_50%)]"
        data-oid="bcdd25w"
      ></div>
      <div
        className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(34,197,94,0.2),transparent_50%)]"
        data-oid="ho.q9br"
      ></div>

      <div
        className="relative z-10 text-center max-w-5xl mx-auto"
        data-oid="grjn.zl"
      >
        <div className="animate-fade-in-up" data-oid="4kkpn05">
          <h1
            className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-white via-green-100 to-green-400 bg-clip-text text-transparent leading-tight"
            data-oid=".djmltw"
          >
            Save Smarter,
            <br data-oid="vabe_xq" />
            <span
              className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"
              data-oid="z:1qwuu"
            >
              Grow Faster
            </span>
          </h1>
          <p
            className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed"
            data-oid="sca4ceg"
          >
            Transform your financial future with Koja Save's intelligent savings
            platform. Automated savings, high-yield returns, and smart goal
            tracking - all in one beautiful app.
          </p>
          <div
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
            data-oid="zovv_74"
          >
            <Button
              size="lg"
              className="group flex items-center space-x-2"
              data-oid="2oe_3bp"
            >
              <span data-oid="y-xcwej">Start Saving Today</span>
              <ArrowRight
                className="w-5 h-5 group-hover:translate-x-1 transition-transform"
                data-oid="as-susd"
              />
            </Button>
            <Button variant="outline" size="lg" data-oid="v0df4bl">
              Watch Demo
            </Button>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce"
          data-oid="e7ug.:r"
        >
          <ChevronDown className="w-8 h-8 text-green-400" data-oid="8jeiw4m" />
        </div>
      </div>
    </section>
  );
};
