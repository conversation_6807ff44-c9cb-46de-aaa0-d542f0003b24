"use client";

import { useState, useEffect } from "react";
import { Menu, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import KojaSaveLogo from "../KojaSaveLogo";

export const Navigation = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <nav
      className={`fixed top-0 w-full z-50 transition-all duration-300 ${
        isScrolled
          ? "bg-black/80 backdrop-blur-md border-b border-green-500/20"
          : "bg-transparent"
      }`}
      data-oid="5-xj6zh"
    >
      <div
        className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"
        data-oid="jm9bx3u"
      >
        <div
          className="flex justify-between items-center h-16"
          data-oid="ukyadl:"
        >
          <div className="flex items-center" data-oid="k6_z4zg">
            <KojaSaveLogo
              size={32}
              showText={true}
              textClassName="text-xl bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"
            />
          </div>

          {/* Desktop Menu */}
          <div
            className="hidden md:flex items-center space-x-8"
            data-oid="bi0fsam"
          >
            <a
              href="#features"
              className="text-gray-300 hover:text-green-400 transition-colors"
              data-oid="rwcf6sw"
            >
              Features
            </a>
            <a
              href="#testimonials"
              className="text-gray-300 hover:text-green-400 transition-colors"
              data-oid="5y0fg1g"
            >
              Testimonials
            </a>
            <a
              href="#contact"
              className="text-gray-300 hover:text-green-400 transition-colors"
              data-oid="6pdw:p."
            >
              Contact
            </a>
            <Button size="sm" data-oid="0qy-ei5">
              Login
            </Button>
            <Button variant="outline" size="sm" data-oid="zv.sfvf">
              Sign Up
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            data-oid="9:a_30l"
          >
            {isMenuOpen ? (
              <X className="w-6 h-6" data-oid="wo-pa1l" />
            ) : (
              <Menu className="w-6 h-6" data-oid="ehfpbqz" />
            )}
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div
          className="md:hidden bg-black/95 backdrop-blur-md border-t border-green-500/20"
          data-oid="y_:h6v5"
        >
          <div className="px-4 py-4 space-y-4" data-oid="2en-kbq">
            <a
              href="#features"
              className="block text-gray-300 hover:text-green-400 transition-colors"
              data-oid="u5wwywi"
            >
              Features
            </a>
            <a
              href="#testimonials"
              className="block text-gray-300 hover:text-green-400 transition-colors"
              data-oid="7yapj1r"
            >
              Testimonials
            </a>
            <a
              href="#contact"
              className="block text-gray-300 hover:text-green-400 transition-colors"
              data-oid="dtuaq2s"
            >
              Contact
            </a>
            <Button className="w-full" data-oid="wz1m7tz">
              Login
            </Button>
            <Button variant="outline" className="w-full" data-oid="geekyza">
              Sign Up
            </Button>
          </div>
        </div>
      )}
    </nav>
  );
};
