"use client";

import { motion } from "framer-motion";
import Link from "next/link";
import { ReactNode } from "react";

interface EnhancedHeroProps {
  icon: ReactNode;
  title: string;
  highlightText: string;
  description: string;
  primaryButton?: {
    text: string;
    href: string;
  };
  secondaryButton?: {
    text: string;
    href: string;
  };
  stats?: Array<{
    value: string;
    label: string;
  }>;
  features?: Array<{
    icon: ReactNode;
    label: string;
  }>;
  className?: string;
}

export default function EnhancedHero({
  icon,
  title,
  highlightText,
  description,
  primaryButton,
  secondaryButton,
  stats,
  features,
  className = ""
}: EnhancedHeroProps) {
  return (
    <section className={`relative z-10 px-6 py-32 hero-bg ${className}`}>
      <div className="max-w-7xl mx-auto text-center">
        {/* Icon */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8 }}
          className="mb-8"
        >
          <div className="w-24 h-24 bg-gradient-to-r from-green-400 to-green-600 rounded-3xl flex items-center justify-center mx-auto mb-6 card-glow">
            {icon}
          </div>
        </motion.div>

        {/* Title */}
        <motion.h1
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="text-5xl md:text-7xl font-bold mb-6 text-white text-glow"
        >
          {title}{" "}
          <span className="gradient-text">
            {highlightText}
          </span>
        </motion.h1>

        {/* Description */}
        <motion.p
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto mb-12"
        >
          {description}
        </motion.p>

        {/* Buttons */}
        {(primaryButton || secondaryButton) && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16"
          >
            {primaryButton && (
              <Link
                href={primaryButton.href}
                className="btn-advanced px-8 py-4 rounded-lg text-white font-semibold hover-lift"
              >
                {primaryButton.text}
              </Link>
            )}
            {secondaryButton && (
              <Link
                href={secondaryButton.href}
                className="px-8 py-4 border border-green-400 rounded-lg text-white hover:bg-green-400 hover:text-black transition-all hover-lift magnetic"
              >
                {secondaryButton.text}
              </Link>
            )}
          </motion.div>
        )}

        {/* Features */}
        {features && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto mb-12"
          >
            {features.map((item, i) => (
              <div key={i} className="glass-advanced rounded-xl p-4 text-center hover-lift-advanced">
                <div className="text-green-400 text-2xl mb-2">{item.icon}</div>
                <div className="text-sm text-gray-300">{item.label}</div>
              </div>
            ))}
          </motion.div>
        )}

        {/* Stats */}
        {stats && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.0 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-2xl mx-auto"
          >
            {stats.map((stat, i) => (
              <div key={i} className="text-center">
                <div className="text-3xl font-bold text-green-400 mb-1">{stat.value}</div>
                <div className="text-sm text-gray-400">{stat.label}</div>
              </div>
            ))}
          </motion.div>
        )}
      </div>
    </section>
  );
}
