"use client";

import React from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { FiArrowLeft, FiTarget, FiDollarSign, FiUsers } from 'react-icons/fi';
import KojaSaveLogo from '../../components/KojaSaveLogo';

export default function TestSavingsPage() {
  return (
    <div className="min-h-screen bg-black text-white p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <KojaSaveLogo size={40} showText={true} />
            <div>
              <h1 className="text-2xl font-bold">Savings Platform Test</h1>
              <p className="text-gray-400">Test the savings functionality</p>
            </div>
          </div>
          <Link
            href="/"
            className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors"
          >
            <FiArrowLeft />
            <span>Back to Home</span>
          </Link>
        </div>

        {/* Test Links */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gray-900/50 border border-gray-800 rounded-lg p-6"
          >
            <FiTarget className="text-green-400 text-3xl mb-4" />
            <h3 className="text-lg font-semibold mb-2">User Dashboard</h3>
            <p className="text-gray-400 text-sm mb-4">
              Test the user dashboard with savings overview and quick actions
            </p>
            <Link
              href="/user/dashboard"
              className="inline-block bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg text-sm transition-colors"
            >
              View Dashboard
            </Link>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-gray-900/50 border border-gray-800 rounded-lg p-6"
          >
            <FiDollarSign className="text-blue-400 text-3xl mb-4" />
            <h3 className="text-lg font-semibold mb-2">Savings Plans</h3>
            <p className="text-gray-400 text-sm mb-4">
              Create and manage individual savings plans with goals and targets
            </p>
            <Link
              href="/user/savings-plans"
              className="inline-block bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-sm transition-colors"
            >
              Manage Plans
            </Link>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-gray-900/50 border border-gray-800 rounded-lg p-6"
          >
            <FiUsers className="text-purple-400 text-3xl mb-4" />
            <h3 className="text-lg font-semibold mb-2">Authentication</h3>
            <p className="text-gray-400 text-sm mb-4">
              Test user signup, login, and admin authentication
            </p>
            <div className="space-y-2">
              <Link
                href="/signup"
                className="block bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded-lg text-sm transition-colors text-center"
              >
                User Signup
              </Link>
              <Link
                href="/login"
                className="block bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded-lg text-sm transition-colors text-center"
              >
                User Login
              </Link>
              <Link
                href="/admin-login"
                className="block bg-red-600 hover:bg-red-700 px-4 py-2 rounded-lg text-sm transition-colors text-center"
              >
                Admin Login
              </Link>
            </div>
          </motion.div>
        </div>

        {/* Implementation Status */}
        <div className="mt-12 bg-gray-900/50 border border-gray-800 rounded-lg p-6">
          <h2 className="text-xl font-bold mb-4">Implementation Status</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-green-400 font-semibold mb-2">✅ Completed Features</h3>
              <ul className="space-y-1 text-sm text-gray-300">
                <li>• JWT-based Authentication System</li>
                <li>• User Signup with Validation</li>
                <li>• Admin Login Interface</li>
                <li>• Private Route Protection</li>
                <li>• User Dashboard with Stats</li>
                <li>• Savings Plans Management</li>
                <li>• Savings Plan Creation Form</li>
                <li>• Savings Goal Modal</li>
                <li>• Target Savings Calculator</li>
                <li>• User Navigation & Layout</li>
                <li>• Interest Calculation Service</li>
                <li>• Responsive Design</li>
              </ul>
            </div>
            <div>
              <h3 className="text-yellow-400 font-semibold mb-2">🚧 Next Features</h3>
              <ul className="space-y-1 text-sm text-gray-300">
                <li>• Group Savings System</li>
                <li>• Rotational Savings</li>
                <li>• Payment Integration</li>
                <li>• KYC Verification</li>
                <li>• Analytics Dashboard</li>
                <li>• Notification System</li>
                <li>• Admin Management</li>
                <li>• Settings & Profile</li>
                <li>• Transaction History</li>
                <li>• Real-time Updates</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Technical Notes */}
        <div className="mt-8 bg-blue-500/10 border border-blue-500/20 rounded-lg p-6">
          <h3 className="text-blue-400 font-semibold mb-2">Technical Implementation</h3>
          <div className="text-sm text-gray-300 space-y-2">
            <p>
              <strong>Frontend:</strong> Next.js 14 with TypeScript, Framer Motion animations, Tailwind CSS styling
            </p>
            <p>
              <strong>Authentication:</strong> JWT tokens with refresh mechanism, role-based access control
            </p>
            <p>
              <strong>State Management:</strong> React Context API for global auth state
            </p>
            <p>
              <strong>Forms:</strong> Comprehensive validation with real-time error handling
            </p>
            <p>
              <strong>API Integration:</strong> Service layer architecture with error handling
            </p>
            <p>
              <strong>UI/UX:</strong> Professional design with Koja Save branding and responsive layout
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
