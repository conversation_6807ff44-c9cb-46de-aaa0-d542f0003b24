"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '../../src/components/ui/Button';
import { Card3D } from '../../src/components/ui/Card3D';
import { BetterInterestLogo } from '../../src/components/ui/BetterInterestLogo';
import { BackgroundAnimation } from '../../src/components/ui/BackgroundAnimation';
import { 
  FiMail, 
  FiLock, 
  FiEye, 
  FiEyeOff, 
  FiArrowRight,
  FiUser,
  FiPhone,
  FiShield,
  FiTrendingUp,
  FiUsers,
  FiCheck
} from 'react-icons/fi';
import { FaGoogle, FaFacebook, FaApple } from 'react-icons/fa';

export default function SignupPage() {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [agreedToTerms, setAgreedToTerms] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!agreedToTerms) {
      alert('Please agree to the terms and conditions');
      return;
    }
    
    setIsLoading(true);
    
    // Simulate signup process
    setTimeout(() => {
      setIsLoading(false);
      // Redirect to dashboard
      window.location.href = '/dashboard';
    }, 2000);
  };

  return (
    <div className="min-h-screen bg-theme text-theme relative overflow-hidden">
      {/* Background Animation */}
      <BackgroundAnimation variant="subtle" />
      
      <div className="relative z-10 min-h-screen flex">
        {/* Left Side - Signup Form */}
        <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="w-full max-w-md"
          >
            {/* Logo */}
            <div className="text-center mb-8">
              <BetterInterestLogo size="lg" showText showIcon className="mx-auto mb-4" />
              <h1 className="text-3xl font-bold text-theme font-inter mb-2">
                Create Account
              </h1>
              <p className="text-theme-secondary font-inter">
                Join BetterInterest and start earning better returns
              </p>
            </div>

            {/* Signup Form */}
            <Card3D elevation={2} className="p-8">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Name Fields */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="firstName" className="block text-sm font-medium text-theme mb-2 font-inter">
                      First Name
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <FiUser className="h-5 w-5 text-theme-secondary" />
                      </div>
                      <input
                        id="firstName"
                        name="firstName"
                        type="text"
                        value={formData.firstName}
                        onChange={handleInputChange}
                        className="block w-full pl-10 pr-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme placeholder-theme-secondary focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand transition-colors font-inter"
                        placeholder="First name"
                        required
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="lastName" className="block text-sm font-medium text-theme mb-2 font-inter">
                      Last Name
                    </label>
                    <input
                      id="lastName"
                      name="lastName"
                      type="text"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      className="block w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme placeholder-theme-secondary focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand transition-colors font-inter"
                      placeholder="Last name"
                      required
                    />
                  </div>
                </div>

                {/* Email Field */}
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-theme mb-2 font-inter">
                    Email Address
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <FiMail className="h-5 w-5 text-theme-secondary" />
                    </div>
                    <input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="block w-full pl-10 pr-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme placeholder-theme-secondary focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand transition-colors font-inter"
                      placeholder="Enter your email"
                      required
                    />
                  </div>
                </div>

                {/* Phone Field */}
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-theme mb-2 font-inter">
                    Phone Number
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <FiPhone className="h-5 w-5 text-theme-secondary" />
                    </div>
                    <input
                      id="phone"
                      name="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="block w-full pl-10 pr-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme placeholder-theme-secondary focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand transition-colors font-inter"
                      placeholder="+234 ************"
                      required
                    />
                  </div>
                </div>

                {/* Password Fields */}
                <div className="grid grid-cols-1 gap-4">
                  <div>
                    <label htmlFor="password" className="block text-sm font-medium text-theme mb-2 font-inter">
                      Password
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <FiLock className="h-5 w-5 text-theme-secondary" />
                      </div>
                      <input
                        id="password"
                        name="password"
                        type={showPassword ? 'text' : 'password'}
                        value={formData.password}
                        onChange={handleInputChange}
                        className="block w-full pl-10 pr-12 py-3 border border-theme rounded-lg bg-theme-secondary text-theme placeholder-theme-secondary focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand transition-colors font-inter"
                        placeholder="Create password"
                        required
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                      >
                        {showPassword ? (
                          <FiEyeOff className="h-5 w-5 text-theme-secondary hover:text-brand transition-colors" />
                        ) : (
                          <FiEye className="h-5 w-5 text-theme-secondary hover:text-brand transition-colors" />
                        )}
                      </button>
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="confirmPassword" className="block text-sm font-medium text-theme mb-2 font-inter">
                      Confirm Password
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <FiLock className="h-5 w-5 text-theme-secondary" />
                      </div>
                      <input
                        id="confirmPassword"
                        name="confirmPassword"
                        type={showConfirmPassword ? 'text' : 'password'}
                        value={formData.confirmPassword}
                        onChange={handleInputChange}
                        className="block w-full pl-10 pr-12 py-3 border border-theme rounded-lg bg-theme-secondary text-theme placeholder-theme-secondary focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand transition-colors font-inter"
                        placeholder="Confirm password"
                        required
                      />
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                      >
                        {showConfirmPassword ? (
                          <FiEyeOff className="h-5 w-5 text-theme-secondary hover:text-brand transition-colors" />
                        ) : (
                          <FiEye className="h-5 w-5 text-theme-secondary hover:text-brand transition-colors" />
                        )}
                      </button>
                    </div>
                  </div>
                </div>

                {/* Terms Agreement */}
                <div className="flex items-start">
                  <div className="flex items-center h-5">
                    <input
                      id="terms"
                      type="checkbox"
                      checked={agreedToTerms}
                      onChange={(e) => setAgreedToTerms(e.target.checked)}
                      className="h-4 w-4 text-brand focus:ring-brand border-theme-secondary rounded"
                      required
                    />
                  </div>
                  <div className="ml-3 text-sm">
                    <label htmlFor="terms" className="text-theme-secondary font-inter">
                      I agree to the{' '}
                      <Link href="/terms" className="text-brand hover:text-brand-dark">
                        Terms of Service
                      </Link>{' '}
                      and{' '}
                      <Link href="/privacy" className="text-brand hover:text-brand-dark">
                        Privacy Policy
                      </Link>
                    </label>
                  </div>
                </div>

                {/* Signup Button */}
                <Button
                  type="submit"
                  fullWidth
                  size="lg"
                  loading={isLoading}
                  rightIcon={FiArrowRight}
                  className="font-inter"
                >
                  {isLoading ? 'Creating Account...' : 'Create Account'}
                </Button>
              </form>

              {/* Divider */}
              <div className="mt-6">
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-theme-secondary" />
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-theme text-theme-secondary font-inter">Or continue with</span>
                  </div>
                </div>
              </div>

              {/* Social Signup */}
              <div className="mt-6 grid grid-cols-3 gap-3">
                <button className="w-full inline-flex justify-center py-3 px-4 border border-theme-secondary rounded-lg bg-theme-secondary hover:bg-theme transition-colors">
                  <FaGoogle className="h-5 w-5 text-red-500" />
                </button>
                <button className="w-full inline-flex justify-center py-3 px-4 border border-theme-secondary rounded-lg bg-theme-secondary hover:bg-theme transition-colors">
                  <FaFacebook className="h-5 w-5 text-blue-500" />
                </button>
                <button className="w-full inline-flex justify-center py-3 px-4 border border-theme-secondary rounded-lg bg-theme-secondary hover:bg-theme transition-colors">
                  <FaApple className="h-5 w-5 text-theme" />
                </button>
              </div>

              {/* Login Link */}
              <div className="mt-6 text-center">
                <p className="text-sm text-theme-secondary font-inter">
                  Already have an account?{' '}
                  <Link
                    href="/login"
                    className="text-brand hover:text-brand-dark font-medium transition-colors"
                  >
                    Sign in
                  </Link>
                </p>
              </div>
            </Card3D>
          </motion.div>
        </div>

        {/* Right Side - Hero Image & Benefits */}
        <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-brand/10 to-brand-dark/10 items-center justify-center p-8">
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="max-w-lg text-center"
          >
            {/* Hero Image */}
            <div className="mb-8">
              <Image
                src="/images/nneww.png"
                alt="BetterInterest Financial Success"
                width={400}
                height={300}
                className="mx-auto rounded-2xl shadow-2xl"
                priority
              />
            </div>

            {/* Benefits */}
            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-theme font-inter mb-6">
                Start your financial journey today
              </h2>
              
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="p-2 rounded-lg bg-brand/20">
                    <FiCheck className="w-5 h-5 text-brand" />
                  </div>
                  <div className="text-left">
                    <h3 className="font-semibold text-theme font-inter">Free to Join</h3>
                    <p className="text-sm text-theme-secondary">No hidden fees or monthly charges</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="p-2 rounded-lg bg-brand/20">
                    <FiTrendingUp className="w-5 h-5 text-brand" />
                  </div>
                  <div className="text-left">
                    <h3 className="font-semibold text-theme font-inter">Higher Interest Rates</h3>
                    <p className="text-sm text-theme-secondary">Earn up to 15% annual interest on savings</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="p-2 rounded-lg bg-brand/20">
                    <FiShield className="w-5 h-5 text-brand" />
                  </div>
                  <div className="text-left">
                    <h3 className="font-semibold text-theme font-inter">Secure & Insured</h3>
                    <p className="text-sm text-theme-secondary">FDIC insured up to ₦250,000</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="p-2 rounded-lg bg-brand/20">
                    <FiUsers className="w-5 h-5 text-brand" />
                  </div>
                  <div className="text-left">
                    <h3 className="font-semibold text-theme font-inter">Trusted Community</h3>
                    <p className="text-sm text-theme-secondary">Join 50,000+ satisfied users</p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
