"use client";

import React from 'react';
import { motion } from 'framer-motion';

interface BackgroundAnimationProps {
  variant?: 'default' | 'auth' | 'dashboard';
  className?: string;
}

export function BackgroundAnimation({ 
  variant = 'default', 
  className = '' 
}: BackgroundAnimationProps) {
  const variants = {
    default: {
      gradient: 'bg-gradient-to-br from-gray-900 via-green-900 to-black',
      particles: 30,
      colors: ['bg-green-400', 'bg-orange-400', 'bg-yellow-400']
    },
    auth: {
      gradient: 'bg-gradient-to-br from-gray-900 via-black to-gray-900',
      particles: 20,
      colors: ['bg-green-500', 'bg-orange-500', 'bg-yellow-500']
    },
    dashboard: {
      gradient: 'bg-gradient-to-br from-gray-900 via-gray-800 to-black',
      particles: 15,
      colors: ['bg-green-400', 'bg-orange-400', 'bg-yellow-400']
    }
  };

  const config = variants[variant];

  return (
    <div className={`fixed inset-0 z-0 ${className}`}>
      {/* Main gradient background */}
      <div className={`absolute inset-0 ${config.gradient} animate-gradient`}></div>
      
      {/* Animated blob shapes */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          className="absolute -top-40 -right-40 w-80 h-80 bg-orange-500 rounded-full mix-blend-multiply filter blur-xl opacity-20"
          animate={{
            x: [0, 30, -20, 0],
            y: [0, -50, 20, 0],
            scale: [1, 1.1, 0.9, 1]
          }}
          transition={{
            duration: 7,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute -bottom-40 -left-40 w-80 h-80 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl opacity-20"
          animate={{
            x: [0, -30, 20, 0],
            y: [0, 50, -20, 0],
            scale: [1, 0.9, 1.1, 1]
          }}
          transition={{
            duration: 7,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
        />
        <motion.div
          className="absolute top-40 left-40 w-80 h-80 bg-amber-500 rounded-full mix-blend-multiply filter blur-xl opacity-20"
          animate={{
            x: [0, 20, -30, 0],
            y: [0, -30, 40, 0],
            scale: [1, 1.2, 0.8, 1]
          }}
          transition={{
            duration: 7,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 4
          }}
        />
      </div>

      {/* Floating particles */}
      <div className="absolute inset-0 opacity-20">
        {[...Array(config.particles)].map((_, i) => (
          <motion.div
            key={i}
            className={`absolute w-1 h-1 ${config.colors[i % config.colors.length]} rounded-full`}
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -20, 0],
              opacity: [0.2, 0.8, 0.2],
              scale: [1, 1.5, 1]
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
              ease: "easeInOut"
            }}
          />
        ))}
      </div>

      {/* Gradient overlay for depth */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-black/30"></div>
    </div>
  );
}

// Specialized background variants
export function DefaultBackground(props: Omit<BackgroundAnimationProps, 'variant'>) {
  return <BackgroundAnimation {...props} variant="default" />;
}

export function AuthBackground(props: Omit<BackgroundAnimationProps, 'variant'>) {
  return <BackgroundAnimation {...props} variant="auth" />;
}

export function DashboardBackground(props: Omit<BackgroundAnimationProps, 'variant'>) {
  return <BackgroundAnimation {...props} variant="dashboard" />;
}

export default BackgroundAnimation;
