"use client";

// Removed auth imports for demo mode
// ...existing code...
import React, { useEffect, useState } from "react";
import { CardImage3D, TestimonialImage3D } from "../../src/components/ui/Image3D";
import Image from "next/image";
import {
  FiDollarSign,
  FiTrendingUp,
  FiUsers,
  FiTarget,
  FiPlus,
  FiCreditCard,
  FiPieChart,
  FiUserCheck
} from "react-icons/fi";
import { useKYCEnforcement } from "../../src/hooks/use-kyc-enforcement";
import { KYCIncompleteBanner } from "../../src/components/KYCIncompleteBanner";
import { userService, savingsService, groupSavingsService, transactionsService } from "../../src/services";
import { UserProfile } from "../../src/types/user";
import { SavingsPlan } from "../../src/types/savings";
import { GroupSavings } from "../../src/types/groupSavings";
import { Transaction } from "../../src/types/transactions";
import DashboardLayout from "../../src/components/dashboard/DashboardLayout";
import { StatCard, ActionCard, ProgressCard } from "../../src/components/dashboard/DashboardCard";
import { SavingsLineChart } from "../../src/components/charts/SavingsChart";
import { LoadingLogo } from "../../components/LoadingLogo";
// Type for savings summary from /api/savings/summary
type SavingsSummary = {
  userId: string;
  totalPlans: number;
  activePlans: number;
  completedPlans: number;
  totalSaved: number;
  totalInterestEarned: number;
  totalTargetAmount: number;
  savingsProgress: number;
};


export default function UserDashboard() {
  const [user, setUser] = useState<UserProfile | null>(null);
  const [savingsSummary, setSavingsSummary] = useState<SavingsSummary | null>(null);
  const [savingsPlans, setSavingsPlans] = useState<SavingsPlan[]>([]);
  const [groupPlans, setGroupPlans] = useState<GroupSavings[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const kycApproved = useKYCEnforcement({ redirect: false });

  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      try {
        // 1. Get user profile
        console.log('[DASHBOARD] Fetching user profile...');
        const userRes = await userService.getCurrentUserProfile();
        console.log('[DASHBOARD] userService.getCurrentUserProfile:', userRes);
        if (userRes) {
          console.log('[DASHBOARD][DEBUG] user fields:', Object.keys(userRes), userRes);
          console.log('[DASHBOARD][DEBUG] user.balance:', userRes.balance);
        } else {
          console.log('[DASHBOARD][DEBUG] userRes is null or undefined');
        }
        setUser(userRes);
        if (userRes) {
          console.log('[DASHBOARD][DEBUG] user fields:', Object.keys(userRes), userRes);
        }
        // No need to fetch wallet balance separately; always use userRes.balance
        // 3. Get savings summary (totalSaved, totalInterestEarned, etc.)
        // Always use env-based backend URL
        const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';
        console.log('[DASHBOARD] Fetching savings summary...');
        const summaryRes = await fetch(`${API_BASE_URL}/api/savings/summary`, {
          headers: { 'Authorization': `Bearer ${localStorage.getItem('auth_token')}` }
        });
        let summary: SavingsSummary | null = null;
        if (summaryRes.ok) {
          summary = await summaryRes.json();
        } else {
          console.error('[DASHBOARD] Failed to fetch savings summary:', summaryRes.status, await summaryRes.text());
        }
        console.log('[DASHBOARD] savings summary:', summary);
        setSavingsSummary(summary);
        // 4. Get individual savings plans
        console.log('[DASHBOARD] Fetching user savings plans...');
        const savingsRes = await savingsService.getUserSavingsPlans();
        console.log('[DASHBOARD] savingsService.getUserSavingsPlans:', savingsRes);
        setSavingsPlans(savingsRes || []);
        // 5. Get group savings plans
        console.log('[DASHBOARD] Fetching group savings plans...');
        const groupRes = await groupSavingsService.getUserGroups();
        console.log('[DASHBOARD] groupSavingsService.getUserGroups:', groupRes);
        setGroupPlans(groupRes || []);
        // 6. Get transactions
        console.log('[DASHBOARD] Fetching user transactions...');
        // Fetch user transactions using userId if user is available
        let txRes = null;
        if (userRes && userRes.id) {
          txRes = await transactionsService.getUserTransactions(userRes.id);
          console.log('[DASHBOARD] transactionsService.getUserTransactions:', txRes);
          setTransactions((txRes && txRes.transactions) || []);
        } else {
          setTransactions([]);
        }
      } catch (err) {
        console.error('[DASHBOARD] fetchData error:', err);
        // Do not clear user state on error; just log the error
      }
      setLoading(false);
    }
    fetchData();
  }, []);

  if (loading) {
    return <div className="flex justify-center items-center min-h-[60vh]"><LoadingLogo /></div>;
  }

  return (
    <DashboardLayout title="Dashboard">
      <div className="space-y-6">
        {/* KYC Banner for incomplete KYC - always at the top */}
        {!kycApproved && <KYCIncompleteBanner />}
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-brand/10 to-brand/5 border border-brand/30 rounded-xl p-8 relative overflow-hidden shadow-xl">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute top-4 right-4 w-32 h-32 bg-brand rounded-full blur-3xl"></div>
            <div className="absolute bottom-4 left-4 w-24 h-24 bg-brand rounded-full blur-2xl"></div>
          </div>

          <div className="flex items-center justify-between relative z-10">
            <div className="flex-1">
              <h1 className="text-4xl font-inter font-bold text-theme mb-3 tracking-tight">
                Welcome back, <span className="text-brand">{user?.firstName}</span>! 👋
              </h1>
              <p className="text-theme-secondary text-lg mb-6 font-inter">
                Here's an overview of your savings journey and financial progress.
              </p>
              <div className="flex items-center space-x-6 text-sm">
                <span className="flex items-center space-x-2 text-brand font-medium">
                  <span>💰</span>
                  <span>Better Interest Rates</span>
                </span>
                <span className="flex items-center space-x-2 text-brand font-medium">
                  <span>📈</span>
                  <span>Growing Savings</span>
                </span>
                <span className="flex items-center space-x-2 text-brand font-medium">
                  <span>🎯</span>
                  <span>Goals Achieved</span>
                </span>
              </div>
            </div>
            <div className="hidden md:flex relative ml-6 justify-center">
            {/* Use TestimonialImage3D wrapper to avoid 'variant' prop error */}
            <TestimonialImage3D
                src="/Celebrating with her iPhone 14 Pro.png"
                alt="Happy user celebrating savings success"
                width={128}
                height={128}
                intensity="light"
                className="border-2 border-green-400/50 mx-auto"
              />
            </div>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          {(() => {
            const bal = user && user.balance !== undefined && user.balance !== null && !isNaN(Number(user.balance))
              ? Number(user.balance)
              : 0;
            console.log('[DASHBOARD][RENDER] Wallet Balance value for StatCard:', bal);
            return (
              <StatCard
                title="Wallet Balance"
                value={`₦${bal.toLocaleString()}`}
                subtitle="Available balance"
                icon={FiDollarSign}
                color="green"
                trend={{ value: 0, isPositive: true }}
              />
            );
          })()}
          <StatCard
            title="Total Savings"
            value={savingsSummary && typeof savingsSummary.totalTargetAmount === 'number' ? `₦${savingsSummary.totalTargetAmount.toLocaleString()}` : "₦0"}
            subtitle="All plan targets"
            icon={FiTrendingUp}
            color="blue"
            trend={{ value: 0, isPositive: true }}
          />
          <StatCard
            title="Total Saved"
            value={savingsSummary && typeof savingsSummary.totalSaved === 'number' ? `₦${savingsSummary.totalSaved.toLocaleString()}` : "₦0"}
            subtitle="Deposited so far"
            icon={FiPieChart}
            color="purple"
          />
          <StatCard
            title="Total Earnings"
            value={savingsSummary && typeof savingsSummary.totalInterestEarned === 'number' ? `₦${savingsSummary.totalInterestEarned.toLocaleString()}` : "₦0"}
            subtitle="Interest & bonuses"
            icon={FiPieChart}
            color="purple"
          />
          <StatCard
            title="Active Plans"
            value={savingsSummary && typeof savingsSummary.activePlans === 'number' ? savingsSummary.activePlans : 0}
            subtitle="Individual & Group"
            icon={FiPieChart}
            color="yellow"
          />

        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <ActionCard
            title="Create Savings Plan"
            subtitle="Start a new individual savings plan"
            icon={FiPlus}
            color="green"
            onClick={() => window.location.href = '/dashboard/savings-plans'}
          />
          <ActionCard
            title="Join Group Savings"
            subtitle="Find and join group savings plans"
            icon={FiUsers}
            color="blue"
            onClick={() => window.location.href = '/dashboard/group-savings'}
          />
          <ActionCard
            title="Make Payment"
            subtitle="Add funds to your savings"
            icon={FiCreditCard}
            color="purple"
            onClick={() => window.location.href = '/dashboard/payments'}
          />
        </div>

        {/* Charts and Progress */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Savings Progress Chart */}
          <SavingsLineChart
            data={
              savingsPlans.length > 0
                ? savingsPlans.map(plan => ({
                    name: plan.name,
                    value: typeof plan.currentAmount === 'number' ? plan.currentAmount : 0,
                    target: typeof plan.targetAmount === 'number' ? plan.targetAmount : 0,
                  }))
                : [{ name: 'No Plans', value: 0, target: 0 }]
            }
            title="Savings Progress"
            height={300}
            showTargetLine={true}
          />

          {/* Goals Progress (show each plan as a progress card) */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">Goals Progress</h3>
            {savingsPlans.map(plan => (
              <ProgressCard
                key={plan.id}
                title={plan.name}
                current={plan.currentAmount}
                target={plan.targetAmount}
                unit="₦"
                color="green"
              />
            ))}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-gray-900/50 border border-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Recent Activity</h3>
            {/* You can add a View All button here if needed */}
          </div>
          <div className="space-y-3">
            {transactions.slice(0, 5).map((tx, index) => (
              <div key={index} className="flex items-center justify-between py-3 border-b border-gray-800 last:border-b-0">
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    tx.type === 'DEPOSIT' ? 'bg-green-500/20' :
                    tx.type === 'INTEREST' ? 'bg-purple-500/20' :
                    tx.type === 'WITHDRAWAL' ? 'bg-blue-500/20' :
                    'bg-yellow-500/20'
                  }`}>
                    {tx.type === 'DEPOSIT' && <FiDollarSign className="w-4 h-4 text-green-400" />}
                    {tx.type === 'INTEREST' && <FiTrendingUp className="w-4 h-4 text-purple-400" />}
                    {tx.type === 'WITHDRAWAL' && <FiCreditCard className="w-4 h-4 text-blue-400" />}
                    {tx.type === 'PENALTY' && <FiTarget className="w-4 h-4 text-yellow-400" />}
                  </div>
                  <div>
                    <p className="text-white text-sm font-medium">{tx.type}</p>
                    <p className="text-gray-400 text-xs">{tx.description || tx.planId || tx.goalId}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-green-400 text-sm font-medium">₦{tx.amount?.toLocaleString()}</p>
                  <p className="text-gray-500 text-xs">{new Date(tx.createdAt).toLocaleString()}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* KYC Status banner is now always at the top via KYCIncompleteBanner */}

      </div>


    </DashboardLayout>
  );
}
