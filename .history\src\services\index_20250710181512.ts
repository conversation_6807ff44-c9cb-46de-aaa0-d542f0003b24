// Export all services for easy importing
export { authService } from './auth';
export { userService } from './user';
export { savingsService } from './savings';
export { groupSavingsService } from './groupSavings';
export { targetSavingsService } from './targetSavings';
export { depositsService } from './deposits';
export { withdrawalsService } from './withdrawals';
export { paystackService } from './paystackService';
export { transactionsService } from './transactions';
export { notificationsService } from './notifications';
export { kycService } from './kyc';
export { adminService } from './admin';
export { settingsService } from './settings';

// Service configuration
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api',
  TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 second
};

// Common service utilities
export class ServiceError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ServiceError';
  }
}

export const createAuthHeaders = () => {
  const token = localStorage.getItem('auth_token');
  return {
    'Content-Type': 'application/json',
    ...(token && { Authorization: `Bearer ${token}` })
  };
};

export const createFileUploadHeaders = () => {
  const token = localStorage.getItem('auth_token');
  return {
    ...(token && { Authorization: `Bearer ${token}` })
  };
};

export const handleApiResponse = async (response: Response) => {
  if (!response.ok) {
    let error;
    try {
      error = await response.json();
    } catch {
      error = { message: 'An unexpected error occurred' };
    }
    
    throw new ServiceError(
      error.message || 'Request failed',
      response.status,
      error.code,
      error.details
    );
  }
  
  return response.json();
};

export const buildQueryParams = (params: Record<string, any>): URLSearchParams => {
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      if (Array.isArray(value)) {
        value.forEach(item => searchParams.append(key, item.toString()));
      } else {
        searchParams.append(key, value.toString());
      }
    }
  });
  
  return searchParams;
};

export const retryRequest = async <T>(
  requestFn: () => Promise<T>,
  maxAttempts: number = API_CONFIG.RETRY_ATTEMPTS,
  delay: number = API_CONFIG.RETRY_DELAY
): Promise<T> => {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await requestFn();
    } catch (error) {
      lastError = error as Error;
      
      // Don't retry on client errors (4xx)
      if (error instanceof ServiceError && error.statusCode && error.statusCode < 500) {
        throw error;
      }
      
      if (attempt === maxAttempts) {
        break;
      }
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }
  
  throw lastError!;
};

// Token management utilities
export const tokenManager = {
  getToken(): string | null {
    return localStorage.getItem('auth_token');
  },
  
  setToken(token: string): void {
    localStorage.setItem('auth_token', token);
  },
  
  getRefreshToken(): string | null {
    return localStorage.getItem('refresh_token');
  },
  
  setRefreshToken(token: string): void {
    localStorage.setItem('refresh_token', token);
  },
  
  clearTokens(): void {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('refresh_token');
  },
  
  isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp * 1000 < Date.now();
    } catch {
      return true;
    }
  },
  
  shouldRefreshToken(): boolean {
    const token = this.getToken();
    if (!token) return false;
    
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const expirationTime = payload.exp * 1000;
      const currentTime = Date.now();
      const timeUntilExpiry = expirationTime - currentTime;
      
      // Refresh if token expires in less than 5 minutes
      return timeUntilExpiry < 5 * 60 * 1000;
    } catch {
      return true;
    }
  }
};

// Request interceptor for automatic token refresh
export const createApiRequest = async (
  url: string,
  options: RequestInit = {}
): Promise<Response> => {
  // Check if token needs refresh
  if (tokenManager.shouldRefreshToken()) {
    try {
      const { authService } = await import('./auth');
      await authService.refreshToken();
    } catch (error) {
      // If refresh fails, clear tokens and redirect to login
      tokenManager.clearTokens();
      window.location.href = '/login';
      throw error;
    }
  }
  
  const response = await fetch(url, {
    ...options,
    headers: {
      ...createAuthHeaders(),
      ...options.headers
    }
  });
  
  // Handle 401 responses
  if (response.status === 401) {
    tokenManager.clearTokens();
    window.location.href = '/login';
    throw new ServiceError('Authentication required', 401);
  }
  
  return response;
};

// Currency formatting utility
export const formatCurrency = (amount: number, currency: string = 'NGN'): string => {
  return new Intl.NumberFormat('en-NG', {
    style: 'currency',
    currency
  }).format(amount);
};

// Date formatting utilities
export const formatDate = (date: string | Date): string => {
  return new Intl.DateTimeFormat('en-NG', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(new Date(date));
};

export const formatDateTime = (date: string | Date): string => {
  return new Intl.DateTimeFormat('en-NG', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(date));
};

export const formatTimeAgo = (date: string | Date): string => {
  const now = new Date();
  const past = new Date(date);
  const diffInSeconds = Math.floor((now.getTime() - past.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'Just now';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours} hour${hours > 1 ? 's' : ''} ago`;
  } else if (diffInSeconds < 604800) {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days} day${days > 1 ? 's' : ''} ago`;
  } else {
    return formatDate(date);
  }
};

// Validation utilities
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePhoneNumber = (phoneNumber: string): boolean => {
  const phoneRegex = /^(\+234|234|0)?[789][01]\d{8}$/;
  return phoneRegex.test(phoneNumber);
};

export const validateAmount = (amount: number, min: number = 0, max?: number): { isValid: boolean; error?: string } => {
  if (isNaN(amount) || amount < 0) {
    return { isValid: false, error: 'Amount must be a positive number' };
  }
  
  if (amount < min) {
    return { isValid: false, error: `Amount must be at least ${formatCurrency(min)}` };
  }
  
  if (max && amount > max) {
    return { isValid: false, error: `Amount must not exceed ${formatCurrency(max)}` };
  }
  
  return { isValid: true };
};

// File utilities
export const validateFileSize = (file: File, maxSizeMB: number): { isValid: boolean; error?: string } => {
  const maxSizeBytes = maxSizeMB * 1024 * 1024;
  
  if (file.size > maxSizeBytes) {
    return {
      isValid: false,
      error: `File size must be less than ${maxSizeMB}MB`
    };
  }
  
  return { isValid: true };
};

export const validateFileType = (file: File, allowedTypes: string[]): { isValid: boolean; error?: string } => {
  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: `File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(', ')}`
    };
  }
  
  return { isValid: true };
};

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
