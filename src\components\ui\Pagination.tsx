"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { FiChevronLeft, FiChevronRight, FiMoreHorizontal } from 'react-icons/fi';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  showInfo?: boolean;
  totalItems?: number;
  itemsPerPage?: number;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  maxVisiblePages?: number;
}

export function Pagination({
  currentPage,
  totalPages,
  onPageChange,
  showInfo = false,
  totalItems,
  itemsPerPage,
  className = '',
  size = 'md',
  maxVisiblePages = 7
}: PaginationProps) {
  const sizeClasses = {
    sm: {
      button: 'px-2 py-1 text-sm',
      spacing: 'space-x-1'
    },
    md: {
      button: 'px-3 py-2 text-sm',
      spacing: 'space-x-2'
    },
    lg: {
      button: 'px-4 py-3 text-base',
      spacing: 'space-x-3'
    }
  };

  const currentSize = sizeClasses[size];

  // Calculate visible page numbers
  const getVisiblePages = () => {
    if (totalPages <= maxVisiblePages) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    const halfVisible = Math.floor(maxVisiblePages / 2);
    let start = Math.max(1, currentPage - halfVisible);
    let end = Math.min(totalPages, start + maxVisiblePages - 1);

    if (end - start + 1 < maxVisiblePages) {
      start = Math.max(1, end - maxVisiblePages + 1);
    }

    const pages = [];
    
    // Add first page if not in range
    if (start > 1) {
      pages.push(1);
      if (start > 2) {
        pages.push('...');
      }
    }

    // Add visible pages
    for (let i = start; i <= end; i++) {
      pages.push(i);
    }

    // Add last page if not in range
    if (end < totalPages) {
      if (end < totalPages - 1) {
        pages.push('...');
      }
      pages.push(totalPages);
    }

    return pages;
  };

  const visiblePages = getVisiblePages();

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      onPageChange(page);
    }
  };

  const getItemRange = () => {
    if (!totalItems || !itemsPerPage) return null;
    
    const start = (currentPage - 1) * itemsPerPage + 1;
    const end = Math.min(currentPage * itemsPerPage, totalItems);
    
    return { start, end };
  };

  const itemRange = getItemRange();

  if (totalPages <= 1) {
    return showInfo && itemRange ? (
      <div className={`text-sm text-gray-400 ${className}`}>
        Showing {itemRange.start} to {itemRange.end} of {totalItems} results
      </div>
    ) : null;
  }

  return (
    <div className={`flex items-center justify-between ${className}`}>
      {/* Info */}
      {showInfo && itemRange && (
        <div className="text-sm text-gray-400">
          Showing {itemRange.start} to {itemRange.end} of {totalItems} results
        </div>
      )}

      {/* Pagination Controls */}
      <nav className={`flex items-center ${currentSize.spacing}`}>
        {/* Previous Button */}
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className={`
            ${currentSize.button}
            flex items-center justify-center rounded-lg border border-gray-600 bg-gray-800 text-gray-300
            hover:bg-gray-700 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed
            transition-colors duration-200
          `}
        >
          <FiChevronLeft className="w-4 h-4" />
          <span className="ml-1 hidden sm:inline">Previous</span>
        </motion.button>

        {/* Page Numbers */}
        <div className={`flex items-center ${currentSize.spacing}`}>
          {visiblePages.map((page, index) => {
            if (page === '...') {
              return (
                <span
                  key={`ellipsis-${index}`}
                  className={`${currentSize.button} flex items-center justify-center text-gray-400`}
                >
                  <FiMoreHorizontal className="w-4 h-4" />
                </span>
              );
            }

            const pageNumber = page as number;
            const isActive = pageNumber === currentPage;

            return (
              <motion.button
                key={pageNumber}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => handlePageChange(pageNumber)}
                className={`
                  ${currentSize.button}
                  flex items-center justify-center rounded-lg border transition-colors duration-200
                  ${isActive
                    ? 'border-green-500 bg-green-600 text-white'
                    : 'border-gray-600 bg-gray-800 text-gray-300 hover:bg-gray-700 hover:text-white'
                  }
                `}
              >
                {pageNumber}
              </motion.button>
            );
          })}
        </div>

        {/* Next Button */}
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className={`
            ${currentSize.button}
            flex items-center justify-center rounded-lg border border-gray-600 bg-gray-800 text-gray-300
            hover:bg-gray-700 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed
            transition-colors duration-200
          `}
        >
          <span className="mr-1 hidden sm:inline">Next</span>
          <FiChevronRight className="w-4 h-4" />
        </motion.button>
      </nav>
    </div>
  );
}

// Simple pagination component for basic use cases
export function SimplePagination({
  currentPage,
  totalPages,
  onPageChange,
  className = ''
}: {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  className?: string;
}) {
  if (totalPages <= 1) return null;

  return (
    <div className={`flex items-center justify-center space-x-2 ${className}`}>
      <button
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className="px-3 py-2 text-sm rounded-lg border border-gray-600 bg-gray-800 text-gray-300 hover:bg-gray-700 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        Previous
      </button>
      
      <span className="px-4 py-2 text-sm text-gray-300">
        Page {currentPage} of {totalPages}
      </span>
      
      <button
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className="px-3 py-2 text-sm rounded-lg border border-gray-600 bg-gray-800 text-gray-300 hover:bg-gray-700 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        Next
      </button>
    </div>
  );
}

// Page size selector component
export function PageSizeSelector({
  pageSize,
  onPageSizeChange,
  options = [10, 20, 50, 100],
  className = ''
}: {
  pageSize: number;
  onPageSizeChange: (size: number) => void;
  options?: number[];
  className?: string;
}) {
  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <span className="text-sm text-gray-400">Show:</span>
      <select
        value={pageSize}
        onChange={(e) => onPageSizeChange(Number(e.target.value))}
        className="px-3 py-1 text-sm rounded border border-gray-600 bg-gray-800 text-white focus:outline-none focus:border-green-500"
      >
        {options.map((size) => (
          <option key={size} value={size}>
            {size}
          </option>
        ))}
      </select>
      <span className="text-sm text-gray-400">per page</span>
    </div>
  );
}
