"use client";

import { Star } from "lucide-react";

const testimonials = [
  {
    name: "<PERSON>",
    role: "Marketing Manager",
    content:
      "Koja Save helped me save $10,000 for my dream vacation in just 8 months. The automated features made it effortless!",
    rating: 5,
  },
  {
    name: "<PERSON>",
    role: "Software Engineer",
    content:
      "The high-yield returns and smart goal tracking features are game-changers. I've never been better at saving money.",
    rating: 5,
  },
  {
    name: "<PERSON>",
    role: "Teacher",
    content:
      "Finally, a savings app that actually helps me save! The interface is beautiful and the features are incredibly useful.",
    rating: 5,
  },
];

export const TestimonialsSection = () => {
  return (
    <section id="testimonials" className="py-20 px-4" data-oid="j0y3g7d">
      <div className="max-w-7xl mx-auto" data-oid="96klx57">
        <div className="text-center mb-16" data-oid="u.brubv">
          <h2
            className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-white to-green-400 bg-clip-text text-transparent"
            data-oid="rer:kx6"
          >
            What Our Users Say
          </h2>
          <p
            className="text-xl text-gray-300 max-w-3xl mx-auto"
            data-oid="li7c1zq"
          >
            Join thousands of satisfied users who have transformed their
            financial lives with Koja Save
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8" data-oid=":amwuuu">
          {testimonials.map((testimonial, index) => (
            <div
              key={index}
              className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-green-500/20 rounded-2xl p-8 hover:border-green-500/40 transition-all duration-300 transform hover:scale-105 hover-lift"
              data-oid="frh9.5a"
            >
              <div className="flex mb-4" data-oid="ug7k5d-">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star
                    key={i}
                    className="w-5 h-5 text-green-400 fill-current"
                    data-oid="m45_e76"
                  />
                ))}
              </div>
              <p
                className="text-gray-300 mb-6 leading-relaxed"
                data-oid="tjgfnny"
              >
                "{testimonial.content}"
              </p>
              <div data-oid="wncuq:g">
                <div className="font-semibold text-white" data-oid="4:r:twv">
                  {testimonial.name}
                </div>
                <div className="text-green-400 text-sm" data-oid="psvz769">
                  {testimonial.role}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};
