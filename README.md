# Better Interest - Smart Savings App Landing Page

A modern, responsive landing page for Better Interest, a smart savings application built with Next.js, React, Tailwind CSS, and featuring beautiful animations inspired by Onlook's design.

## 🌟 Features

- **Modern Design**: Clean, professional design with green color scheme and dark gradients
- **Responsive**: Fully responsive design that works on all devices
- **Animations**: Smooth animations using Framer Motion and AOS (Animate On Scroll)
- **Interactive Elements**: Hover effects, button animations, and particle backgrounds
- **Complete Pages**: Landing page, login, signup, and contact pages
- **Accessibility**: Focus states and keyboard navigation support
- **SEO Optimized**: Meta tags, Open Graph, and Twitter Card support

## 🚀 Tech Stack

- **Framework**: Next.js 14 with App Router
- **Styling**: Tailwind CSS with custom animations
- **Animations**: Framer Motion + AOS (Animate On Scroll)
- **Icons**: React Icons (Feather Icons)
- **Package Manager**: Bun
- **TypeScript**: Full TypeScript support

## 📦 Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd koja-save-landing
```

2. Install dependencies:
```bash
bun install
```

3. Run the development server:
```bash
bun run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🎨 Design Features

### Color Scheme
- **Primary Green**: `#22c55e` (green-500)
- **Secondary Green**: `#16a34a` (green-600)
- **Dark Green**: `#15803d` (green-700)
- **Background**: Dark gradient from gray-900 via green-900 to black

### Animations
- **Particle Background**: Floating green particles with random movement
- **Gradient Animation**: Animated background gradients
- **Hover Effects**: Scale, glow, and color transitions
- **AOS Animations**: Scroll-triggered animations for content sections
- **Framer Motion**: Page transitions and interactive elements

### Components
- **Glass Morphism**: Translucent cards with backdrop blur
- **Gradient Text**: Animated gradient text effects
- **Button Effects**: Glow, hover, and ripple animations
- **Loading States**: Shimmer effects for loading content

## 📱 Pages

### Landing Page (`/`)
- Hero section with animated background
- Features showcase
- Testimonials
- Pricing plans
- Call-to-action sections
- Footer with links

### Login Page (`/login`)
- Email/password form
- Social login options (Google, Facebook)
- Remember me functionality
- Forgot password link
- Animated background

### Signup Page (`/signup`)
- Multi-step form with validation
- Password strength indicator
- Terms and conditions
- Social signup options
- Real-time form validation

### Contact Page (`/contact`)
- Contact form with subject selection
- Contact information cards
- Support options
- FAQ section
- Animated elements

## 🛠️ Customization

### Colors
Update the color scheme in `tailwind.config.ts` and `globals.css`:

```css
/* Primary colors */
--primary: #22c55e;
--primary-dark: #16a34a;
--primary-darker: #15803d;
```

### Animations
Customize animations in `globals.css`:

```css
@keyframes your-animation {
  /* Your keyframes */
}

.your-class {
  animation: your-animation 2s ease infinite;
}
```

### Content
Update content in the respective page components:
- `app/page.tsx` - Landing page content
- `app/login/page.tsx` - Login page
- `app/signup/page.tsx` - Signup page
- `app/contact/page.tsx` - Contact page

## 🎯 Performance

- **Optimized Images**: Next.js Image component for optimal loading
- **Code Splitting**: Automatic code splitting with Next.js
- **CSS Optimization**: Tailwind CSS purging for minimal bundle size
- **Animation Performance**: Hardware-accelerated CSS animations

## 🔧 Development

### Scripts
```bash
# Development server
bun run dev

# Build for production
bun run build

# Start production server
bun run start

# Lint code
bun run lint

# Format code
bun run format
```

### Project Structure
```
├── app/                    # Next.js app directory
│   ├── contact/           # Contact page
│   ├── login/             # Login page
│   ├── signup/            # Signup page
│   ├── globals.css        # Global styles and animations
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Landing page
├── components/            # Reusable components
│   └── ui/               # UI components
├── lib/                  # Utility functions
├── public/               # Static assets
└── tailwind.config.ts    # Tailwind configuration
```

## 🌐 Deployment

The app is ready for deployment on platforms like:
- **Vercel** (recommended for Next.js)
- **Netlify**
- **Railway**
- **DigitalOcean App Platform**

### Environment Variables
No environment variables required for the basic setup.

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For support or questions, please contact:
- Email: <EMAIL>
- Website: [Better Interest](https://betterinterest.com)

---

Built with ❤️ using Next.js, Tailwind CSS, and modern web technologies.
