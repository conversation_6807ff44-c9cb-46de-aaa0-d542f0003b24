import { useEffect, useState, useCallback, useRef } from 'react';
import { webSocketService, NotificationEvent, AllWebSocketEvents } from '../services/websocket.service';
import { toast } from '../components/ui/Toast';

export interface RealTimeNotification {
  id: string;
  title: string;
  message: string;
  type: 'DEPOSIT' | 'WITHDRAWAL' | 'CONTRIBUTION' | 'SYSTEM' | 'SECURITY' | 'PROMOTIONAL';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  timestamp: string;
  read: boolean;
  actionRequired?: boolean;
  actionUrl?: string;
  actionText?: string;
}

export interface UseRealTimeNotificationsOptions {
  showToasts?: boolean;
  autoMarkAsRead?: boolean;
  maxNotifications?: number;
  enableSound?: boolean;
  soundUrl?: string;
}

export interface UseRealTimeNotificationsReturn {
  notifications: RealTimeNotification[];
  unreadCount: number;
  isConnected: boolean;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  clearNotification: (notificationId: string) => void;
  clearAllNotifications: () => void;
  playNotificationSound: () => void;
}

const defaultOptions: UseRealTimeNotificationsOptions = {
  showToasts: true,
  autoMarkAsRead: false,
  maxNotifications: 50,
  enableSound: true,
  soundUrl: '/sounds/notification.mp3'
};

export function useRealTimeNotifications(
  options: UseRealTimeNotificationsOptions = {}
): UseRealTimeNotificationsReturn {
  const opts = { ...defaultOptions, ...options };
  
  const [notifications, setNotifications] = useState<RealTimeNotification[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Initialize audio for notifications
  useEffect(() => {
    if (opts.enableSound && opts.soundUrl) {
      audioRef.current = new Audio(opts.soundUrl);
      audioRef.current.volume = 0.5;
    }
  }, [opts.enableSound, opts.soundUrl]);

  const playNotificationSound = useCallback(() => {
    if (audioRef.current && opts.enableSound) {
      audioRef.current.play().catch(error => {
        console.warn('Could not play notification sound:', error);
      });
    }
  }, [opts.enableSound]);

  const showToast = useCallback((notification: RealTimeNotification) => {
    if (!opts.showToasts) return;

    const toastOptions = {
      duration: notification.priority === 'URGENT' ? 10000 : 5000,
      action: notification.actionRequired && notification.actionUrl ? {
        label: notification.actionText || 'View',
        onClick: () => window.open(notification.actionUrl, '_blank')
      } : undefined
    };

    switch (notification.priority) {
      case 'URGENT':
        toast.error(notification.message, toastOptions);
        break;
      case 'HIGH':
        toast.warning(notification.message, toastOptions);
        break;
      case 'MEDIUM':
        toast.info(notification.message, toastOptions);
        break;
      default:
        toast.success(notification.message, toastOptions);
    }
  }, [opts.showToasts]);

  const addNotification = useCallback((event: NotificationEvent) => {
    const notification: RealTimeNotification = {
      id: event.data.id,
      title: event.data.title,
      message: event.data.message,
      type: event.data.type,
      priority: event.data.priority,
      timestamp: event.timestamp,
      read: false,
      actionRequired: event.data.actionRequired,
      actionUrl: event.data.actionUrl,
      actionText: event.data.actionText
    };

    setNotifications(prev => {
      const updated = [notification, ...prev];
      
      // Limit the number of notifications
      if (opts.maxNotifications && updated.length > opts.maxNotifications) {
        return updated.slice(0, opts.maxNotifications);
      }
      
      return updated;
    });

    // Show toast notification
    showToast(notification);

    // Play sound
    if (notification.priority === 'URGENT' || notification.priority === 'HIGH') {
      playNotificationSound();
    }

    // Auto-mark as read if enabled
    if (opts.autoMarkAsRead) {
      setTimeout(() => {
        markAsRead(notification.id);
      }, 3000);
    }
  }, [opts.maxNotifications, opts.autoMarkAsRead, showToast, playNotificationSound]);

  const markAsRead = useCallback((notificationId: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, read: true }
          : notification
      )
    );
  }, []);

  const markAllAsRead = useCallback(() => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, read: true }))
    );
  }, []);

  const clearNotification = useCallback((notificationId: string) => {
    setNotifications(prev => 
      prev.filter(notification => notification.id !== notificationId)
    );
  }, []);

  const clearAllNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  // WebSocket event handlers
  useEffect(() => {
    const unsubscribeNotification = webSocketService.on('notification', addNotification);
    const unsubscribeConnection = webSocketService.onConnectionChange(setIsConnected);

    // Handle other real-time events that should show notifications
    const unsubscribeTransaction = webSocketService.on('transaction', (event) => {
      const notification: RealTimeNotification = {
        id: `transaction-${event.data.id}`,
        title: 'Transaction Update',
        message: `Your ${event.data.type.toLowerCase()} of ₦${event.data.amount.toLocaleString()} is ${event.data.status.toLowerCase()}`,
        type: event.data.type === 'DEPOSIT' ? 'DEPOSIT' : 'WITHDRAWAL',
        priority: event.data.status === 'FAILED' ? 'HIGH' : 'MEDIUM',
        timestamp: event.timestamp,
        read: false
      };
      
      setNotifications(prev => [notification, ...prev]);
      showToast(notification);
    });

    const unsubscribeBalance = webSocketService.on('balance_update', (event) => {
      const isIncrease = event.data.change > 0;
      const notification: RealTimeNotification = {
        id: `balance-${Date.now()}`,
        title: 'Balance Updated',
        message: `Your balance has ${isIncrease ? 'increased' : 'decreased'} by ₦${Math.abs(event.data.change).toLocaleString()}. New balance: ₦${event.data.newBalance.toLocaleString()}`,
        type: 'SYSTEM',
        priority: 'LOW',
        timestamp: event.timestamp,
        read: false
      };
      
      setNotifications(prev => [notification, ...prev]);
      if (isIncrease) {
        showToast(notification);
      }
    });

    const unsubscribeGroupSavings = webSocketService.on('group_savings', (event) => {
      const notification: RealTimeNotification = {
        id: `group-${event.data.groupId}-${Date.now()}`,
        title: `Group: ${event.data.groupName}`,
        message: event.data.message,
        type: 'CONTRIBUTION',
        priority: event.data.eventType === 'GOAL_ACHIEVED' ? 'HIGH' : 'MEDIUM',
        timestamp: event.timestamp,
        read: false
      };
      
      setNotifications(prev => [notification, ...prev]);
      showToast(notification);
      
      if (event.data.eventType === 'GOAL_ACHIEVED') {
        playNotificationSound();
      }
    });

    const unsubscribeKYC = webSocketService.on('kyc_update', (event) => {
      const notification: RealTimeNotification = {
        id: `kyc-${Date.now()}`,
        title: 'KYC Update',
        message: event.data.message,
        type: 'SECURITY',
        priority: event.data.status === 'REJECTED' ? 'HIGH' : 'MEDIUM',
        timestamp: event.timestamp,
        read: false,
        actionRequired: event.data.status === 'REJECTED',
        actionUrl: '/dashboard/kyc',
        actionText: 'Update KYC'
      };
      
      setNotifications(prev => [notification, ...prev]);
      showToast(notification);
      
      if (event.data.status === 'APPROVED' || event.data.status === 'REJECTED') {
        playNotificationSound();
      }
    });

    const unsubscribeMaintenance = webSocketService.on('system_maintenance', (event) => {
      const notification: RealTimeNotification = {
        id: `maintenance-${Date.now()}`,
        title: 'System Maintenance',
        message: event.data.message,
        type: 'SYSTEM',
        priority: event.data.type === 'EMERGENCY' ? 'URGENT' : 'MEDIUM',
        timestamp: event.timestamp,
        read: false
      };
      
      setNotifications(prev => [notification, ...prev]);
      showToast(notification);
      
      if (event.data.type === 'EMERGENCY') {
        playNotificationSound();
      }
    });

    return () => {
      unsubscribeNotification();
      unsubscribeConnection();
      unsubscribeTransaction();
      unsubscribeBalance();
      unsubscribeGroupSavings();
      unsubscribeKYC();
      unsubscribeMaintenance();
    };
  }, [addNotification, showToast, playNotificationSound]);

  // Connect to WebSocket on mount
  useEffect(() => {
    webSocketService.connect();
    
    return () => {
      // Don't disconnect on unmount as other components might be using it
      // webSocketService.disconnect();
    };
  }, []);

  const unreadCount = notifications.filter(n => !n.read).length;

  return {
    notifications,
    unreadCount,
    isConnected,
    markAsRead,
    markAllAsRead,
    clearNotification,
    clearAllNotifications,
    playNotificationSound
  };
}
