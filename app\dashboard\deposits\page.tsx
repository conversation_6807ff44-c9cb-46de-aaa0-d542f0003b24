"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  FiPlus, 
  FiCreditCard, 
  FiDollarSign,
  FiTrendingUp,
  FiCalendar,
  FiCheck,
  FiX,
  FiClock,
  FiDownload,
  FiFilter
} from 'react-icons/fi';
import DashboardLayout from '../../../src/components/dashboard/DashboardLayout';
import { useKYCEnforcement } from '../../../src/hooks/use-kyc-enforcement';
import { KYCIncompleteBanner } from '../../../src/components/KYCIncompleteBanner';
import { depositsService } from '../../../src/services';
import { Deposit, CreateDepositData, DepositSearchFilters } from '../../../src/types';
import { Button } from '../../../src/components/ui/Button';
import { Card } from '../../../src/components/ui/Card';
import { Badge } from '../../../src/components/ui/Badge';
import { Modal } from '../../../src/components/ui/Modal';
import { Input } from '../../../src/components/ui/Input';
import { Select } from '../../../src/components/ui/Select';
import Table from '../../../src/components/ui/Table';
import { toast } from '../../../src/components/ui/Toast';

const paymentMethodIcons = {
  CARD: '💳',
  BANK_TRANSFER: '🏦',
  USSD: '📱',
  QR_CODE: '📱'
};

  const kycApproved = useKYCEnforcement({ redirect: false });
  const [deposits, setDeposits] = useState<Deposit[]>([]);
  const [loading, setLoading] = useState(true);
  const [showDepositModal, setShowDepositModal] = useState(false);
  const [processingDeposit, setProcessingDeposit] = useState(false);
  
  const [depositForm, setDepositForm] = useState<CreateDepositData>({
    amount: 0,
    paymentMethod: 'CARD',
    currency: 'NGN',
    description: ''
  });

  const [filters, setFilters] = useState<DepositSearchFilters>({
    status: undefined,
    paymentMethod: undefined,
    dateFrom: '',
    dateTo: '',
    page: 1,
    limit: 20
  });

  const [stats, setStats] = useState({
    totalDeposits: 0,
    totalAmount: 0,
    successfulDeposits: 0,
    pendingDeposits: 0
  });

  useEffect(() => {
    loadDeposits();
    loadStats();
  }, [filters]);

  const loadDeposits = async () => {
    try {
      setLoading(true);
      const response = await depositsService.getUserDeposits(filters);
      setDeposits(response.deposits);
    } catch (error) {
      toast.error('Failed to load deposits');
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const response = await depositsService.getDepositStats();
      setStats(response);
    } catch (error) {
      console.error('Failed to load deposit stats:', error);
    }
  };

  const handleInitiateDeposit = async () => {
    try {
      if (!depositForm.amount || depositForm.amount < 100) {
        toast.error('Minimum deposit amount is ₦100');
        return;
      }

      setProcessingDeposit(true);
      const response = await depositsService.initiateDeposit(depositForm);
      
      // Redirect to payment gateway
      if (response.paymentData.authorizationUrl) {
        window.open(response.paymentData.authorizationUrl, '_blank');
      }

      setShowDepositModal(false);
      setDepositForm({
        amount: 0,
        paymentMethod: 'CARD',
        currency: 'NGN',
        description: ''
      });

      toast.success('Deposit initiated successfully');
      loadDeposits();
    } catch (error: any) {
      toast.error(error.message || 'Failed to initiate deposit');
    } finally {
      setProcessingDeposit(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-NG');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED': return 'success';
      case 'PENDING': return 'warning';
      case 'FAILED': return 'danger';
      case 'CANCELLED': return 'default';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED': return <FiCheck className="text-green-500" />;
      case 'PENDING': return <FiClock className="text-yellow-500" />;
      case 'FAILED': return <FiX className="text-red-500" />;
      default: return <FiClock className="text-gray-500" />;
    }
  };

  const columns = [
    {
      key: 'reference',
      title: 'Reference',
      render: (value: string) => (
        <span className="font-mono text-sm text-gray-300">{value}</span>
      )
    },
    {
      key: 'amount',
      title: 'Amount',
      render: (value: number) => (
        <span className="font-semibold text-white">{formatCurrency(value)}</span>
      )
    },
    {
      key: 'paymentMethod',
      title: 'Method',
      render: (value: string) => (
        <div className="flex items-center space-x-2">
          <span>{paymentMethodIcons[value]}</span>
          <span className="text-gray-300">{value.replace('_', ' ')}</span>
        </div>
      )
    },
    {
      key: 'status',
      title: 'Status',
      render: (value: string, row: Deposit) => (
        <div className="flex items-center space-x-2">
          {getStatusIcon(value)}
          <Badge variant={getStatusColor(value)}>
            {value}
          </Badge>
        </div>
      )
    },
    {
      key: 'fees',
      title: 'Fees',
      render: (value: number) => (
        <span className="text-gray-400">{formatCurrency(value || 0)}</span>
      )
    },
    {
      key: 'createdAt',
      title: 'Date',
      render: (value: string) => (
        <span className="text-gray-400">{formatDate(value)}</span>
      )
    }
  ];

  return (
    <DashboardLayout title="Deposits">
      <div className="space-y-6">
        {/* KYC Banner for incomplete KYC */}
        {!kycApproved && <KYCIncompleteBanner />}
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white">Deposits</h1>
            <p className="text-gray-400 mt-2">Add money to your account</p>
          </div>
          <Button
            onClick={() => setShowDepositModal(true)}
            className="bg-green-600 hover:bg-green-700"
          >
            <FiPlus className="mr-2" />
            Make Deposit
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Deposits</p>
                <p className="text-2xl font-bold text-white">{stats.totalDeposits}</p>
              </div>
              <FiDollarSign className="text-green-500 text-2xl" />
            </div>
          </Card>

          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Amount</p>
                <p className="text-2xl font-bold text-white">{formatCurrency(stats.totalAmount)}</p>
              </div>
              <FiTrendingUp className="text-blue-500 text-2xl" />
            </div>
          </Card>

          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Successful</p>
                <p className="text-2xl font-bold text-green-400">{stats.successfulDeposits}</p>
              </div>
              <FiCheck className="text-green-500 text-2xl" />
            </div>
          </Card>

          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Pending</p>
                <p className="text-2xl font-bold text-yellow-400">{stats.pendingDeposits}</p>
              </div>
              <FiClock className="text-yellow-500 text-2xl" />
            </div>
          </Card>
        </div>

        {/* Filters */}
        <Card className="bg-gray-800 border-gray-700 p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Select
              value={filters.status || ''}
              onChange={(value) => setFilters({ ...filters, status: value || undefined })}
              options={[
                { value: '', label: 'All Status' },
                { value: 'PENDING', label: 'Pending' },
                { value: 'COMPLETED', label: 'Completed' },
                { value: 'FAILED', label: 'Failed' },
                { value: 'CANCELLED', label: 'Cancelled' }
              ]}
            />

            <Select
              value={filters.paymentMethod || ''}
              onChange={(value) => setFilters({ ...filters, paymentMethod: value || undefined })}
              options={[
                { value: '', label: 'All Methods' },
                { value: 'CARD', label: 'Card Payment' },
                { value: 'BANK_TRANSFER', label: 'Bank Transfer' },
                { value: 'USSD', label: 'USSD' },
                { value: 'QR_CODE', label: 'QR Code' }
              ]}
            />

            <Input
              type="date"
              value={filters.dateFrom}
              onChange={(e) => setFilters({ ...filters, dateFrom: e.target.value })}
              placeholder="From Date"
            />

            <Input
              type="date"
              value={filters.dateTo}
              onChange={(e) => setFilters({ ...filters, dateTo: e.target.value })}
              placeholder="To Date"
            />
          </div>
        </Card>

        {/* Deposits Table */}
        <Card className="bg-gray-800 border-gray-700">
          <div className="p-6 border-b border-gray-700">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold text-white">Recent Deposits</h3>
              <Button variant="outline" size="sm">
                <FiDownload className="mr-2" />
                Export
              </Button>
            </div>
          </div>

          <Table
            data={deposits}
            columns={columns}
            loading={loading}
            emptyMessage="No deposits found"
          />
        </Card>
      </div>

      {/* Deposit Modal */}
      <Modal
        isOpen={showDepositModal}
        onClose={() => setShowDepositModal(false)}
        title="Make a Deposit"
        size="md"
      >
        <div className="space-y-6">
          <div>
            <Input
              label="Amount"
              type="number"
              value={depositForm.amount}
              onChange={(e) => setDepositForm({ ...depositForm, amount: Number(e.target.value) })}
              placeholder="Enter amount"
              min="100"
              required
            />
            <p className="text-sm text-gray-400 mt-1">Minimum deposit: ₦100</p>
          </div>

          <Select
            label="Payment Method"
            value={depositForm.paymentMethod}
            onChange={(value) => setDepositForm({ ...depositForm, paymentMethod: value as any })}
            options={[
              { value: 'CARD', label: '💳 Card Payment' },
              { value: 'BANK_TRANSFER', label: '🏦 Bank Transfer' },
              { value: 'USSD', label: '📱 USSD' },
              { value: 'QR_CODE', label: '📱 QR Code' }
            ]}
          />

          <Input
            label="Description (Optional)"
            value={depositForm.description}
            onChange={(e) => setDepositForm({ ...depositForm, description: e.target.value })}
            placeholder="What is this deposit for?"
          />

          {/* Fee Information */}
          {depositForm.amount > 0 && (
            <div className="p-4 bg-gray-700 rounded-lg">
              <h4 className="font-medium text-white mb-2">Transaction Summary</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">Amount:</span>
                  <span className="text-white">{formatCurrency(depositForm.amount)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Fees:</span>
                  <span className="text-white">
                    {formatCurrency(depositsService.calculateFees(depositForm.amount, depositForm.paymentMethod).fees)}
                  </span>
                </div>
                <div className="flex justify-between border-t border-gray-600 pt-2">
                  <span className="text-white font-medium">You will receive:</span>
                  <span className="text-green-400 font-medium">
                    {formatCurrency(depositsService.calculateFees(depositForm.amount, depositForm.paymentMethod).netAmount)}
                  </span>
                </div>
              </div>
            </div>
          )}

          <div className="flex justify-end space-x-3 pt-4">
            <Button
              variant="outline"
              onClick={() => setShowDepositModal(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleInitiateDeposit}
              className="bg-green-600 hover:bg-green-700"
              disabled={processingDeposit || !depositForm.amount || depositForm.amount < 100}
            >
              {processingDeposit ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Processing...
                </div>
              ) : (
                <>
                  <FiCreditCard className="mr-2" />
                  Proceed to Payment
                </>
              )}
            </Button>
          </div>
        </div>
      </Modal>
    </DashboardLayout>
  );
}
