# Backend API Features & Endpoints Documentation

This document provides an extensive overview of the backend features, models, and API endpoints for the Kojapay Savings App. It is designed to help frontend/UI developers understand the available backend functionality and how to interact with it.

---

## Table of Contents
1. [Authentication](#authentication)
2. [User Management](#user-management)
3. [Savings Plans](#savings-plans)
4. [Group Savings](#group-savings)
5. [Target Savings](#target-savings)
6. [Deposits & Top-Ups](#deposits--top-ups)
7. [Withdrawals](#withdrawals)
8. [Transactions](#transactions)
9. [Notifications](#notifications)
10. [KYC Verification](#kyc-verification)
11. [Admin Features](#admin-features)
12. [Settings & Penalties](#settings--penalties)

---

## 1. Authentication
- **JWT-based authentication** for all protected routes.
- **Endpoints:**
  - `POST /api/auth/login` — User login
  - `POST /api/auth/signup` — User registration
  - `POST /api/auth/logout` — User logout
  - `POST /api/auth/refresh` — Refresh JWT token
- **Middleware:** `authenticateToken` protects all user routes.

## 2. User Management
- **User Model:** Stores user profile, balance, KYC status, etc.
- **Endpoints:**
  - `GET /api/user/me` — Get current user profile
  - `PUT /api/user/me` — Update profile
  - `GET /api/user/balance` — Get user balance
  - `GET /api/user/:id` — Get user by ID (admin)
  - `GET /api/user/all` — List all users (admin)

## 3. Savings Plans
- **SavingsPlan Model:** Represents individual savings plans.
- **Endpoints:**
  - `GET /api/savings` — List all available plans
  - `GET /api/savings/user` — List user's active plans
  - `POST /api/savings/join` — Join a plan
  - `POST /api/savings/create` — Create a new plan (admin)
  - `PUT /api/savings/:id` — Edit a plan (admin)
  - `DELETE /api/savings/:id` — Delete a plan (admin)
- **Fields:** name, description, minimumAmount, durationDays, interestRate, isActive, category, userId, balance, status, etc.

## 4. Group Savings
- **GroupSavings Model:** For group-based savings (ajo/esusu).
- **Endpoints:**
  - `GET /api/group-savings` — List all groups
  - `GET /api/group-savings/user` — List groups user is in
  - `POST /api/group-savings/create` — Create group
  - `POST /api/group-savings/join` — Join group
  - `POST /api/group-savings/invite` — Invite members
  - `POST /api/group-savings/contribute` — Make a group contribution
  - `GET /api/group-savings/:id` — Group details

## 5. Target Savings
- **TargetSavings Model:** For user-defined savings goals.
- **Endpoints:**
  - `GET /api/target-savings` — List user's target goals
  - `POST /api/target-savings` — Create new target goal
  - `PUT /api/target-savings/:id` — Update goal
  - `DELETE /api/target-savings/:id` — Delete goal

## 6. Deposits & Top-Ups
- **Deposit Model:** Records all deposits/top-ups.
- **Endpoints:**
  - `POST /api/deposit/initiate` — Start a deposit (Paystack)
  - `POST /api/deposit/verify` — Verify deposit
  - `GET /api/deposit/user` — List user's deposits
  - `GET /api/deposit/all` — List all deposits (admin)

## 7. Withdrawals
- **Withdraw Model:** Handles all withdrawal requests.
- **Endpoints:**
  - `POST /api/withdraw/initiate` — Withdraw to bank (Paystack, requires bank account)
  - `POST /api/withdraw/plan` — In-app withdrawal & plan closure (funds to user balance, no bank account needed)
  - `GET /api/withdraw/user/:userId` — List user's withdrawals
  - `GET /api/withdraw/all` — List all withdrawals (admin)
  - `PATCH /api/withdraw/:id` — Approve/reject withdrawal (admin)
  - `PUT /api/withdraw/:id/status` — Update withdrawal status (admin)
  - `POST /api/withdraw/webhook` — Paystack webhook for payout status
  - `GET /api/withdraw/penalty-summary` — Penalty summary (admin)
- **Features:**
  - Penalty calculation for early withdrawal (configurable)
  - In-app closure deletes plan and credits user
  - Paystack integration for external payouts

## 8. Transactions
- **Transaction Model:** Records all user/account transactions (deposits, withdrawals, penalties, refunds, etc.)
- **Endpoints:**
  - `GET /api/transactions/user` — List user's transactions
  - `GET /api/transactions/all` — List all transactions (admin)
  - `POST /api/transactions` — Create transaction (internal use)

## 9. Notifications
- **Notification Model:** Stores all user notifications (withdrawal, deposit, plan events, etc.)
- **Endpoints:**
  - `GET /api/notifications/user` — List user's notifications
  - `POST /api/notifications` — Create notification
  - `PUT /api/notifications/:id/read` — Mark as read

## 10. KYC Verification
- **KYC Model:** Handles user KYC documents and status.
- **Endpoints:**
  - `POST /api/kyc/upload` — Upload KYC document
  - `GET /api/kyc/user` — Get user's KYC status
  - `GET /api/kyc/all` — List all KYC records (admin)
  - `PATCH /api/kyc/:id` — Approve/reject KYC (admin)

## 11. Admin Features
- **Admin endpoints** for managing users, plans, withdrawals, KYC, settings, and more.
- **Endpoints:**
  - `GET /api/admin/users` — List all users
  - `GET /api/admin/withdrawals` — List all withdrawals
  - `GET /api/admin/kyc` — List all KYC records
  - `GET /api/admin/settings` — View/edit global settings

## 12. Settings & Penalties
- **Settings Model:** Stores global app settings (withdrawal penalty %, etc.)
- **Endpoints:**
  - `GET /api/settings` — Get all settings
  - `PUT /api/settings/:key` — Update a setting (admin)

---

## Notes for UI Development
- All endpoints return JSON.
- Most endpoints require authentication (JWT in `Authorization` header).
- Error responses are standardized with `error` and `details` fields.
- Plan closure via `/withdraw/plan` deletes the plan and credits user balance.
- Withdrawals via `/withdraw/initiate` require a linked bank account.
- Notifications are sent for all major user/account events.
- Admin endpoints are protected and only accessible to admin users.

---


---

## Admin Features & UI/UX Recommendations

### Admin Dashboard Features
- **User Management:** View, search, filter, and manage all users. Actions: view profile, reset password, suspend/activate, view KYC status, see all user plans/transactions.
- **Savings Plan Management:** Create, edit, activate/deactivate, or delete any plan. See all users in a plan, plan performance, and plan closure history.
- **Withdrawals:** Approve/reject withdrawals, see withdrawal history, filter by status/user/date, and view penalty summaries. All actions are logged for audit.
- **KYC Management:** Review, approve, or reject KYC submissions. See document previews and user KYC history.
- **Notifications:** Send system-wide or targeted notifications to users. View notification logs.
- **Settings:** Adjust global app settings (penalties, interest rates, etc.) with audit trail.
- **Audit Logs:** View all admin actions for compliance and troubleshooting.
- **Analytics:** Dashboard with charts for deposits, withdrawals, active users, plan performance, and more.

### Admin UI/UX Best Practices
- Use a sidebar with clear navigation for Users, Plans, Withdrawals, KYC, Settings, Analytics, and Audit Logs.
- Data tables should support filtering, sorting, and bulk actions.
- Use modals for sensitive actions (e.g., approve/reject, delete, edit settings).
- Show status badges (e.g., KYC: Pending/Approved/Rejected, Withdrawal: Pending/Success/Failed).
- Provide search and quick actions for all major entities.
- Responsive design for desktop and tablet.

---

## User Dashboard Features & UI/UX Recommendations

### User Dashboard Features
- **Savings Plans:** List all active and past plans, with status, balance, and actions (withdraw, close, view details).
- **Withdrawals:** Initiate in-app or Paystack withdrawals, see withdrawal history, and view penalty info before confirming.
- **Deposits:** Initiate deposits, see deposit history, and verify deposit status.
- **Group Savings:** Join, create, or contribute to group savings. See group details and member list.
- **Target Savings:** Set, edit, or delete personal savings goals. Track progress visually.
- **Transactions:** View all account transactions with filters for type/date.
- **Notifications:** See all notifications, mark as read, and get real-time updates.
- **Profile & KYC:** View/edit profile, upload KYC documents, and see KYC status.
- **Payments:** Manage linked bank accounts, see payment history, and add new accounts.

### User UI/UX Best Practices
- Sidebar navigation for Savings, Group Savings, Target Savings, Payments, Transactions, Notifications, and Profile.
- Use cards for plans/goals with clear CTAs (withdraw, deposit, close, etc.).
- Show progress bars for savings goals and group contributions.
- Use modals for withdrawals, deposits, and KYC uploads.
- Display penalty/interest info contextually (e.g., before withdrawal confirmation).
- Real-time feedback for all actions (success, error, pending states).
- Mobile-first responsive design.

---

## How Dope Should the UI Be?

- **Modern, Clean, and Intuitive:** Use a modern design system (e.g., Tailwind, Material UI) with consistent colors, spacing, and typography.
- **Vibrant & Trustworthy:** Use a color palette that feels vibrant but trustworthy (blues, greens, golds). Avoid clutter.
- **Microinteractions:** Add subtle animations for actions (button clicks, loading, success/failure states).
- **Visual Hierarchy:** Make key actions (withdraw, deposit, join group) stand out. Use icons and badges for clarity.
- **Charts & Progress:** Use charts for analytics and progress bars for goals. Make financial data easy to scan.
- **Accessibility:** Ensure good contrast, keyboard navigation, and screen reader support.
- **Mobile Experience:** The mobile UI should feel like a native app—fast, touch-friendly, and with bottom navigation for key actions.
- **Delightful Touches:** Add celebratory animations for milestones (e.g., goal achieved, plan matured) and friendly empty states.

> **The UI should feel as dope as a modern fintech app—think Chime, Monzo, or Cash App. It should inspire trust, make users feel in control, and be a joy to use.**

---

For more details on request/response payloads, see the backend code or request specific endpoint samples.
---

## Admin Features: Power & UI Recommendations

### Admin Capabilities
- **User Management:** View, search, filter, edit, and deactivate users. See KYC status, balances, and activity logs.
- **Plan Management:** Create, edit, activate/deactivate, or delete any savings plan. See plan stats, user participation, and plan performance.
- **Withdrawal Oversight:** Approve/reject withdrawals, see penalty summaries, and monitor payout statuses. Manual override for failed payouts.
- **KYC Management:** Review, approve, or reject user KYC submissions. View uploaded documents and user history.
- **Settings:** Adjust global app settings (penalties, interest rates, etc.) with audit logging.
- **Notifications:** Send system-wide or targeted notifications to users.
- **Analytics:** Access dashboards for total savings, active users, interest paid, penalties collected, and more. Filter by date, plan, or user.
- **Audit Logs:** Track all admin actions for security and compliance.

### UI/UX Recommendations for Admin
- **Modern, Responsive Dashboard:** Use cards, charts, and tables for at-a-glance stats. Prioritize clarity and speed.
- **Bulk Actions:** Enable multi-select for user/plan management (e.g., approve multiple withdrawals).
- **Search & Filters:** Powerful search and filtering on all lists (users, plans, withdrawals, KYC, etc.).
- **Real-Time Updates:** Use websockets or polling for real-time status on withdrawals, KYC, and notifications.
- **Role-Based Access:** Support for multiple admin roles (super admin, support, finance, etc.).
- **Dark Mode:** Full support for dark/light themes.
- **Accessibility:** Ensure all controls are accessible (keyboard, screen reader, color contrast).
- **Mobile Friendly:** Admins should be able to manage on the go.

### How Dope the UI Should Be
- **Visually Stunning:** Use brand colors, gradients, and subtle animations for a premium feel.
- **Intuitive Navigation:** Sidebar with clear categories (Dashboard, Users, Plans, Withdrawals, KYC, Settings, Analytics, Notifications).
- **Instant Feedback:** Toasts, modals, and inline validation for all actions.
- **Data Visualization:** Charts for trends, pie charts for breakdowns, and progress bars for goals.
- **User Avatars & Badges:** Show user avatars, KYC badges, and plan icons for quick recognition.
- **Actionable Insights:** Highlight anomalies (e.g., spike in withdrawals, failed KYC) with alerts.
- **Customizable Widgets:** Allow admins to personalize their dashboard layout.
- **Seamless Transitions:** Use smooth transitions and loading skeletons for a polished experience.

---

## Final Notes
- The backend is designed for extensibility—new features (e.g., loan products, referral bonuses) can be added easily.
- All endpoints are RESTful and return clear, consistent JSON responses.
- The UI should empower both users and admins, making financial management easy, transparent, and even fun.

For any endpoint or feature, request a sample payload or workflow for UI implementation.
