"use client";

import React, { useState, useEffect } from 'react';
// Type for savings summary from /api/savings/summary
type SavingsSummary = {
  userId: string;
  totalPlans: number;
  activePlans: number;
  completedPlans: number;
  totalSaved: number;
  totalInterestEarned: number;
  totalTargetAmount: number;
  savingsProgress: number;
};
import { motion } from 'framer-motion';
// Removed auth imports for demo mode
import CascadeSidebar from '../../../src/components/dashboard/CascadeSidebar';
import { StatCard, ActionCard } from '../../../src/components/dashboard/DashboardCard';
import { FormModal } from '../../../src/components/ui/Modal';
import { FormInput, SelectInput } from '../../../src/components/ui/FormInput';
import Table, { StatusBadge, ActionButton } from '../../../src/components/ui/Table';
import { Card3D, StatCard3D } from '../../../src/components/ui/Card3D';
import { HeroText3D, Heading3D, Subheading3D } from '../../../src/components/ui/Text3D';
import { PrimaryButton, SecondaryButton } from '../../../src/components/ui/ThemedButton';
import { useTheme, getThemeClasses } from '../../../src/contexts/ThemeContext';
import { 
  FiPlus, 
  FiDollarSign, 
  FiTarget, 
  FiTrendingUp,
  FiEdit,
  FiTrash2,
  FiPause,
  FiPlay,
  FiEye
} from 'react-icons/fi';

interface SavingsPlan {
  id: string;
  name: string;
  planType: 'INDIVIDUAL' | 'TARGET' | 'GOAL';
  targetAmount: number;
  currentAmount: number;
  contributionAmount: number;
  frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY';
  status: 'ACTIVE' | 'PAUSED' | 'COMPLETED' | 'CANCELLED';
  startDate: string;
  endDate: string;
  interestRate: number;
  progress: number;
}

// Remove mockPlans, fetch from backend


import { savingsService } from '../../../src/services/savings';

export default function SavingsPlansPage() {
  const { theme } = useTheme();
  const themeClasses = getThemeClasses(theme);
  const [plans, setPlans] = useState<SavingsPlan[]>([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    planType: 'INDIVIDUAL',
    targetAmount: '',
    contributionAmount: '',
    frequency: 'MONTHLY',
    duration: '12',
  });
  const [savingsSummary, setSavingsSummary] = useState<SavingsSummary | null>(null);

  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      try {
        // Fetch user savings plans and summary from /api/savings/my
        const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
        console.log('[SAVINGS PLANS PAGE] Fetching user savings plans and summary...');
        const res = await fetch(`${API_BASE_URL}/api/savings/my`, {
          headers: { 'Authorization': `Bearer ${localStorage.getItem('auth_token')}` }
        });
        if (!res.ok) {
          throw new Error(`[SAVINGS PLANS PAGE] Failed to fetch /api/savings/my: ${res.status} ${await res.text()}`);
        }
        const data = await res.json();
        // Defensive: fallback if summary or plans missing
        setSavingsSummary(data.summary || null);
        const plansWithProgress = (data.plans || []).map(plan => ({
          ...plan,
          progress: plan.targetAmount > 0 ? Math.round((plan.currentAmount / plan.targetAmount) * 100) : 0,
        }));
        setPlans(plansWithProgress);
      } catch (err) {
        console.error('[SAVINGS PLANS PAGE] Failed to fetch data:', err);
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, []);

  // Removed auth checks for demo mode

  const handleCreatePlan = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      // Prepare backend payload with required fields
      const payload = {
        name: formData.name,
        planType: formData.planType as 'INDIVIDUAL' | 'TARGET' | 'GOAL',
        targetAmount: parseFloat(formData.targetAmount),
        contributionAmount: parseFloat(formData.contributionAmount),
        frequency: formData.frequency as 'DAILY' | 'WEEKLY' | 'MONTHLY',
        duration: parseInt(formData.duration),
        autoDebit: false,
        startDate: new Date().toISOString().split('T')[0],
      };
      console.log('[SAVINGS PLANS PAGE] Creating savings plan with payload:', payload);
      const created = await savingsService.createSavingsPlan(payload);
      console.log('[SAVINGS PLANS PAGE] Created plan:', created);
      setPlans(prev => [
        ...prev,
        {
          ...created,
          progress: created.targetAmount > 0 ? Math.round((created.currentAmount / created.targetAmount) * 100) : 0,
        },
      ]);
      setShowCreateModal(false);
      setFormData({
        name: '',
        planType: 'INDIVIDUAL',
        targetAmount: '',
        contributionAmount: '',
        frequency: 'MONTHLY',
        duration: '12',
      });
    } catch (error) {
      console.error('[SAVINGS PLANS PAGE] Failed to create plan:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const columns = [
    {
      key: 'name' as keyof SavingsPlan,
      title: 'Plan Name',
      sortable: true,
    },
    {
      key: 'planType' as keyof SavingsPlan,
      title: 'Type',
      render: (value: string) => (
        <StatusBadge 
          status={value} 
          variant={value === 'TARGET' ? 'info' : value === 'GOAL' ? 'warning' : 'default'} 
        />
      ),
    },
    {
      key: 'currentAmount' as keyof SavingsPlan,
      title: 'Current Amount',
      render: (value: number) => `₦${value.toLocaleString()}`,
    },
    {
      key: 'targetAmount' as keyof SavingsPlan,
      title: 'Target Amount',
      render: (value: number) => `₦${value.toLocaleString()}`,
    },
    {
      key: 'progress' as keyof SavingsPlan,
      title: 'Progress',
      render: (value: number) => (
        <div className="flex items-center space-x-2">
          <div className="w-16 bg-gray-700 rounded-full h-2">
            <div 
              className="bg-green-500 h-2 rounded-full" 
              style={{ width: `${value}%` }}
            />
          </div>
          <span className="text-sm">{value}%</span>
        </div>
      ),
    },
    {
      key: 'status' as keyof SavingsPlan,
      title: 'Status',
      render: (value: string) => (
        <StatusBadge 
          status={value} 
          variant={
            value === 'ACTIVE' ? 'success' : 
            value === 'PAUSED' ? 'warning' : 
            value === 'COMPLETED' ? 'info' : 'danger'
          } 
        />
      ),
    },
    {
      key: 'id' as keyof SavingsPlan,
      title: 'Actions',
      render: (value: string, row: SavingsPlan) => (
        <div className="flex space-x-2">
          <ActionButton onClick={() => console.log('View', value)} variant="default">
            <FiEye className="w-4 h-4" />
          </ActionButton>
          <ActionButton onClick={() => console.log('Edit', value)} variant="primary">
            <FiEdit className="w-4 h-4" />
          </ActionButton>
          <ActionButton 
            onClick={() => console.log(row.status === 'ACTIVE' ? 'Pause' : 'Resume', value)} 
            variant="default"
          >
            {row.status === 'ACTIVE' ? <FiPause className="w-4 h-4" /> : <FiPlay className="w-4 h-4" />}
          </ActionButton>
          <ActionButton onClick={() => console.log('Delete', value)} variant="danger">
            <FiTrash2 className="w-4 h-4" />
          </ActionButton>
        </div>
      ),
    },
  ];

  // Use dashboard logic: totalSavings = savingsSummary.totalTargetAmount
  const totalSavings = savingsSummary && typeof savingsSummary.totalTargetAmount === 'number' ? savingsSummary.totalTargetAmount : 0;
  // Use dashboard logic for active and completed plans
  const activePlans = savingsSummary && typeof savingsSummary.activePlans === 'number' ? savingsSummary.activePlans : 0;
  const completedPlans = savingsSummary && typeof savingsSummary.completedPlans === 'number' ? savingsSummary.completedPlans : 0;
  // Calculate total scheduled monthly contribution from all plans with frequency 'MONTHLY'
  const totalMonthlyContribution = plans
    .filter(plan => plan.frequency === 'MONTHLY')
    .reduce((sum, plan) => sum + (plan.contributionAmount || 0), 0);

  // Removed loading and auth checks for demo mode

  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      <div className="space-y-8">
        {/* Hero Section */}
        <div className={`relative overflow-hidden rounded-2xl ${themeClasses.bg.card} border ${themeClasses.border.accent} p-8`}>
          <div className="absolute inset-0 bg-gradient-to-r from-green-500/10 to-blue-500/10"></div>
          <div className="relative z-10">
            <HeroText3D
              as="h1"
              color="gradient"
              intensity="strong"
              className="mb-4"
            >
              Savings Plans
            </HeroText3D>
            <Subheading3D
              as="p"
              className={`${themeClasses.text.secondary} mb-6`}
            >
              Manage your individual and group savings plans with better interest rates.
            </Subheading3D>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <StatCard3D
            title="Total Savings"
            value={`₦${totalSavings.toLocaleString()}`}
            subtitle="Across all plans"
            icon={FiDollarSign}
            color="green"
          />
          <StatCard3D
            title="Active Plans"
            value={activePlans.toString()}
            subtitle={`${completedPlans} completed`}
            icon={FiTarget}
            color="blue"
          />
          <StatCard3D
            title="Monthly Contribution"
            value={`₦${totalMonthlyContribution.toLocaleString()}`}
            subtitle="Total scheduled"
            icon={FiTrendingUp}
            color="purple"
          />
        </div>

        {/* Actions */}
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold text-white">Your Savings Plans</h2>
          <button
            onClick={() => setShowCreateModal(true)}
            className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
          >
            <FiPlus className="w-4 h-4" />
            <span>Create Plan</span>
          </button>
        </div>

        {/* Plans Table */}
        <Table
          data={plans}
          columns={columns}
          searchable
          searchPlaceholder="Search plans..."
          emptyMessage="No savings plans found. Create your first plan to get started!"
        />

        {/* Create Plan Modal */}
        <FormModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          title="Create Savings Plan"
          onSubmit={handleCreatePlan}
          submitText="Create Plan"
          isLoading={loading}
        >
          <div className="space-y-4">
            <FormInput
              label="Plan Name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="e.g., Emergency Fund"
              required
            />

            <SelectInput
              label="Plan Type"
              name="planType"
              value={formData.planType}
              onChange={handleInputChange}
              options={[
                { value: 'INDIVIDUAL', label: 'Individual Savings' },
                { value: 'TARGET', label: 'Target Savings' },
                { value: 'GROUP', label: 'Group Savings' },
              ]}
              required
            />

            <FormInput
              label="Target Amount"
              name="targetAmount"
              type="number"
              value={formData.targetAmount}
              onChange={handleInputChange}
              placeholder="1000000"
              required
            />

            <FormInput
              label="Contribution Amount"
              name="contributionAmount"
              type="number"
              value={formData.contributionAmount}
              onChange={handleInputChange}
              placeholder="50000"
              required
            />

            <SelectInput
              label="Frequency"
              name="frequency"
              value={formData.frequency}
              onChange={handleInputChange}
              options={[
                { value: 'DAILY', label: 'Daily' },
                { value: 'WEEKLY', label: 'Weekly' },
                { value: 'MONTHLY', label: 'Monthly' },
              ]}
              required
            />

            <FormInput
              label="Duration (months)"
              name="duration"
              type="number"
              value={formData.duration}
              onChange={handleInputChange}
              placeholder="12"
              required
            />
          </div>
        </FormModal>
      </div>
    </div>
  );
}
