#!/bin/bash

# BetterInterest Deployment Script
echo "🚀 Starting BetterInterest deployment to Vercel..."

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found. Please run this script from the project root."
    exit 1
fi

# Check if git is initialized
if [ ! -d ".git" ]; then
    echo "📦 Initializing git repository..."
    git init
fi

# Add all changes
echo "📝 Adding all changes to git..."
git add .

# Commit changes
echo "💾 Committing changes..."
git commit -m "feat: rebrand to BetterInterest - ready for Vercel deployment

- Updated app name and branding to BetterInterest
- Added new animated logo with percentage icon
- Updated hero section and marketing copy
- Created comprehensive demo credentials
- Configured Vercel deployment settings
- Added production environment variables
- Ready for live deployment"

# Check if remote origin exists
if ! git remote get-url origin > /dev/null 2>&1; then
    echo "🔗 Adding remote origin..."
    git remote add origin https://github.com/Koja-Pay/latestgreenbetterinterestapp.git
fi

# Push to GitHub
echo "⬆️ Pushing to GitHub..."
git branch -M main
git push -u origin main

# Check if Vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    echo "📦 Installing Vercel CLI..."
    npm install -g vercel
fi

# Login to Vercel (if not already logged in)
echo "🔐 Checking Vercel authentication..."
if ! vercel whoami > /dev/null 2>&1; then
    echo "Please login to Vercel:"
    vercel login
fi

# Deploy to Vercel
echo "🚀 Deploying to Vercel..."
vercel --prod --confirm

# Get deployment URL
DEPLOYMENT_URL=$(vercel ls | grep "betterinterest" | head -1 | awk '{print $2}')

echo ""
echo "🎉 Deployment completed successfully!"
echo ""
echo "📱 Your BetterInterest app is now live at:"
echo "🔗 https://$DEPLOYMENT_URL"
echo ""
echo "🔐 Demo Credentials:"
echo "👤 User: <EMAIL> / Demo123!@#"
echo "👨‍💼 Admin: <EMAIL> / Admin123!@#"
echo ""
echo "📚 Next Steps:"
echo "1. Visit the live URL to test the application"
echo "2. Login with demo credentials"
echo "3. Test all features and real-time functionality"
echo "4. Share with stakeholders for feedback"
echo "5. Monitor performance in Vercel dashboard"
echo ""
echo "📖 For detailed information, see:"
echo "- VERCEL-DEPLOYMENT-GUIDE.md"
echo "- DEMO-CREDENTIALS.md"
echo ""
echo "🎊 Happy testing!"
