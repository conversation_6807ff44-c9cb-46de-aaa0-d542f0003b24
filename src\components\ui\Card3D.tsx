"use client";

import React, { useRef } from 'react';
import { motion, useMotionValue, useSpring, useTransform } from 'framer-motion';
import { useTheme, getThemeClasses } from '../../contexts/ThemeContext';

interface Card3DProps {
  children: React.ReactNode;
  className?: string;
  intensity?: 'light' | 'medium' | 'strong';
  glowEffect?: boolean;
  hoverScale?: boolean;
  borderGradient?: boolean;
  elevation?: 1 | 2 | 3 | 4 | 5;
}

export function Card3D({
  children,
  className = '',
  intensity = 'medium',
  glowEffect = true,
  hoverScale = true,
  borderGradient = false,
  elevation = 2
}: Card3DProps) {
  const { theme } = useTheme();
  const themeClasses = getThemeClasses(theme);
  const ref = useRef<HTMLDivElement>(null);

  const x = useMotionValue(0);
  const y = useMotionValue(0);

  const mouseXSpring = useSpring(x);
  const mouseYSpring = useSpring(y);

  const rotateX = useTransform(
    mouseYSpring,
    [-0.5, 0.5],
    intensity === 'light' ? [-5, 5] : intensity === 'medium' ? [-10, 10] : [-15, 15]
  );
  const rotateY = useTransform(
    mouseXSpring,
    [-0.5, 0.5],
    intensity === 'light' ? [-5, 5] : intensity === 'medium' ? [-10, 10] : [-15, 15]
  );

  // Material Design elevation system
  const getMaterialElevation = (level: number, isHovered: boolean = false) => {
    const elevationLevel = isHovered ? Math.min(level + 2, 5) : level;
    const elevations = {
      1: theme === 'light'
        ? '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)'
        : '0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.5)',
      2: theme === 'light'
        ? '0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23)'
        : '0 3px 6px rgba(0, 0, 0, 0.4), 0 3px 6px rgba(0, 0, 0, 0.6)',
      3: theme === 'light'
        ? '0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23)'
        : '0 10px 20px rgba(0, 0, 0, 0.5), 0 6px 6px rgba(0, 0, 0, 0.7)',
      4: theme === 'light'
        ? '0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22)'
        : '0 14px 28px rgba(0, 0, 0, 0.6), 0 10px 10px rgba(0, 0, 0, 0.8)',
      5: theme === 'light'
        ? '0 19px 38px rgba(0, 0, 0, 0.30), 0 15px 12px rgba(0, 0, 0, 0.22)'
        : '0 19px 38px rgba(0, 0, 0, 0.7), 0 15px 12px rgba(0, 0, 0, 0.9)',
    };
    return elevations[elevationLevel as keyof typeof elevations] || elevations[2];
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!ref.current) return;

    const rect = ref.current.getBoundingClientRect();
    const width = rect.width;
    const height = rect.height;

    const mouseX = (e.clientX - rect.left) / width - 0.5;
    const mouseY = (e.clientY - rect.top) / height - 0.5;

    x.set(mouseX);
    y.set(mouseY);
  };

  const handleMouseLeave = () => {
    x.set(0);
    y.set(0);
  };

  const baseClasses = `
    relative rounded-xl overflow-hidden transition-all duration-300 group
    ${themeClasses.bg.card}
    ${themeClasses.border.primary}
    ${borderGradient ? 'border-2 border-transparent bg-gradient-to-r from-green-400/20 to-blue-400/20 p-[1px]' : 'border'}
    ${className}
  `;

  const cardContent = (
    <motion.div
      ref={ref}
      className={baseClasses}
      style={{
        rotateY,
        rotateX,
        transformStyle: "preserve-3d",
        boxShadow: getMaterialElevation(elevation),
      }}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      whileHover={hoverScale ? {
        scale: 1.02,
        boxShadow: getMaterialElevation(elevation, true),
        transition: { duration: 0.2, ease: "easeOut" }
      } : {}}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
    >
      {/* Enhanced Material Design glow effect */}
      {glowEffect && (
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-green-400/5 to-blue-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-xl blur-xl"
          initial={{ scale: 0.8 }}
          whileHover={{ scale: 1.1 }}
          transition={{ duration: 0.3 }}
        />
      )}

      {/* Material Design ripple effect */}
      <motion.div
        className="absolute inset-0 bg-green-400/10 opacity-0 group-hover:opacity-20 rounded-xl"
        initial={{ scale: 0 }}
        whileHover={{ scale: 1 }}
        transition={{ duration: 0.4, ease: "easeOut" }}
      />

      {/* Card content */}
      <div className={`relative z-10 ${borderGradient ? `${themeClasses.bg.card} rounded-xl` : ''}`}>
        {children}
      </div>

      {/* Enhanced shine effect */}
      <motion.div
        className={`absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 ${
          theme === 'dark' ? 'via-white/5' : 'via-white/10'
        }`}
        initial={{ x: '-100%' }}
        whileHover={{ x: '100%' }}
        transition={{ duration: 0.6, ease: "easeInOut" }}
      />
    </motion.div>
  );

  return borderGradient ? (
    <div className="group">
      {cardContent}
    </div>
  ) : (
    <div className="group">
      {cardContent}
    </div>
  );
}

// Specialized card variants
export function StatCard3D({
  title,
  value,
  subtitle,
  icon: Icon,
  color = 'green',
  className = ''
}: {
  title: string;
  value: string;
  subtitle?: string;
  icon?: React.ComponentType<any>;
  color?: 'green' | 'blue' | 'purple' | 'yellow';
  className?: string;
}) {
  const { theme } = useTheme();
  const themeClasses = getThemeClasses(theme);

  const colorClasses = {
    green: 'from-green-400 to-green-600',
    blue: 'from-blue-400 to-blue-600',
    purple: 'from-purple-400 to-purple-600',
    yellow: 'from-yellow-400 to-yellow-600'
  };

  return (
    <Card3D className={`p-6 ${className}`} glowEffect borderGradient>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className={`text-sm font-medium ${themeClasses.text.secondary}`}>
            {title}
          </p>
          <p className={`text-2xl font-bold ${themeClasses.text.primary} mt-1`}>
            {value}
          </p>
          {subtitle && (
            <p className={`text-xs ${themeClasses.text.tertiary} mt-1`}>
              {subtitle}
            </p>
          )}
        </div>
        {Icon && (
          <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${colorClasses[color]} flex items-center justify-center`}>
            <Icon className="w-6 h-6 text-white" />
          </div>
        )}
      </div>
    </Card3D>
  );
}

export function ActionCard3D({
  title,
  subtitle,
  icon: Icon,
  color = 'green',
  onClick,
  className = ''
}: {
  title: string;
  subtitle: string;
  icon?: React.ComponentType<any>;
  color?: 'green' | 'blue' | 'purple' | 'yellow';
  onClick?: () => void;
  className?: string;
}) {
  const { theme } = useTheme();
  const themeClasses = getThemeClasses(theme);

  const colorClasses = {
    green: 'from-green-400 to-green-600',
    blue: 'from-blue-400 to-blue-600',
    purple: 'from-purple-400 to-purple-600',
    yellow: 'from-yellow-400 to-yellow-600'
  };

  return (
    <Card3D 
      className={`p-6 cursor-pointer hover:shadow-lg transition-shadow ${className}`} 
      onClick={onClick}
      hoverScale
    >
      <div className="text-center">
        {Icon && (
          <div className={`w-16 h-16 rounded-full bg-gradient-to-r ${colorClasses[color]} flex items-center justify-center mx-auto mb-4`}>
            <Icon className="w-8 h-8 text-white" />
          </div>
        )}
        <h3 className={`text-lg font-semibold ${themeClasses.text.primary} mb-2`}>
          {title}
        </h3>
        <p className={`text-sm ${themeClasses.text.secondary}`}>
          {subtitle}
        </p>
      </div>
    </Card3D>
  );
}

export default Card3D;
