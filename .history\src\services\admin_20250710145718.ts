import { 
  AdminDashboardStats, 
  AdminAnalytics,
  AdminUser,
  AdminActivity,
  AdminSettings,
  SystemHealth,
  AdminReport,
  CreateAdminReportData,
  AdminSearchFilters,
  AdminActivityFilters,
  ApiResponse
} from '../types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;

class AdminService {
  private getAuthHeaders() {
    const token = localStorage.getItem('auth_token');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` })
    };
  }

  // Dashboard and Analytics
  async getDashboardStats(filters?: {
    dateFrom?: string;
    dateTo?: string;
    period?: 'today' | 'week' | 'month' | 'year';
  }): Promise<AdminDashboardStats> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/admin/dashboard/stats?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch dashboard statistics');
    }

    return response.json();
  }

  async getAnalytics(period: 'daily' | 'weekly' | 'monthly' | 'yearly' = 'monthly'): Promise<AdminAnalytics> {
    const response = await fetch(`${API_BASE_URL}/admin/analytics?period=${period}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch analytics');
    }

    return response.json();
  }

  async getRevenueAnalytics(filters?: {
    dateFrom?: string;
    dateTo?: string;
    breakdown?: 'daily' | 'weekly' | 'monthly';
  }): Promise<any> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/admin/analytics/revenue?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch revenue analytics');
    }

    return response.json();
  }

  // User Management
  async getAllUsers(filters?: AdminSearchFilters): Promise<any> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/admin/users?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch users');
    }

    return response.json();
  }

  async getUserDetails(userId: string): Promise<any> {
    const response = await fetch(`${API_BASE_URL}/admin/users/${userId}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch user details');
    }

    return response.json();
  }

  async updateUserStatus(userId: string, isActive: boolean, reason?: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/admin/users/${userId}/status`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ isActive, reason })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update user status');
    }
  }

  async resetUserPassword(userId: string): Promise<{ temporaryPassword: string }> {
    const response = await fetch(`${API_BASE_URL}/admin/users/${userId}/reset-password`, {
      method: 'POST',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to reset user password');
    }

    return response.json();
  }

  // Savings Plan Management
  async getAllSavingsPlans(filters?: any): Promise<any> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/admin/savings/plans?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch savings plans');
    }

    return response.json();
  }

  async createSavingsPlan(planData: any): Promise<any> {
    const response = await fetch(`${API_BASE_URL}/admin/savings/plans`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(planData)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to create savings plan');
    }

    return response.json();
  }

  async updateSavingsPlan(planId: string, planData: any): Promise<any> {
    const response = await fetch(`${API_BASE_URL}/admin/savings/plans/${planId}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(planData)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update savings plan');
    }

    return response.json();
  }

  async deleteSavingsPlan(planId: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/admin/savings/plans/${planId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to delete savings plan');
    }
  }

  // Withdrawal Management
  async getAllWithdrawals(filters?: any): Promise<any> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/admin/withdrawals?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch withdrawals');
    }

    return response.json();
  }

  async approveWithdrawal(withdrawalId: string, notes?: string): Promise<any> {
    const response = await fetch(`${API_BASE_URL}/admin/withdrawals/${withdrawalId}/approve`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ notes })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to approve withdrawal');
    }

    return response.json();
  }

  async rejectWithdrawal(withdrawalId: string, reason: string, notes?: string): Promise<any> {
    const response = await fetch(`${API_BASE_URL}/admin/withdrawals/${withdrawalId}/reject`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ reason, notes })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to reject withdrawal');
    }

    return response.json();
  }

  // KYC Management
  async getAllKYC(filters?: any): Promise<any> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/admin/kyc?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch KYC records');
    }

    return response.json();
  }

  async reviewKYC(kycId: string, status: 'APPROVED' | 'REJECTED', notes?: string, reason?: string): Promise<any> {
    const response = await fetch(`${API_BASE_URL}/admin/kyc/${kycId}/review`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ status, notes, reason })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to review KYC');
    }

    return response.json();
  }

  // Settings Management
  async getSettings(): Promise<AdminSettings[]> {
    const response = await fetch(`${API_BASE_URL}/admin/settings`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch settings');
    }

    return response.json();
  }

  async updateSetting(key: string, value: any): Promise<AdminSettings> {
    const response = await fetch(`${API_BASE_URL}/admin/settings/${key}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ value })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update setting');
    }

    return response.json();
  }

  async bulkUpdateSettings(settings: { key: string; value: any }[]): Promise<AdminSettings[]> {
    const response = await fetch(`${API_BASE_URL}/admin/settings/bulk`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ settings })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update settings');
    }

    return response.json();
  }

  // System Health and Monitoring
  async getSystemHealth(): Promise<SystemHealth> {
    const response = await fetch(`${API_BASE_URL}/admin/system/health`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch system health');
    }

    return response.json();
  }

  async getSystemLogs(filters?: {
    level?: 'INFO' | 'WARNING' | 'ERROR' | 'CRITICAL';
    dateFrom?: string;
    dateTo?: string;
    service?: string;
    page?: number;
    limit?: number;
  }): Promise<any> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/admin/system/logs?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch system logs');
    }

    return response.json();
  }

  // Admin Activity Tracking
  async getAdminActivity(filters?: AdminActivityFilters): Promise<any> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/admin/activity?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch admin activity');
    }

    return response.json();
  }

  async logAdminActivity(action: string, resource: string, resourceId?: string, metadata?: any): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/admin/activity`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ action, resource, resourceId, metadata })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to log admin activity');
    }
  }

  // Report Generation
  async generateReport(data: CreateAdminReportData): Promise<AdminReport> {
    const response = await fetch(`${API_BASE_URL}/admin/reports`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to generate report');
    }

    return response.json();
  }

  async getReports(filters?: {
    type?: string;
    status?: string;
    dateFrom?: string;
    dateTo?: string;
    page?: number;
    limit?: number;
  }): Promise<any> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/admin/reports?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch reports');
    }

    return response.json();
  }

  async downloadReport(reportId: string): Promise<Blob> {
    const response = await fetch(`${API_BASE_URL}/admin/reports/${reportId}/download`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to download report');
    }

    return response.blob();
  }

  // Utility Methods
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN'
    }).format(amount);
  }

  formatPercentage(value: number): string {
    return `${value.toFixed(2)}%`;
  }

  calculateGrowthRate(current: number, previous: number): number {
    if (previous === 0) return current > 0 ? 100 : 0;
    return ((current - previous) / previous) * 100;
  }

  getStatusColor(status: string): string {
    const colors: Record<string, string> = {
      ACTIVE: '#10B981',
      INACTIVE: '#6B7280',
      PENDING: '#F59E0B',
      APPROVED: '#10B981',
      REJECTED: '#EF4444',
      COMPLETED: '#10B981',
      FAILED: '#EF4444',
      CANCELLED: '#6B7280'
    };
    
    return colors[status] || '#6B7280';
  }

  exportToCSV(data: any[], filename: string): void {
    const csvContent = this.convertToCSV(data);
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }

  private convertToCSV(data: any[]): string {
    if (!data.length) return '';
    
    const headers = Object.keys(data[0]);
    const csvRows = [
      headers.join(','),
      ...data.map(row => 
        headers.map(header => {
          const value = row[header];
          return typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : value;
        }).join(',')
      )
    ];
    
    return csvRows.join('\n');
  }
}

export const adminService = new AdminService();
