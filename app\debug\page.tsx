"use client";

import React, { useState, useEffect } from 'react';
import { useTheme } from '../../src/contexts/ThemeContext';

export default function DebugPage() {
  const [mounted, setMounted] = useState(false);
  const [screenSize, setScreenSize] = useState({ width: 0, height: 0 });
  const { theme, toggleTheme } = useTheme();

  useEffect(() => {
    setMounted(true);
    const updateScreenSize = () => {
      setScreenSize({ width: window.innerWidth, height: window.innerHeight });
    };
    updateScreenSize();
    window.addEventListener('resize', updateScreenSize);
    return () => window.removeEventListener('resize', updateScreenSize);
  }, []);

  if (!mounted) {
    return <div>Loading...</div>;
  }

  return (
    <div className={`min-h-screen p-8 ${
      theme === 'light' ? 'bg-white text-black' : 'bg-black text-white'
    }`}>
      <h1 className="text-3xl font-bold mb-6">Debug Information</h1>
      
      <div className="space-y-4">
        <div>
          <strong>Current Theme:</strong> {theme}
        </div>
        
        <div>
          <strong>Screen Size:</strong> {screenSize.width} x {screenSize.height}
        </div>
        
        <div>
          <strong>Is Large Screen (≥1024px):</strong> {screenSize.width >= 1024 ? 'Yes' : 'No'}
        </div>
        
        <button
          onClick={toggleTheme}
          className={`px-4 py-2 rounded ${
            theme === 'light' 
              ? 'bg-gray-800 text-white hover:bg-gray-700' 
              : 'bg-white text-black hover:bg-gray-200'
          }`}
        >
          Toggle Theme
        </button>
        
        <div className="mt-8">
          <h2 className="text-xl font-semibold mb-4">Responsive Test</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div className={`p-4 rounded ${
              theme === 'light' ? 'bg-gray-100' : 'bg-gray-800'
            }`}>
              <h3 className="font-semibold">Always Visible</h3>
              <p>This content is always visible</p>
            </div>
            <div className={`p-4 rounded lg:block hidden ${
              theme === 'light' ? 'bg-blue-100' : 'bg-blue-900'
            }`}>
              <h3 className="font-semibold">Large Screen Only</h3>
              <p>This content is only visible on large screens (lg:block hidden)</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
