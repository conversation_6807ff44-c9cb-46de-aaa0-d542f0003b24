import { userService } from '../user';
import { ApiError, ValidationError, AuthenticationError } from '../errorHandler';

// Mock fetch globally
global.fetch = jest.fn();

const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

describe('UserService', () => {
  beforeEach(() => {
    mockFetch.mockClear();
    localStorage.clear();
    localStorage.setItem('auth_token', 'mock-jwt-token');
  });

  describe('getCurrentUserProfile', () => {
    it('should get current user profile successfully', async () => {
      const mockProfile = {
        id: '1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        phoneNumber: '+*************',
        dateOfBirth: '1990-01-01',
        address: '123 Main St',
        city: 'Lagos',
        state: 'Lagos',
        country: 'Nigeria',
        profileImage: 'https://example.com/profile.jpg',
        isVerified: true,
        kycStatus: 'APPROVED',
        balance: 50000,
        totalSavings: 100000,
        totalEarnings: 5000,
        isActive: true,
        lastLoginAt: '2023-01-01T12:00:00Z',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z'
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockProfile,
        headers: new Headers({ 'content-type': 'application/json' })
      } as Response);

      const result = await userService.getCurrentUserProfile();

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/user/me',
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': 'Bearer mock-jwt-token'
          })
        })
      );

      expect(result).toEqual(mockProfile);
    });

    it('should throw AuthenticationError when not authenticated', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: async () => ({ message: 'Authentication required' }),
        headers: new Headers({ 'content-type': 'application/json' })
      } as Response);

      await expect(userService.getCurrentUserProfile()).rejects.toThrow(AuthenticationError);
    });
  });

  describe('updateCurrentUserProfile', () => {
    it('should update user profile successfully', async () => {
      const updateData = {
        firstName: 'Jane',
        lastName: 'Smith',
        phoneNumber: '+2348087654321',
        address: '456 New St',
        city: 'Abuja'
      };

      const mockUpdatedProfile = {
        id: '1',
        email: '<EMAIL>',
        ...updateData,
        isVerified: true,
        kycStatus: 'APPROVED',
        balance: 50000,
        totalSavings: 100000,
        totalEarnings: 5000,
        isActive: true,
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-02T00:00:00Z'
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockUpdatedProfile,
        headers: new Headers({ 'content-type': 'application/json' })
      } as Response);

      const result = await userService.updateCurrentUserProfile(updateData);

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/user/me',
        expect.objectContaining({
          method: 'PUT',
          body: JSON.stringify(updateData)
        })
      );

      expect(result).toEqual(mockUpdatedProfile);
    });

    it('should throw ValidationError for invalid data', async () => {
      const mockErrorResponse = {
        success: false,
        message: 'Validation failed',
        errors: {
          phoneNumber: ['Invalid phone number format']
        }
      };

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 422,
        json: async () => mockErrorResponse,
        headers: new Headers({ 'content-type': 'application/json' })
      } as Response);

      const updateData = {
        phoneNumber: 'invalid-phone'
      };

      await expect(userService.updateCurrentUserProfile(updateData)).rejects.toThrow(ValidationError);
    });
  });

  describe('getUserBalance', () => {
    it('should get user balance successfully', async () => {
      const mockBalance = {
        userId: '1',
        availableBalance: 50000,
        totalSavings: 100000,
        totalEarnings: 5000,
        pendingDeposits: 10000,
        pendingWithdrawals: 5000,
        lastUpdated: '2023-01-01T12:00:00Z'
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockBalance,
        headers: new Headers({ 'content-type': 'application/json' })
      } as Response);

      const result = await userService.getUserBalance();

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/user/balance',
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': 'Bearer mock-jwt-token'
          })
        })
      );

      expect(result).toEqual(mockBalance);
    });
  });

  describe('addBankAccount', () => {
    it('should add bank account successfully', async () => {
      const bankData = {
        bankName: 'First Bank',
        accountNumber: '**********',
        accountName: 'John Doe',
        bankCode: '011'
      };

      const mockResponse = {
        bankAccount: {
          id: 'bank-1',
          userId: '1',
          ...bankData,
          isVerified: false,
          isDefault: false,
          createdAt: '2023-01-01T00:00:00Z'
        }
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
        headers: new Headers({ 'content-type': 'application/json' })
      } as Response);

      const result = await userService.addBankAccount(bankData);

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/user/bank-accounts',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(bankData)
        })
      );

      expect(result).toEqual(mockResponse);
    });

    it('should throw ValidationError for invalid bank data', async () => {
      const mockErrorResponse = {
        success: false,
        message: 'Validation failed',
        errors: {
          accountNumber: ['Account number must be 10 digits'],
          bankCode: ['Invalid bank code']
        }
      };

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 422,
        json: async () => mockErrorResponse,
        headers: new Headers({ 'content-type': 'application/json' })
      } as Response);

      const bankData = {
        bankName: 'First Bank',
        accountNumber: '123',
        accountName: 'John Doe',
        bankCode: 'invalid'
      };

      await expect(userService.addBankAccount(bankData)).rejects.toThrow(ValidationError);
    });
  });

  describe('getBankAccounts', () => {
    it('should get bank accounts successfully', async () => {
      const mockBankAccounts = [
        {
          id: 'bank-1',
          userId: '1',
          bankName: 'First Bank',
          accountNumber: '**********',
          accountName: 'John Doe',
          bankCode: '011',
          isVerified: true,
          isDefault: true,
          createdAt: '2023-01-01T00:00:00Z'
        },
        {
          id: 'bank-2',
          userId: '1',
          bankName: 'GTBank',
          accountNumber: '**********',
          accountName: 'John Doe',
          bankCode: '058',
          isVerified: false,
          isDefault: false,
          createdAt: '2023-01-02T00:00:00Z'
        }
      ];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockBankAccounts,
        headers: new Headers({ 'content-type': 'application/json' })
      } as Response);

      const result = await userService.getBankAccounts();

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/user/bank-accounts',
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': 'Bearer mock-jwt-token'
          })
        })
      );

      expect(result).toEqual(mockBankAccounts);
    });
  });

  describe('utility methods', () => {
    it('should format currency correctly', () => {
      expect(userService.formatCurrency(50000)).toBe('₦50,000.00');
      expect(userService.formatCurrency(1234.56)).toBe('₦1,234.56');
    });

    it('should get initials correctly', () => {
      expect(userService.getInitials('John', 'Doe')).toBe('JD');
      expect(userService.getInitials('jane', 'smith')).toBe('JS');
    });

    it('should validate phone numbers correctly', () => {
      expect(userService.validatePhoneNumber('+*************')).toBe(true);
      expect(userService.validatePhoneNumber('***********')).toBe(true);
      expect(userService.validatePhoneNumber('*************')).toBe(true);
      expect(userService.validatePhoneNumber('invalid-phone')).toBe(false);
      expect(userService.validatePhoneNumber('**********')).toBe(false);
    });

    it('should validate email addresses correctly', () => {
      expect(userService.validateEmail('<EMAIL>')).toBe(true);
      expect(userService.validateEmail('<EMAIL>')).toBe(true);
      expect(userService.validateEmail('invalid-email')).toBe(false);
      expect(userService.validateEmail('test@')).toBe(false);
      expect(userService.validateEmail('@example.com')).toBe(false);
    });
  });
});
