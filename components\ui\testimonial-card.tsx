"use client";

import { Star } from "lucide-react";

interface TestimonialCardProps {
  name: string;
  role: string;
  content: string;
  rating: number;
  delay?: number;
}

export function TestimonialCard({
  name,
  role,
  content,
  rating,
  delay = 0,
}: TestimonialCardProps) {
  return (
    <div
      className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-green-500/20 rounded-2xl p-8 hover:border-green-500/40 transition-all duration-300 transform hover:scale-105 hover-lift glass"
      style={{ animationDelay: `${delay}ms` }}
      data-oid="tq2hvc2"
    >
      <div className="flex mb-4" data-oid="rdtrpo5">
        {[...Array(rating)].map((_, i) => (
          <Star
            key={i}
            className="w-5 h-5 text-green-400 fill-current animate-pulse-green"
            style={{ animationDelay: `${i * 100}ms` }}
            data-oid="l2sd4h9"
          />
        ))}
      </div>
      <p
        className="text-gray-300 mb-6 leading-relaxed italic"
        data-oid="pwr23ou"
      >
        "{content}"
      </p>
      <div className="border-t border-green-500/20 pt-4" data-oid="lwoe2uw">
        <div className="font-semibold text-white" data-oid="vovk5tn">
          {name}
        </div>
        <div className="text-green-400 text-sm" data-oid="42cs8ma">
          {role}
        </div>
      </div>
    </div>
  );
}
