"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FiPlus,
  FiSettings,
  FiTrendingUp,
  FiClock,
  FiDollarSign,
  FiCreditCard,
  FiZap,
  FiRefreshCw,
  FiEye,
  FiEdit3,
  FiTrash2,
  FiPlay,
  FiPause
} from 'react-icons/fi';
import DashboardLayout from '../../../src/components/dashboard/DashboardLayout';
import { Button } from '../../../src/components/ui/Button';
import { Card3D } from '../../../src/components/ui/Card3D';
import WithdrawalAccountManager from '../../../src/components/withdrawals/WithdrawalAccountManager';
import AutomaticWithdrawalSetup from '../../../src/components/withdrawals/AutomaticWithdrawalSetup';
import {
  WithdrawalDashboardData,
  AutomaticWithdrawalRule,
  WithdrawalAccount,
  AutomaticWithdrawalRequest
} from '../../../src/types/automaticWithdrawal';
import { automaticWithdrawalService } from '../../../src/services/automaticWithdrawalService';
import { automaticWithdrawalProcessor } from '../../../src/services/automaticWithdrawalProcessor';

export default function WithdrawalsPage() {
  const [activeTab, setActiveTab] = useState<'overview' | 'accounts' | 'rules' | 'history'>('overview');
  const [dashboardData, setDashboardData] = useState<WithdrawalDashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [showAccountManager, setShowAccountManager] = useState(false);
  const [showRuleSetup, setShowRuleSetup] = useState(false);
  const [editingRule, setEditingRule] = useState<AutomaticWithdrawalRule | null>(null);
  const [processingStatus, setProcessingStatus] = useState<any>(null);

  useEffect(() => {
    loadDashboardData();
    loadProcessingStatus();

    // Refresh processing status every 30 seconds
    const interval = setInterval(loadProcessingStatus, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const data = await automaticWithdrawalService.getDashboardData();
      setDashboardData(data);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      // Set mock data for development
      setDashboardData({
        totalBalance: 250000,
        availableForWithdrawal: 240000,
        pendingWithdrawals: 15000,
        activeRules: 3,
        recentWithdrawals: [],
        activeWithdrawalRules: [],
        withdrawalAccounts: [],
        monthlyWithdrawalStats: {
          totalAmount: 85000,
          totalCount: 12,
          successRate: 95.8
        }
      });
    } finally {
      setLoading(false);
    }
  };

  const loadProcessingStatus = async () => {
    try {
      const status = automaticWithdrawalProcessor.getProcessingStatus();
      setProcessingStatus(status);
    } catch (error) {
      console.error('Failed to load processing status:', error);
    }
  };
  const handleRuleToggle = async (ruleId: string, isActive: boolean) => {
    try {
      await automaticWithdrawalService.toggleRuleStatus(ruleId, isActive);
      loadDashboardData();
    } catch (error) {
      console.error('Failed to toggle rule:', error);
    }
  };

  const handleRuleDelete = async (ruleId: string) => {
    try {
      await automaticWithdrawalService.deleteAutomaticRule(ruleId);
      loadDashboardData();
    } catch (error) {
      console.error('Failed to delete rule:', error);
    }
  };

  const handleTriggerProcessing = async () => {
    try {
      await automaticWithdrawalProcessor.forceProcessing();
      loadProcessingStatus();
      loadDashboardData();
    } catch (error) {
      console.error('Failed to trigger processing:', error);
    }
  };

  const formatCurrency = (amount: number) => `₦${amount.toLocaleString()}`;

  const tabs = [
    { id: 'overview', label: 'Overview', icon: FiTrendingUp },
    { id: 'accounts', label: 'Accounts', icon: FiCreditCard },
    { id: 'rules', label: 'Auto Rules', icon: FiSettings },
    { id: 'history', label: 'History', icon: FiClock }
  ];

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card3D className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-theme-secondary text-sm font-inter">Total Balance</p>
              <p className="text-2xl font-bold text-theme font-inter">
                {formatCurrency(dashboardData?.totalBalance || 0)}
              </p>
            </div>
            <div className="p-3 bg-brand/20 rounded-lg">
              <FiDollarSign className="w-6 h-6 text-brand" />
            </div>
          </div>
        </Card3D>

        <Card3D className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-theme-secondary text-sm font-inter">Available</p>
              <p className="text-2xl font-bold text-theme font-inter">
                {formatCurrency(dashboardData?.availableForWithdrawal || 0)}
              </p>
            </div>
            <div className="p-3 bg-green-500/20 rounded-lg">
              <FiZap className="w-6 h-6 text-green-400" />
            </div>
          </div>
        </Card3D>

        <Card3D className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-theme-secondary text-sm font-inter">Pending</p>
              <p className="text-2xl font-bold text-theme font-inter">
                {formatCurrency(dashboardData?.pendingWithdrawals || 0)}
              </p>
            </div>
            <div className="p-3 bg-yellow-500/20 rounded-lg">
              <FiClock className="w-6 h-6 text-yellow-400" />
            </div>
          </div>
        </Card3D>

        <Card3D className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-theme-secondary text-sm font-inter">Active Rules</p>
              <p className="text-2xl font-bold text-theme font-inter">
                {dashboardData?.activeRules || 0}
              </p>
            </div>
            <div className="p-3 bg-blue-500/20 rounded-lg">
              <FiSettings className="w-6 h-6 text-blue-400" />
            </div>
          </div>
        </Card3D>
      </div>

      {/* Processing Status */}
      {processingStatus && (
        <Card3D className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-theme font-inter">Processing Engine</h3>
            <Button
              size="sm"
              onClick={handleTriggerProcessing}
              leftIcon={FiRefreshCw}
              className="font-inter"
            >
              Trigger Processing
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-theme-secondary text-sm font-inter">Status</p>
              <div className={`inline-flex items-center space-x-2 px-3 py-1 rounded-full ${
                processingStatus.isProcessing
                  ? 'bg-green-500/20 text-green-400'
                  : 'bg-gray-500/20 text-gray-400'
              }`}>
                <div className={`w-2 h-2 rounded-full ${
                  processingStatus.isProcessing ? 'bg-green-400' : 'bg-gray-400'
                }`} />
                <span className="text-sm font-medium font-inter">
                  {processingStatus.isProcessing ? 'Processing' : 'Idle'}
                </span>
              </div>
            </div>

            <div className="text-center">
              <p className="text-theme-secondary text-sm font-inter">Queue</p>
              <p className="text-xl font-bold text-theme font-inter">{processingStatus.queueLength}</p>
            </div>

            <div className="text-center">
              <p className="text-theme-secondary text-sm font-inter">Processed</p>
              <p className="text-xl font-bold text-theme font-inter">{processingStatus.processedRulesCount}</p>
            </div>

            <div className="text-center">
              <p className="text-theme-secondary text-sm font-inter">Failed</p>
              <p className="text-xl font-bold text-theme font-inter">{processingStatus.failedRulesCount}</p>
            </div>
          </div>
        </Card3D>
      )}

      {/* Quick Actions */}
      <Card3D className="p-6">
        <h3 className="text-lg font-semibold text-theme font-inter mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Button
            onClick={() => setShowAccountManager(true)}
            leftIcon={FiCreditCard}
            className="font-inter"
            fullWidth
          >
            Manage Accounts
          </Button>

          <Button
            onClick={() => setShowRuleSetup(true)}
            leftIcon={FiPlus}
            className="font-inter"
            fullWidth
          >
            Create Auto Rule
          </Button>

          <Button
            onClick={() => setActiveTab('history')}
            leftIcon={FiClock}
            variant="outline"
            className="font-inter"
            fullWidth
          >
            View History
          </Button>
        </div>
      </Card3D>

      {/* Recent Activity */}
      <Card3D className="p-6">
        <h3 className="text-lg font-semibold text-theme font-inter mb-4">Recent Withdrawals</h3>
        {dashboardData?.recentWithdrawals.length === 0 ? (
          <div className="text-center py-8">
            <FiClock className="w-12 h-12 text-theme-secondary mx-auto mb-4" />
            <p className="text-theme-secondary font-inter">No recent withdrawals</p>
          </div>
        ) : (
          <div className="space-y-3">
            {dashboardData?.recentWithdrawals.map((withdrawal) => (
              <div key={withdrawal.id} className="flex items-center justify-between p-3 bg-theme-secondary/50 rounded-lg">
                <div>
                  <p className="font-medium text-theme font-inter">{formatCurrency(withdrawal.amount)}</p>
                  <p className="text-sm text-theme-secondary font-inter">
                    {new Date(withdrawal.createdAt).toLocaleDateString()}
                  </p>
                </div>
                <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                  withdrawal.status === 'completed'
                    ? 'bg-green-500/20 text-green-400'
                    : withdrawal.status === 'pending'
                    ? 'bg-yellow-500/20 text-yellow-400'
                    : 'bg-red-500/20 text-red-400'
                }`}>
                  {withdrawal.status}
                </div>
              </div>
            ))}
          </div>
        )}
      </Card3D>
    </div>
  );

  const renderAccounts = () => (
    <WithdrawalAccountManager showAddButton={true} />
  );

  const renderRules = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-bold text-theme font-inter">Automatic Withdrawal Rules</h3>
          <p className="text-theme-secondary font-inter">Manage your automatic withdrawal rules</p>
        </div>
        <Button
          onClick={() => setShowRuleSetup(true)}
          leftIcon={FiPlus}
          className="font-inter"
        >
          Create Rule
        </Button>
      </div>

      {dashboardData?.activeWithdrawalRules.length === 0 ? (
        <Card3D className="p-12 text-center">
          <FiSettings className="w-16 h-16 text-theme-secondary mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-theme font-inter mb-2">No Automatic Rules</h3>
          <p className="text-theme-secondary font-inter mb-6">
            Create automatic withdrawal rules to streamline your transfers
          </p>
          <Button
            onClick={() => setShowRuleSetup(true)}
            leftIcon={FiPlus}
            className="font-inter"
          >
            Create Your First Rule
          </Button>
        </Card3D>
      ) : (
        <div className="space-y-4">
          {dashboardData?.activeWithdrawalRules.map((rule) => (
            <Card3D key={rule.id} className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h4 className="font-semibold text-theme font-inter">{rule.name}</h4>
                    <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                      rule.isActive
                        ? 'bg-green-500/20 text-green-400'
                        : 'bg-gray-500/20 text-gray-400'
                    }`}>
                      {rule.isActive ? 'Active' : 'Inactive'}
                    </div>
                  </div>

                  {rule.description && (
                    <p className="text-theme-secondary text-sm font-inter mb-2">{rule.description}</p>
                  )}

                  <div className="flex items-center space-x-4 text-sm text-theme-secondary">
                    <span className="font-inter">Trigger: {rule.triggerType.replace('_', ' ')}</span>
                    <span className="font-inter">Amount: {rule.withdrawalConfig.amountType}</span>
                    <span className="font-inter">
                      Executions: {rule.statistics.totalExecutions}
                    </span>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleRuleToggle(rule.id, !rule.isActive)}
                    leftIcon={rule.isActive ? FiPause : FiPlay}
                    className="font-inter"
                  >
                    {rule.isActive ? 'Pause' : 'Activate'}
                  </Button>

                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setEditingRule(rule)}
                    leftIcon={FiEdit3}
                    className="font-inter"
                  >
                    Edit
                  </Button>

                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleRuleDelete(rule.id)}
                    leftIcon={FiTrash2}
                    className="font-inter text-red-400 border-red-400 hover:bg-red-400/10"
                  >
                    Delete
                  </Button>
                </div>
              </div>
            </Card3D>
          ))}
        </div>
      )}
    </div>
  );

  const renderHistory = () => (
    <Card3D className="p-6">
      <h3 className="text-lg font-semibold text-theme font-inter mb-4">Withdrawal History</h3>
      <div className="text-center py-8">
        <FiClock className="w-12 h-12 text-theme-secondary mx-auto mb-4" />
        <p className="text-theme-secondary font-inter">Withdrawal history will be displayed here</p>
      </div>
    </Card3D>
  );

  if (loading) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          {[1, 2, 3].map(i => (
            <Card3D key={i} className="p-6 animate-pulse">
              <div className="h-4 bg-theme-secondary rounded w-3/4 mb-4"></div>
              <div className="h-3 bg-theme-secondary rounded w-1/2"></div>
            </Card3D>
          ))}
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-theme font-inter">Automatic Withdrawals</h1>
          <p className="text-theme-secondary font-inter">
            Manage your automatic withdrawal accounts and rules
          </p>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 bg-theme-secondary/50 p-1 rounded-lg">
          {tabs.map((tab) => {
            const IconComponent = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-all font-inter ${
                  activeTab === tab.id
                    ? 'bg-brand text-white'
                    : 'text-theme-secondary hover:text-theme hover:bg-theme-secondary'
                }`}
              >
                <IconComponent className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </div>

        {/* Tab Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            {activeTab === 'overview' && renderOverview()}
            {activeTab === 'accounts' && renderAccounts()}
            {activeTab === 'rules' && renderRules()}
            {activeTab === 'history' && renderHistory()}
          </motion.div>
        </AnimatePresence>

        {/* Modals */}
        <AnimatePresence>
          {showAccountManager && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
              onClick={() => setShowAccountManager(false)}
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                className="bg-theme rounded-2xl p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto"
                onClick={(e) => e.stopPropagation()}
              >
                <WithdrawalAccountManager />
              </motion.div>
            </motion.div>
          )}

          {(showRuleSetup || editingRule) && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
              onClick={() => {
                setShowRuleSetup(false);
                setEditingRule(null);
              }}
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                className="bg-theme rounded-2xl p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto"
                onClick={(e) => e.stopPropagation()}
              >
                <AutomaticWithdrawalSetup
                  editingRule={editingRule || undefined}
                  withdrawalAccounts={dashboardData?.withdrawalAccounts || []}
                  onRuleCreated={(rule) => {
                    setShowRuleSetup(false);
                    setEditingRule(null);
                    loadDashboardData();
                  }}
                  onCancel={() => {
                    setShowRuleSetup(false);
                    setEditingRule(null);
                  }}
                />
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </DashboardLayout>
  );
}
