"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import AOS from 'aos';
import 'aos/dist/aos.css';
import { useAuth } from '../../../src/hooks/use-auth';
import { useRouter } from 'next/navigation';
import { useKYCEnforcement } from '../../../src/hooks/use-kyc-enforcement';
import { KYCIncompleteBanner } from '../../../src/components/KYCIncompleteBanner';
import DashboardLayout from '../../../src/components/dashboard/DashboardLayout';
import { StatCard } from '../../../src/components/dashboard/DashboardCard';
import { showToast } from '../../../src/components/ui/Toast';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  Filler,
} from 'chart.js';
import { <PERSON>, <PERSON>, <PERSON>hn<PERSON>, Pie } from 'react-chartjs-2';
import { 
  FiDollarSign, 
  FiTrendingUp, 
  FiTarget, 
  FiCalendar,
  FiDownload,
  FiRefreshCw,
  FiFilter,
  FiBarChart3,
  FiPieChart,
  FiActivity
} from 'react-icons/fi';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  Filler
);

interface AnalyticsData {
  totalSavings: number;
  monthlyGrowth: number;
  interestEarned: number;
  goalCompletion: number;
  savingsHistory: Array<{ month: string; amount: number; target: number }>;
  categoryBreakdown: Array<{ category: string; amount: number; color: string }>;
  monthlyContributions: Array<{ month: string; contributions: number; withdrawals: number }>;
  performanceMetrics: {
    avgMonthlyGrowth: number;
    bestMonth: string;
    totalInterest: number;
    savingsRate: number;
  };
}

const mockAnalyticsData: AnalyticsData = {
  totalSavings: 2750000,
  monthlyGrowth: 15.2,
  interestEarned: 187500,
  goalCompletion: 68,
  savingsHistory: [
    { month: 'Jan', amount: 500000, target: 600000 },
    { month: 'Feb', amount: 750000, target: 800000 },
    { month: 'Mar', amount: 1200000, target: 1000000 },
    { month: 'Apr', amount: 1650000, target: 1500000 },
    { month: 'May', amount: 2100000, target: 2000000 },
    { month: 'Jun', amount: 2750000, target: 2500000 },
  ],
  categoryBreakdown: [
    { category: 'Emergency Fund', amount: 1200000, color: '#10B981' },
    { category: 'Vacation', amount: 500000, color: '#3B82F6' },
    { category: 'Car Purchase', amount: 800000, color: '#F59E0B' },
    { category: 'Investment', amount: 250000, color: '#8B5CF6' },
  ],
  monthlyContributions: [
    { month: 'Jan', contributions: 250000, withdrawals: 0 },
    { month: 'Feb', contributions: 300000, withdrawals: 50000 },
    { month: 'Mar', contributions: 500000, withdrawals: 0 },
    { month: 'Apr', contributions: 450000, withdrawals: 0 },
    { month: 'May', contributions: 500000, withdrawals: 50000 },
    { month: 'Jun', contributions: 700000, withdrawals: 50000 },
  ],
  performanceMetrics: {
    avgMonthlyGrowth: 12.8,
    bestMonth: 'June',
    totalInterest: 187500,
    savingsRate: 85.2,
  },
};

export default function AnalyticsPage() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const kycApproved = useKYCEnforcement({ redirect: false });
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('6months');
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    // Initialize AOS with custom settings
    AOS.init({
      duration: 1200,
      once: false,
      easing: 'ease-out-cubic',
      offset: 100,
      delay: 100,
    });

    // Refresh AOS on component mount
    AOS.refresh();

    return () => {
      AOS.refresh();
    };
  }, []);

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login');
      return;
    }

    if (!isLoading && user?.role === 'ADMIN') {
      router.push('/admin/dashboard');
      return;
    }

    loadAnalyticsData();
  }, [isAuthenticated, isLoading, user, router, selectedPeriod]);

  const loadAnalyticsData = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      setAnalyticsData(mockAnalyticsData);
      showToast.success('Analytics data loaded successfully!');
    } catch (error) {
      console.error('Failed to load analytics:', error);
      showToast.error('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await loadAnalyticsData();
      showToast.success('Data refreshed!');
    } finally {
      setRefreshing(false);
    }
  };

  const handleExport = () => {
    showToast.promise(
      new Promise((resolve) => setTimeout(resolve, 2000)),
      {
        loading: 'Generating report...',
        success: 'Analytics report downloaded!',
        error: 'Failed to generate report',
      }
    );
  };

  // Chart configurations
  const savingsChartData = {
    labels: analyticsData?.savingsHistory.map(item => item.month) || [],
    datasets: [
      {
        label: 'Actual Savings',
        data: analyticsData?.savingsHistory.map(item => item.amount) || [],
        borderColor: '#10B981',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        borderWidth: 3,
        fill: true,
        tension: 0.4,
        pointBackgroundColor: '#10B981',
        pointBorderColor: '#ffffff',
        pointBorderWidth: 2,
        pointRadius: 6,
        pointHoverRadius: 8,
      },
      {
        label: 'Target',
        data: analyticsData?.savingsHistory.map(item => item.target) || [],
        borderColor: '#6B7280',
        backgroundColor: 'transparent',
        borderWidth: 2,
        borderDash: [5, 5],
        fill: false,
        tension: 0.4,
        pointRadius: 0,
      },
    ],
  };

  const contributionsChartData = {
    labels: analyticsData?.monthlyContributions.map(item => item.month) || [],
    datasets: [
      {
        label: 'Contributions',
        data: analyticsData?.monthlyContributions.map(item => item.contributions) || [],
        backgroundColor: '#10B981',
        borderColor: '#059669',
        borderWidth: 1,
        borderRadius: 4,
      },
      {
        label: 'Withdrawals',
        data: analyticsData?.monthlyContributions.map(item => item.withdrawals) || [],
        backgroundColor: '#EF4444',
        borderColor: '#DC2626',
        borderWidth: 1,
        borderRadius: 4,
      },
    ],
  };

  const categoryChartData = {
    labels: analyticsData?.categoryBreakdown.map(item => item.category) || [],
    datasets: [
      {
        data: analyticsData?.categoryBreakdown.map(item => item.amount) || [],
        backgroundColor: analyticsData?.categoryBreakdown.map(item => item.color) || [],
        borderColor: '#1F2937',
        borderWidth: 2,
        hoverBorderWidth: 3,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          color: '#F9FAFB',
          font: {
            size: 12,
          },
          usePointStyle: true,
          padding: 20,
        },
      },
      tooltip: {
        backgroundColor: '#1F2937',
        titleColor: '#F9FAFB',
        bodyColor: '#F9FAFB',
        borderColor: '#374151',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        callbacks: {
          label: function(context: any) {
            return `${context.dataset.label}: ₦${context.parsed.y?.toLocaleString()}`;
          },
        },
      },
    },
    scales: {
      x: {
        grid: {
          color: '#374151',
          drawBorder: false,
        },
        ticks: {
          color: '#9CA3AF',
          font: {
            size: 11,
          },
        },
      },
      y: {
        grid: {
          color: '#374151',
          drawBorder: false,
        },
        ticks: {
          color: '#9CA3AF',
          font: {
            size: 11,
          },
          callback: function(value: any) {
            return '₦' + value.toLocaleString();
          },
        },
      },
    },
  };

  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: {
          color: '#F9FAFB',
          font: {
            size: 12,
          },
          usePointStyle: true,
          padding: 15,
        },
      },
      tooltip: {
        backgroundColor: '#1F2937',
        titleColor: '#F9FAFB',
        bodyColor: '#F9FAFB',
        borderColor: '#374151',
        borderWidth: 1,
        cornerRadius: 8,
        callbacks: {
          label: function(context: any) {
            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
            const percentage = ((context.parsed / total) * 100).toFixed(1);
            return `${context.label}: ₦${context.parsed.toLocaleString()} (${percentage}%)`;
          },
        },
      },
    },
    cutout: '60%',
  };

  if (isLoading || loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center" data-aos="fade-in">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto mb-4"></div>
            <p className="text-gray-400">Loading analytics...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!isAuthenticated || user?.role === 'ADMIN' || !analyticsData) {
    return null;
  }

  return (
    <DashboardLayout title="Analytics">
      <div className="space-y-6">
        {/* KYC Banner for incomplete KYC */}
        {!kycApproved && <KYCIncompleteBanner />}
        {/* Header Actions */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0"
          data-aos="fade-down"
          data-aos-delay="100"
        >
          <div>
            <h1 className="text-2xl font-bold text-white mb-2">Savings Analytics</h1>
            <p className="text-gray-400">Track your financial progress and insights</p>
          </div>
          
          <div className="flex space-x-3">
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-green-500"
              data-aos="fade-left"
              data-aos-delay="200"
            >
              <option value="3months">Last 3 Months</option>
              <option value="6months">Last 6 Months</option>
              <option value="1year">Last Year</option>
            </select>
            
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white rounded-lg transition-colors"
              data-aos="fade-left"
              data-aos-delay="300"
            >
              <FiRefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </button>
            
            <button
              onClick={handleExport}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
              data-aos="fade-left"
              data-aos-delay="400"
            >
              <FiDownload className="w-4 h-4" />
              <span>Export</span>
            </button>
          </div>
        </motion.div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div data-aos="fade-up" data-aos-delay="100">
            <StatCard
              title="Total Savings"
              value={`₦${analyticsData.totalSavings.toLocaleString()}`}
              icon={FiDollarSign}
              color="green"
              trend={{ value: analyticsData.monthlyGrowth, isPositive: true }}
            />
          </div>
          <div data-aos="fade-up" data-aos-delay="200">
            <StatCard
              title="Interest Earned"
              value={`₦${analyticsData.interestEarned.toLocaleString()}`}
              subtitle="This period"
              icon={FiTrendingUp}
              color="blue"
            />
          </div>
          <div data-aos="fade-up" data-aos-delay="300">
            <StatCard
              title="Goal Completion"
              value={`${analyticsData.goalCompletion}%`}
              subtitle="Average progress"
              icon={FiTarget}
              color="purple"
            />
          </div>
          <div data-aos="fade-up" data-aos-delay="400">
            <StatCard
              title="Savings Rate"
              value={`${analyticsData.performanceMetrics.savingsRate}%`}
              subtitle="Of income"
              icon={FiActivity}
              color="yellow"
            />
          </div>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Savings Progress Chart */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="bg-gray-900/50 border border-gray-800 rounded-lg p-6"
            data-aos="fade-right"
            data-aos-delay="500"
          >
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-white flex items-center">
                <FiBarChart3 className="w-5 h-5 mr-2 text-green-400" />
                Savings Progress
              </h3>
              <div className="text-sm text-gray-400">
                vs Target
              </div>
            </div>
            <div className="h-80">
              <Line data={savingsChartData} options={chartOptions} />
            </div>
          </motion.div>

          {/* Category Breakdown */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="bg-gray-900/50 border border-gray-800 rounded-lg p-6"
            data-aos="fade-left"
            data-aos-delay="600"
          >
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-white flex items-center">
                <FiPieChart className="w-5 h-5 mr-2 text-blue-400" />
                Savings by Category
              </h3>
              <div className="text-sm text-gray-400">
                Distribution
              </div>
            </div>
            <div className="h-80">
              <Doughnut data={categoryChartData} options={doughnutOptions} />
            </div>
          </motion.div>
        </div>

        {/* Monthly Contributions Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-900/50 border border-gray-800 rounded-lg p-6"
          data-aos="fade-up"
          data-aos-delay="700"
        >
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-white flex items-center">
              <FiActivity className="w-5 h-5 mr-2 text-purple-400" />
              Monthly Contributions vs Withdrawals
            </h3>
            <div className="text-sm text-gray-400">
              Cash Flow Analysis
            </div>
          </div>
          <div className="h-80">
            <Bar data={contributionsChartData} options={chartOptions} />
          </div>
        </motion.div>

        {/* Performance Insights */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-gradient-to-br from-green-500/20 to-green-600/20 border border-green-500/30 rounded-lg p-6"
            data-aos="zoom-in"
            data-aos-delay="800"
          >
            <div className="flex items-center justify-between mb-4">
              <FiTrendingUp className="w-8 h-8 text-green-400" />
              <span className="text-green-400 text-sm font-medium">Growth</span>
            </div>
            <h4 className="text-2xl font-bold text-white mb-1">
              {analyticsData.performanceMetrics.avgMonthlyGrowth}%
            </h4>
            <p className="text-gray-300 text-sm">Average Monthly Growth</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-gradient-to-br from-blue-500/20 to-blue-600/20 border border-blue-500/30 rounded-lg p-6"
            data-aos="zoom-in"
            data-aos-delay="900"
          >
            <div className="flex items-center justify-between mb-4">
              <FiCalendar className="w-8 h-8 text-blue-400" />
              <span className="text-blue-400 text-sm font-medium">Best</span>
            </div>
            <h4 className="text-2xl font-bold text-white mb-1">
              {analyticsData.performanceMetrics.bestMonth}
            </h4>
            <p className="text-gray-300 text-sm">Best Performing Month</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-gradient-to-br from-purple-500/20 to-purple-600/20 border border-purple-500/30 rounded-lg p-6"
            data-aos="zoom-in"
            data-aos-delay="1000"
          >
            <div className="flex items-center justify-between mb-4">
              <FiDollarSign className="w-8 h-8 text-purple-400" />
              <span className="text-purple-400 text-sm font-medium">Interest</span>
            </div>
            <h4 className="text-2xl font-bold text-white mb-1">
              ₦{analyticsData.performanceMetrics.totalInterest.toLocaleString()}
            </h4>
            <p className="text-gray-300 text-sm">Total Interest Earned</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-gradient-to-br from-yellow-500/20 to-yellow-600/20 border border-yellow-500/30 rounded-lg p-6"
            data-aos="zoom-in"
            data-aos-delay="1100"
          >
            <div className="flex items-center justify-between mb-4">
              <FiTarget className="w-8 h-8 text-yellow-400" />
              <span className="text-yellow-400 text-sm font-medium">Rate</span>
            </div>
            <h4 className="text-2xl font-bold text-white mb-1">
              {analyticsData.performanceMetrics.savingsRate}%
            </h4>
            <p className="text-gray-300 text-sm">Savings Rate</p>
          </motion.div>
        </div>

        {/* Insights and Recommendations */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-900/50 border border-gray-800 rounded-lg p-6"
          data-aos="fade-up"
          data-aos-delay="1200"
        >
          <h3 className="text-lg font-semibold text-white mb-6 flex items-center">
            <FiActivity className="w-5 h-5 mr-2 text-green-400" />
            Insights & Recommendations
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4" data-aos="fade-right" data-aos-delay="1300">
              <div className="bg-green-500/10 border border-green-500/30 rounded-lg p-4">
                <h4 className="text-green-400 font-medium mb-2">🎯 Great Progress!</h4>
                <p className="text-gray-300 text-sm">
                  You're exceeding your savings targets by an average of 15%. Keep up the excellent work!
                </p>
              </div>

              <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4">
                <h4 className="text-blue-400 font-medium mb-2">📈 Consistent Growth</h4>
                <p className="text-gray-300 text-sm">
                  Your savings have grown consistently over the past 6 months with minimal volatility.
                </p>
              </div>
            </div>

            <div className="space-y-4" data-aos="fade-left" data-aos-delay="1400">
              <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4">
                <h4 className="text-yellow-400 font-medium mb-2">💡 Optimization Tip</h4>
                <p className="text-gray-300 text-sm">
                  Consider increasing your emergency fund allocation to reach the recommended 6-month expense coverage.
                </p>
              </div>

              <div className="bg-purple-500/10 border border-purple-500/30 rounded-lg p-4">
                <h4 className="text-purple-400 font-medium mb-2">🚀 Next Goal</h4>
                <p className="text-gray-300 text-sm">
                  You're on track to reach your next milestone of ₦3M by the end of this quarter!
                </p>
              </div>
            </div>
          </div>
        </motion.div>

      </div>
    </DashboardLayout>
  );
}
