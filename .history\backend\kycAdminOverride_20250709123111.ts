// backend/kycAdminOverride.ts
// Placeholder for KYC admin override and KYC API integration logic.
// This file will contain logic for verifying KYC via external API (Dojah, Smile Identity, VerifyMe, etc.)
// and fallback/manual admin approval if the KYC API is down.

// TODO: Implement KYC API integration (BVN/NIN verification)
// TODO: Implement admin override logic for manual approval

export const verifyKYCWithProvider = async (kycData) => {
  // Call external KYC API here
  // Return verification result or throw error if API is down
};

export const adminApproveKYC = async (userId, adminId) => {
  // Logic for admin to manually approve KYC if API is down
  // Update user KYC status in DB
};
 Best Practice Logic
Signup Flow
Signup form: Only ask for essential info (name, email, phone, password).
After signup/login, check user.kycStatus.
If not APPROVED, redirect to /dashboard/kyc and block access to deposit, plans, etc.
KYC Enforcement
On all protected pages (deposits, plans, dashboard), check user.kycStatus.
If not APPROVED, show a message and redirect to /dashboard/kyc.
Optionally, show a banner or modal: “Complete your KYC to unlock deposits and savings plans.”
KYC Form
Step 1: Personal info (pre-fill from signup if possible).
Step 2: BVN or NIN (let user choose which to provide).
Step 3: Selfie and document upload (optional for basic KYC, required for higher limits).
Step 4: Submit for verification (call KYC API).
KYC API Integration
On KYC step, call Dojah/VerifyMe/Smile Identity API with BVN or NIN.
If verified, set user.kycStatus = 'APPROVED' in your backend.
If failed, show error and let user retry.
User Experience
Signup is fast (no BVN/NIN required at signup).
User can browse dashboard, but cannot deposit or create plans until KYC is done.
KYC is a guided, clear process with instant feedback if possible.
4. Implementation Steps
Backend: Enforce KYC check in all deposit/plan creation endpoints.
Frontend: On protected pages, check user.kycStatus and redirect if not approved.
KYC Page: Add BVN/NIN as required fields, and integrate with a KYC API.
User Model: Store kycStatus, kycType (BVN/NIN), and KYC result data.
Admin Panel: Optionally, allow manual review/override of KYC status.
5. Example User Flow
User signs up (name, email, phone, password).
User lands on dashboard, sees “Complete KYC to deposit or create plans.”
User goes to KYC page, enters BVN or NIN, submits selfie.
KYC API verifies instantly (or within a few minutes).
If approved, user can now deposit and create plans.
Summary:

Don’t ask for BVN/NIN at signup—ask after login, before financial actions.
Enforce KYC everywhere it matters (backend and frontend).
Use a reputable KYC API for instant verification.
Make the KYC process clear, fast, and user-friendly.