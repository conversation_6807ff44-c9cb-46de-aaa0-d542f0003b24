"use client";

import { useEffect } from 'react';
import { useAuth } from '../../hooks/use-auth';

declare global {
  interface Window {
    Intercom: any;
    intercomSettings: any;
  }
}

interface IntercomWidgetProps {
  appId?: string;
}

export default function IntercomWidget({ appId = 'your_intercom_app_id' }: IntercomWidgetProps) {
  const { user, isAuthenticated } = useAuth();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!isClient) return;

    // Load Intercom script
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.async = true;
    script.src = `https://widget.intercom.io/widget/${appId}`;
    
    const firstScript = document.getElementsByTagName('script')[0];
    if (firstScript && firstScript.parentNode) {
      firstScript.parentNode.insertBefore(script, firstScript);
    }

    // Configure Intercom settings
    window.intercomSettings = {
      app_id: appId,
      ...(isAuthenticated && user && {
        user_id: user.id,
        name: `${user.firstName} ${user.lastName}`,
        email: user.email,
        created_at: user.createdAt ? Math.floor(new Date(user.createdAt).getTime() / 1000) : undefined,
        user_hash: user.intercomHash, // Server-side generated hash for security
        custom_attributes: {
          role: user.role,
          plan_type: user.planType || 'basic',
          total_savings: user.totalSavings || 0,
          kyc_status: user.kycStatus || 'pending'
        }
      })
    };

    // Initialize Intercom
    if (window.Intercom) {
      window.Intercom('reattach_activator');
      window.Intercom('update', window.intercomSettings);
    } else {
      const intercomFunction = function(...args: any[]) {
        intercomFunction.c(args);
      };
      intercomFunction.q = [];
      intercomFunction.c = function(args: any[]) {
        intercomFunction.q.push(args);
      };
      window.Intercom = intercomFunction;
    }

    // Boot Intercom
    window.Intercom('boot', window.intercomSettings);

    return () => {
      // Cleanup on unmount
      if (window.Intercom) {
        window.Intercom('shutdown');
      }
    };
  }, [appId, isAuthenticated, user, isClient]);

  // Update Intercom when user data changes
  useEffect(() => {
    if (window.Intercom && isAuthenticated && user) {
      window.Intercom('update', {
        user_id: user.id,
        name: `${user.firstName} ${user.lastName}`,
        email: user.email,
        custom_attributes: {
          role: user.role,
          plan_type: user.planType || 'basic',
          total_savings: user.totalSavings || 0,
          kyc_status: user.kycStatus || 'pending'
        }
      });
    }
  }, [user, isAuthenticated]);

  return null; // This component doesn't render anything visible
}

// Helper functions to interact with Intercom programmatically
export const IntercomAPI = {
  show: () => {
    if (window.Intercom) {
      window.Intercom('show');
    }
  },
  
  hide: () => {
    if (window.Intercom) {
      window.Intercom('hide');
    }
  },
  
  showMessages: () => {
    if (window.Intercom) {
      window.Intercom('showMessages');
    }
  },
  
  showNewMessage: (message?: string) => {
    if (window.Intercom) {
      window.Intercom('showNewMessage', message);
    }
  },
  
  trackEvent: (eventName: string, metadata?: Record<string, any>) => {
    if (window.Intercom) {
      window.Intercom('trackEvent', eventName, metadata);
    }
  },
  
  update: (data: Record<string, any>) => {
    if (window.Intercom) {
      window.Intercom('update', data);
    }
  }
};

// Custom hook for Intercom
export function useIntercom() {
  const showSupport = () => IntercomAPI.show();
  const hideSupport = () => IntercomAPI.hide();
  const showMessages = () => IntercomAPI.showMessages();
  const showNewMessage = (message?: string) => IntercomAPI.showNewMessage(message);
  const trackEvent = (eventName: string, metadata?: Record<string, any>) => 
    IntercomAPI.trackEvent(eventName, metadata);

  return {
    showSupport,
    hideSupport,
    showMessages,
    showNewMessage,
    trackEvent
  };
}
