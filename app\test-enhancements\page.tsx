"use client";

import React from 'react';
import { motion } from 'framer-motion';
import DashboardLayout from '../../src/components/dashboard/DashboardLayout';
import { StatCard, ActionCard } from '../../src/components/dashboard/DashboardCard';
import { Card3D } from '../../src/components/ui/Card3D';
import { ThemeToggleButton } from '../../src/components/ui/ThemedButton';
import { useTheme, getThemeClasses } from '../../src/contexts/ThemeContext';
import {
  FiDollarSign,
  FiTrendingUp,
  FiUsers,
  FiTarget,
  FiPlus,
  FiCreditCard,
  FiPieChart,
  FiUserCheck
} from 'react-icons/fi';

export default function TestEnhancementsPage() {
  const { theme } = useTheme();
  const themeClasses = getThemeClasses(theme);

  return (
    <DashboardLayout title="Enhanced Components Test">
      <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className={`text-3xl font-bold ${themeClasses.text.primary} mb-2`}>
              Enhanced Dashboard Components
            </h1>
            <p className={`text-lg ${themeClasses.text.secondary}`}>
              Material Design enhanced components with improved theming
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <ThemeToggleButton variant="switch" />
            <ThemeToggleButton variant="icon" />
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatCard
            title="Total Balance"
            value="₦2,450,000"
            subtitle="Available balance"
            icon={FiDollarSign}
            color="green"
            trend={{ value: 12.5, isPositive: true }}
          />
          <StatCard
            title="Monthly Growth"
            value="₦125,000"
            subtitle="This month"
            icon={FiTrendingUp}
            color="blue"
            trend={{ value: 8.2, isPositive: true }}
          />
          <StatCard
            title="Active Users"
            value="1,234"
            subtitle="Registered users"
            icon={FiUsers}
            color="purple"
            trend={{ value: 5.1, isPositive: true }}
          />
          <StatCard
            title="Goals Achieved"
            value="89%"
            subtitle="This quarter"
            icon={FiTarget}
            color="yellow"
            trend={{ value: -2.3, isPositive: false }}
          />
        </div>

        {/* Action Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <ActionCard
            title="Create Savings Plan"
            subtitle="Start a new savings goal"
            icon={FiPlus}
            color="green"
            onClick={() => console.log('Create savings plan')}
          />
          <ActionCard
            title="Add Payment Method"
            subtitle="Link your bank account"
            icon={FiCreditCard}
            color="blue"
            onClick={() => console.log('Add payment method')}
          />
          <ActionCard
            title="View Analytics"
            subtitle="Track your progress"
            icon={FiPieChart}
            color="purple"
            onClick={() => console.log('View analytics')}
          />
        </div>

        {/* 3D Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <Card3D elevation={1} intensity="light">
            <div className="p-6">
              <h3 className={`text-xl font-semibold ${themeClasses.text.primary} mb-2`}>
                Elevation 1
              </h3>
              <p className={themeClasses.text.secondary}>
                Light elevation with subtle shadow
              </p>
            </div>
          </Card3D>
          <Card3D elevation={3} intensity="medium">
            <div className="p-6">
              <h3 className={`text-xl font-semibold ${themeClasses.text.primary} mb-2`}>
                Elevation 3
              </h3>
              <p className={themeClasses.text.secondary}>
                Medium elevation with moderate shadow
              </p>
            </div>
          </Card3D>
          <Card3D elevation={5} intensity="strong">
            <div className="p-6">
              <h3 className={`text-xl font-semibold ${themeClasses.text.primary} mb-2`}>
                Elevation 5
              </h3>
              <p className={themeClasses.text.secondary}>
                High elevation with strong shadow
              </p>
            </div>
          </Card3D>
        </div>

        {/* Theme Toggle Variants */}
        <div className={`rounded-xl p-6 ${themeClasses.bg.card} ${themeClasses.border.primary} border`}>
          <h2 className={`text-2xl font-semibold ${themeClasses.text.primary} mb-4`}>
            Theme Toggle Variants
          </h2>
          <div className="flex flex-wrap items-center gap-6">
            <div className="flex flex-col items-center space-y-2">
              <ThemeToggleButton variant="icon" />
              <span className={`text-sm ${themeClasses.text.secondary}`}>Icon</span>
            </div>
            <div className="flex flex-col items-center space-y-2">
              <ThemeToggleButton variant="switch" />
              <span className={`text-sm ${themeClasses.text.secondary}`}>Switch</span>
            </div>
          </div>
        </div>

        {/* Floating Action Button */}
        <ThemeToggleButton variant="fab" />
      </div>
    </DashboardLayout>
  );
}
