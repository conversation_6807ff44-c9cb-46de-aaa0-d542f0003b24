"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  FiCreditCard, 
  FiSearch, 
  FiFilter,
  FiDownload,
  FiRefreshCw,
  FiCheck,
  FiX,
  FiClock,
  FiDollarSign,
  FiTrendingUp,
  FiTrendingDown,
  FiAlertCircle,
  FiEye,
  FiMoreVertical
} from 'react-icons/fi';
import AdminLayout from '../../../src/components/admin/AdminLayout';
import { adminService, transactionsService, depositsService, withdrawalsService } from '../../../src/services';
import Button from '../../../src/components/ui/Button';
import Card from '../../../src/components/ui/Card';
import Badge from '../../../src/components/ui/Badge';
import Input from '../../../src/components/ui/Input';
import Select from '../../../src/components/ui/Select';
import Modal from '../../../src/components/ui/Modal';
import Table from '../../../src/components/ui/Table';
import { Pagination } from '../../../src/components/ui/Pagination';
import Textarea from '../../../src/components/ui/Textarea';
import { toast } from '../../../src/components/ui/Toast';

export default function AdminPaymentsPage() {
  const [activeTab, setActiveTab] = useState<'deposits' | 'withdrawals'>('deposits');
  const [deposits, setDeposits] = useState<any[]>([]);
  const [withdrawals, setWithdrawals] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedPayment, setSelectedPayment] = useState<any>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showActionModal, setShowActionModal] = useState(false);
  const [actionType, setActionType] = useState<'approve' | 'reject'>('approve');
  const [actionNotes, setActionNotes] = useState('');

  type DepositStatus = 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'CANCELLED' | undefined;
  type WithdrawalStatus = 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'CANCELLED' | 'APPROVED' | 'REJECTED' | undefined;
  const [filters, setFilters] = useState<{
    search: string;
    status: DepositStatus | WithdrawalStatus;
    dateFrom: string;
    dateTo: string;
    page: number;
    limit: number;
  }>({
    search: '',
    status: undefined,
    dateFrom: '',
    dateTo: '',
    page: 1,
    limit: 20
  });

  const [stats, setStats] = useState({
    totalDeposits: 0,
    totalWithdrawals: 0,
    pendingDeposits: 0,
    pendingWithdrawals: 0,
    depositVolume: 0,
    withdrawalVolume: 0,
    successRate: 0
  });

  useEffect(() => {
    loadPayments();
    loadStats();
  }, [activeTab, filters]);

  const loadPayments = async () => {
    try {
      setLoading(true);
      // Only allow valid transaction statuses
      const validStatuses = ['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED'];
      const txFilters: any = {
        ...filters,
        type: (activeTab === 'deposits' ? 'deposit' : 'withdrawal'),
      };
      if (validStatuses.includes(filters.status as string)) {
        txFilters.status = filters.status;
      } else {
        delete txFilters.status;
      }
      console.log('[AdminPaymentsPage] Fetching transactions with filters:', txFilters);
      const response = await transactionsService.getAllTransactions(txFilters);
      console.log('[AdminPaymentsPage] Backend response:', response);
      // Map userId to user and ensure all expected fields exist for frontend compatibility
      const mappedTransactions = response.transactions.map((tx: any) => {
        if (!tx) return {
          user: {},
          paymentMethod: '',
          method: '',
          fees: 0,
          netAmount: 0,
          status: '',
          createdAt: '',
          processedAt: '',
          reference: '',
          amount: 0,
          type: '',
        };
        return {
          ...tx,
          user: tx.userId || {},
          // Defensive: ensure all fields used in UI exist (fallback to empty string or 0)
          paymentMethod: tx.paymentMethod || '',
          method: tx.paymentMethod || tx.method || '', // for withdrawal table consistency
          fees: tx.fees ?? 0,
          netAmount: tx.netAmount ?? (typeof tx.amount === 'number' ? tx.amount : 0),
          status: tx.status || '',
          createdAt: tx.createdAt || '',
          processedAt: tx.processedAt || '',
          reference: tx.reference || '',
          amount: typeof tx.amount === 'number' ? tx.amount : 0,
          type: tx.type || '',
        };
      });
      if (activeTab === 'deposits') {
        setDeposits(mappedTransactions);
      } else {
        setWithdrawals(mappedTransactions);
      }
    } catch (error) {
      console.error('[AdminPaymentsPage] Failed to load', activeTab, error);
      toast.error(`Failed to load ${activeTab}`);
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      console.log('[AdminPaymentsPage] Fetching stats...');
      const [depositStats, withdrawalStats] = await Promise.all([
        depositsService.getDepositStats(),
        withdrawalsService.getWithdrawalStats()
      ]);
      console.log('[AdminPaymentsPage] Deposit stats:', depositStats);
      console.log('[AdminPaymentsPage] Withdrawal stats:', withdrawalStats);
      setStats({
        totalDeposits: depositStats.totalDeposits,
        totalWithdrawals: withdrawalStats.totalWithdrawals,
        pendingDeposits: depositStats.pendingDeposits,
        pendingWithdrawals: withdrawalStats.pendingWithdrawals,
        depositVolume: depositStats.totalAmount,
        withdrawalVolume: withdrawalStats.totalAmount,
        successRate: 0 // No successRate property, fallback to 0
      });
    } catch (error) {
      console.error('[AdminPaymentsPage] Failed to load payment stats:', error);
    }
  };

  const handleWithdrawalAction = async () => {
    if (!selectedPayment || activeTab !== 'withdrawals') return;

    try {
      if (actionType === 'approve') {
        await withdrawalsService.approveWithdrawal(selectedPayment.id);
        toast.success('Withdrawal approved successfully');
      } else {
        await withdrawalsService.rejectWithdrawal(selectedPayment.id);
        toast.success('Withdrawal rejected successfully');
      }

      setShowActionModal(false);
      setActionNotes('');
      loadPayments();
    } catch (error) {
      toast.error(`Failed to ${actionType} withdrawal`);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-NG', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED': case 'APPROVED': return 'success';
      case 'PENDING': return 'warning';
      case 'FAILED': case 'REJECTED': return 'error';
      case 'PROCESSING': return 'info';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED': case 'APPROVED': return <FiCheck className="text-green-500" />;
      case 'PENDING': return <FiClock className="text-yellow-500" />;
      case 'FAILED': case 'REJECTED': return <FiX className="text-red-500" />;
      case 'PROCESSING': return <FiRefreshCw className="text-blue-500 animate-spin" />;
      default: return <FiClock className="text-gray-500" />;
    }
  };

  const depositColumns = [
    {
      key: 'user',
      title: 'User',
      render: (deposit: any) => (
        <div>
          <p className="font-medium text-white">{deposit.user?.firstName || ''} {deposit.user?.lastName || ''}</p>
          <p className="text-sm text-gray-400">{deposit.user?.email || ''}</p>
        </div>
      )
    },
    {
      key: 'amount',
      title: 'Amount',
      render: (deposit: any) => (
        <div>
          <p className="font-semibold text-white">{formatCurrency(deposit.amount ?? 0)}</p>
          <p className="text-sm text-gray-400">Fee: {formatCurrency(deposit.fees ?? 0)}</p>
        </div>
      )
    },
    {
      key: 'method',
      title: 'Method',
      render: (deposit: any) => (
        <div className="flex items-center space-x-2">
          <FiCreditCard className="text-blue-500" />
          <span className="text-gray-300">{deposit?.paymentMethod || ''}</span>
        </div>
      )
    },
    {
      key: 'status',
      title: 'Status',
      render: (deposit: any) => (
        <div className="flex items-center space-x-2">
          {getStatusIcon(deposit.status)}
          <Badge variant={getStatusColor(deposit.status)}>
            {deposit.status}
          </Badge>
        </div>
      )
    },
    {
      key: 'date',
      title: 'Date',
      render: (deposit: any) => (
        <span className="text-gray-400">{formatDate(deposit.createdAt)}</span>
      )
    },
    {
      key: 'actions',
      title: 'Actions',
      render: (deposit: any) => (
        <Button
          size="sm"
          variant="outline"
          onClick={() => {
            setSelectedPayment(deposit);
            setShowDetailsModal(true);
          }}
        >
          <FiEye />
        </Button>
      )
    }
  ];

  const withdrawalColumns = [
    {
      key: 'user',
      title: 'User',
      render: (withdrawal: any) => (
        <div>
          <p className="font-medium text-white">{withdrawal.user?.firstName || ''} {withdrawal.user?.lastName || ''}</p>
          <p className="text-sm text-gray-400">{withdrawal.user?.email || ''}</p>
        </div>
      )
    },
    {
      key: 'amount',
      title: 'Amount',
      render: (withdrawal: any) => (
        <div>
          <p className="font-semibold text-white">{formatCurrency(withdrawal.amount ?? 0)}</p>
          <p className="text-sm text-gray-400">Fee: {formatCurrency(withdrawal.fees ?? 0)}</p>
        </div>
      )
    },
    {
      key: 'method',
      title: 'Method',
      render: (withdrawal: any) => (
        <div className="flex items-center space-x-2">
          <FiCreditCard className="text-blue-500" />
          <span className="text-gray-300">{withdrawal?.method || withdrawal?.paymentMethod || ''}</span>
        </div>
      )
    },
    {
      key: 'status',
      title: 'Status',
      render: (withdrawal: any) => (
        <div className="flex items-center space-x-2">
          {getStatusIcon(withdrawal.status)}
          <Badge variant={getStatusColor(withdrawal.status)}>
            {withdrawal.status}
          </Badge>
        </div>
      )
    },
    {
      key: 'date',
      title: 'Date',
      render: (withdrawal: any) => (
        <span className="text-gray-400">{formatDate(withdrawal.createdAt)}</span>
      )
    },
    {
      key: 'actions',
      title: 'Actions',
      render: (withdrawal: any) => (
        <div className="flex items-center space-x-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => {
              setSelectedPayment(withdrawal);
              setShowDetailsModal(true);
            }}
          >
            <FiEye />
          </Button>
          {withdrawal.status === 'PENDING' && (
            <>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setSelectedPayment(withdrawal);
                  setActionType('approve');
                  setShowActionModal(true);
                }}
                className="text-green-400 hover:text-green-300"
              >
                <FiCheck />
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setSelectedPayment(withdrawal);
                  setActionType('reject');
                  setShowActionModal(true);
                }}
                className="text-red-400 hover:text-red-300"
              >
                <FiX />
              </Button>
            </>
          )}
        </div>
      )
    }
  ];

  return (
    <AdminLayout title="Payment Management">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white">Payment Management</h1>
            <p className="text-gray-400 mt-2">Monitor and manage deposits and withdrawals</p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={loadPayments}>
              <FiRefreshCw className="mr-2" />
              Refresh
            </Button>
            <Button variant="outline">
              <FiDownload className="mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Deposits</p>
                <p className="text-2xl font-bold text-green-400">{stats.totalDeposits.toLocaleString()}</p>
                <p className="text-sm text-gray-400">{formatCurrency(stats.depositVolume)}</p>
              </div>
              <FiTrendingUp className="text-green-500 text-2xl" />
            </div>
          </Card>

          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Withdrawals</p>
                <p className="text-2xl font-bold text-blue-400">{stats.totalWithdrawals.toLocaleString()}</p>
                <p className="text-sm text-gray-400">{formatCurrency(stats.withdrawalVolume)}</p>
              </div>
              <FiTrendingDown className="text-blue-500 text-2xl" />
            </div>
          </Card>

          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Pending Approvals</p>
                <p className="text-2xl font-bold text-yellow-400">
                  {stats.pendingDeposits + stats.pendingWithdrawals}
                </p>
                <p className="text-sm text-gray-400">
                  {stats.pendingDeposits} deposits, {stats.pendingWithdrawals} withdrawals
                </p>
              </div>
              <FiClock className="text-yellow-500 text-2xl" />
            </div>
          </Card>

          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Success Rate</p>
                <p className="text-2xl font-bold text-white">{stats.successRate.toFixed(1)}%</p>
                <p className="text-sm text-green-400">Last 30 days</p>
              </div>
              <FiDollarSign className="text-purple-500 text-2xl" />
            </div>
          </Card>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 bg-gray-800 rounded-lg p-1">
          <button
            onClick={() => setActiveTab('deposits')}
            className={`flex-1 py-2 px-4 rounded-md transition-colors ${
              activeTab === 'deposits'
                ? 'bg-green-600 text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <div className="flex items-center justify-center space-x-2">
              <FiTrendingUp />
              <span>Deposits ({stats.totalDeposits})</span>
            </div>
          </button>
          <button
            onClick={() => setActiveTab('withdrawals')}
            className={`flex-1 py-2 px-4 rounded-md transition-colors ${
              activeTab === 'withdrawals'
                ? 'bg-green-600 text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <div className="flex items-center justify-center space-x-2">
              <FiTrendingDown />
              <span>Withdrawals ({stats.totalWithdrawals})</span>
            </div>
          </button>
        </div>

        {/* Filters */}
        <Card className="bg-gray-800 border-gray-700 p-6">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <Input
              placeholder="Search by user or reference..."
              value={filters.search}
              onChange={(e) => setFilters({ ...filters, search: e.target.value })}
            />

            <Select
              value={filters.status || ''}
              onChange={(e) => {
                const value = e.target.value;
                setFilters({ ...filters, status: value === '' ? undefined : value as DepositStatus | WithdrawalStatus });
              }}
              options={[
                { value: '', label: 'All Status' },
                { value: 'PENDING', label: 'Pending' },
                { value: 'PROCESSING', label: 'Processing' },
                { value: 'COMPLETED', label: 'Completed' },
                { value: 'APPROVED', label: 'Approved' },
                { value: 'REJECTED', label: 'Rejected' },
                { value: 'FAILED', label: 'Failed' },
                { value: 'CANCELLED', label: 'Cancelled' }
              ]}
            />

            <Input
              type="date"
              value={filters.dateFrom}
              onChange={(e) => setFilters({ ...filters, dateFrom: e.target.value })}
              placeholder="From Date"
            />

            <Input
              type="date"
              value={filters.dateTo}
              onChange={(e) => setFilters({ ...filters, dateTo: e.target.value })}
              placeholder="To Date"
            />

            <Button
              variant="outline"
              onClick={() => setFilters({
                search: '',
                status: undefined,
                dateFrom: '',
                dateTo: '',
                page: 1,
                limit: 20
              })}
            >
              Clear Filters
            </Button>
          </div>
        </Card>

        {/* Payments Table */}
        <Card className="bg-gray-800 border-gray-700">
          <div className="p-6 border-b border-gray-700">
            <h3 className="text-lg font-semibold text-white">
              {activeTab === 'deposits' ? 'Deposits' : 'Withdrawals'}
            </h3>
          </div>

          <Table
            columns={activeTab === 'deposits' ? depositColumns : withdrawalColumns}
            data={activeTab === 'deposits' ? deposits : withdrawals}
            loading={loading}
            emptyMessage={`No ${activeTab} found`}
          />
        </Card>
      </div>

      {/* Payment Details Modal */}
      <Modal
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        title="Payment Details"
        size="lg"
      >
        {selectedPayment && (
          <div className="space-y-6">
            {/* Payment Info */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold text-white mb-2">Payment Information</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Reference:</span>
                    <span className="text-white font-mono">{selectedPayment.reference}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Amount:</span>
                    <span className="text-white">{formatCurrency(selectedPayment.amount)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Fees:</span>
                    <span className="text-white">{formatCurrency(selectedPayment.fees || 0)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Net Amount:</span>
                    <span className="text-white">{formatCurrency(selectedPayment.netAmount || selectedPayment.amount)}</span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-semibold text-white mb-2">User Information</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Name:</span>
                    <span className="text-white">{selectedPayment.user?.firstName} {selectedPayment.user?.lastName}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Email:</span>
                    <span className="text-white">{selectedPayment.user?.email}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Status:</span>
                    <Badge variant={getStatusColor(selectedPayment.status)}>
                      {selectedPayment.status}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>

            {/* Timeline */}
            <div>
              <h4 className="font-semibold text-white mb-4">Timeline</h4>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <div>
                    <p className="text-white text-sm">Payment initiated</p>
                    <p className="text-gray-400 text-xs">{formatDate(selectedPayment.createdAt)}</p>
                  </div>
                </div>
                {selectedPayment.processedAt && (
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <div>
                      <p className="text-white text-sm">Payment processed</p>
                      <p className="text-gray-400 text-xs">{formatDate(selectedPayment.processedAt)}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Notes */}
            {selectedPayment.notes && (
              <div>
                <h4 className="font-semibold text-white mb-2">Notes</h4>
                <p className="text-gray-300 text-sm bg-gray-700 p-3 rounded-lg">
                  {selectedPayment.notes}
                </p>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* Action Modal */}
      <Modal
        isOpen={showActionModal}
        onClose={() => setShowActionModal(false)}
        title={`${actionType === 'approve' ? 'Approve' : 'Reject'} Withdrawal`}
      >
        <div className="space-y-4">
          <p className="text-gray-300">
            Are you sure you want to {actionType} this withdrawal?
          </p>
          
          {selectedPayment && (
            <div className="p-4 bg-gray-700 rounded-lg">
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">User:</span>
                  <span className="text-white">{selectedPayment.user?.firstName} {selectedPayment.user?.lastName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Amount:</span>
                  <span className="text-white">{formatCurrency(selectedPayment.amount)}</span>
                </div>
              </div>
            </div>
          )}

          <Textarea
            label="Notes"
            value={actionNotes}
            onChange={(e) => setActionNotes(e.target.value)}
            placeholder={`Add notes for this ${actionType}...`}
            rows={3}
          />

          <div className="flex justify-end space-x-3 pt-4">
            <Button
              variant="outline"
              onClick={() => setShowActionModal(false)}
            >
              Cancel
            </Button>
            <Button
              variant={actionType === 'approve' ? 'primary' : 'danger'}
              onClick={handleWithdrawalAction}
            >
              {actionType === 'approve' ? 'Approve' : 'Reject'} Withdrawal
            </Button>
          </div>
        </div>
      </Modal>
    </AdminLayout>
  );
}
