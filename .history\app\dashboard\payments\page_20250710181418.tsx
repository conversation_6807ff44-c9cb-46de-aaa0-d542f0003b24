"use client";

import React, { useState, useEffect, useRef } from 'react';
import { transactionsService, userService, depositsService, withdrawalsService, paystackService } from '../../../src/services';
import { motion } from 'framer-motion';
import AOS from 'aos';
import 'aos/dist/aos.css';
import { useAuth } from '../../../src/hooks/use-auth';
import { useRouter } from 'next/navigation';
import DashboardLayout from '../../../src/components/dashboard/DashboardLayout';
import { StatCard } from '../../../src/components/dashboard/DashboardCard';
import { FormModal } from '../../../src/components/ui/Modal';
import { FormInput, SelectInput } from '../../../src/components/ui/FormInput';
import Table, { StatusBadge, ActionButton } from '../../../src/components/ui/Table';
import { showToast } from '../../../src/components/ui/Toast';
import { 
  FiPlus, 
  FiDollarSign, 
  FiCreditCard, 
  FiArrowUpRight,
  FiArrowDownLeft,
  FiEye,
  FiDownload,
  FiRefreshCw,
  FiSmartphone
} from 'react-icons/fi';

import { Transaction, CreateTransactionData } from '../../../src/types/transactions';



export default function PaymentsPage() {
  console.log('[PaymentsPage] Rendered');
  const { user, isAuthenticated, isLoading } = useAuth();
  console.log('[PaymentsPage] useAuth:', { user, isAuthenticated, isLoading });
  const router = useRouter();
  const [transactions, setTransactions] = useState<Transaction[]>([]);

  // Balance and total deposits state
  const [balance, setBalance] = useState<number | null>(null);
  const [totalDeposits, setTotalDeposits] = useState<number>(0);

  // Fetch transactions from backend
  const fetchTransactions = async () => {
    if (!user?.id) {
      console.log('[PaymentsPage] No user id, cannot fetch transactions');
      return;
    }
    try {
      console.log('[PaymentsPage] Fetching transactions for user:', user.id);
      const res = await transactionsService.getUserTransactions(user.id);
      console.log('[PaymentsPage] Transactions API response:', res);
      setTransactions(res.transactions || []);
    } catch (err) {
      console.error('[PaymentsPage] Failed to fetch transactions:', err);
      showToast.error('Failed to fetch transactions');
    }
  };


  // Fetch balance from backend (map to correct field)
  const fetchBalance = async () => {
    if (!user?.id) return;
    try {
      const res = await userService.getUserBalance(user.id);
      // Map to backend's balance field if available, else fallback
      setBalance(res.balance ?? res.availableBalance ?? 0);
    } catch (err) {
      showToast.error('Failed to fetch balance');
    }
  };

  // Fetch total deposits from backend
  const fetchTotalDeposits = async () => {
    if (!user?.id) return;
    try {
      const total = await depositsService.getUserTotalDeposits(user.id);
      setTotalDeposits(total);
    } catch (err) {
      showToast.error('Failed to fetch total deposits');
    }
  };

  const [showDepositModal, setShowDepositModal] = useState(false);
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [depositForm, setDepositForm] = useState({
    amount: '',
    method: 'BANK_TRANSFER',
    bankCode: '',
    accountNumber: '',
    accountName: '',
  });

  // Banks list for select input
  const [banks, setBanks] = useState<{ name: string; code: string }[]>([]);
  useEffect(() => {
    // Fetch banks list for withdrawal using Paystack API
    async function fetchBanks() {
      try {
        const bankListRes = await paystackService.getBankList();
        if (bankListRes && Array.isArray(bankListRes.data)) {
          setBanks(bankListRes.data.map(b => ({ name: b.name, code: b.code })));
        } else {
          setBanks([]);
        }
      } catch (err) {
        console.error('[Withdraw] Failed to fetch banks from Paystack:', err);
        setBanks([]);
      }
    }
    fetchBanks();
  }, []);

  // Bank account verification (dummy, replace with real API if available)
  const verifyAccount = async () => {
    // You should replace this with a real API call to verify account number and bank code
    if (depositForm.accountNumber.length === 10 && depositForm.bankCode) {
      setDepositForm(prev => ({ ...prev, accountName: 'Verified Account Name' }));
      showToast.success('Account verified!');
    } else {
      showToast.error('Enter a valid 10-digit account number and select a bank.');
    }
  };



  useEffect(() => {
    console.log('[PaymentsPage] useEffect triggered. user:', user);
    AOS.init({
      duration: 1000,
      once: false,
      easing: 'ease-out-cubic',
      offset: 50,
    });
    AOS.refresh();
    if (user?.id) {
      console.log('[PaymentsPage] user.id present, fetching transactions, balance, and total deposits:', user.id);
      fetchTransactions();
      fetchBalance();
      fetchTotalDeposits();
    } else {
      console.log('[PaymentsPage] No user.id in useEffect');
    }
  }, [user]);

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login');
      return;
    }
    if (!isLoading && user?.role === 'ADMIN') {
      router.push('/admin/dashboard');
      return;
    }
  }, [isAuthenticated, isLoading, user, router]);

  // Paystack integration
  const paystackScriptAdded = useRef(false);
  useEffect(() => {
    if (!paystackScriptAdded.current) {
      const script = document.createElement('script');
      script.src = 'https://js.paystack.co/v1/inline.js';
      script.async = true;
      document.body.appendChild(script);
      paystackScriptAdded.current = true;
    }
  }, []);

  const handleDeposit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      const amount = parseInt(depositForm.amount);
      console.log('[Deposit] User:', user);
      console.log('[Deposit] Amount:', amount);
      if (isNaN(amount) || amount < 1) {
        showToast.error('Please enter a valid amount (minimum ₦1)');
        setLoading(false);
        return;
      }
      const email = user?.email || '';
      const paystackKey = process.env.NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY || process.env.PAYSTACK_PUBLIC_KEY;
      console.log('[Deposit] Paystack Key:', paystackKey);
      if (!paystackKey || !paystackKey.startsWith('pk_')) {
        showToast.error('Paystack public key is not set or invalid.');
        setLoading(false);
        return;
      }
      if (!email || !email.includes('@')) {
        showToast.error('A valid user email is required for payment.');
        setLoading(false);
        return;
      }
      // Pass paymentMethod as required by InitiateDepositData
      console.log('[Deposit] Calling depositsService.initiateDeposit', { amount, paymentMethod: 'CARD' });
      const res = await depositsService.initiateDeposit({ amount, paymentMethod: 'CARD' });
      console.log('[Deposit] initiateDeposit response:', res);
      // Accept both possible backend response shapes
      const reference = res.paymentData?.reference;
      console.log('[Deposit] Paystack reference:', reference);
      if (!reference) {
        showToast.error('Failed to get Paystack reference from backend.');
        setLoading(false);
        return;
      }
      // @ts-ignore
      if (!window.PaystackPop) {
        showToast.error('Paystack script not loaded');
        setLoading(false);
        return;
      }
      // Use Paystack inline modal with backend reference
      // @ts-ignore
      window.PaystackPop.setup({
        key: paystackKey,
        email,
        amount: amount * 100,
        currency: 'NGN',
        ref: reference,
        callback: function(response: any) {
          // Paystack requires callback to be a plain function, not async
          (async () => {
            try {
              showToast.success('Verifying deposit with backend...');
              const verifyRes = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/paystack/deposit/verify/${reference}`);
              const verifyData = await verifyRes.json();
              if (verifyRes.ok && verifyData.status && verifyData.data && verifyData.data.status === 'success') {
                showToast.success('Deposit successful! Your balance has been updated.');
                setShowDepositModal(false);
                setDepositForm({ amount: '', method: 'BANK_TRANSFER', bankCode: '', accountNumber: '', accountName: '' });
                // Instantly update totalDeposits if backend returned it
                if (typeof verifyData.totalDeposits === 'number') {
                  setTotalDeposits(verifyData.totalDeposits);
                }
                // Refetch from backend for consistency
                setTimeout(async () => {
                  console.log('[Deposit] Refetching after Paystack verify...');
                  const bal = await fetchBalance();
                  const txs = await fetchTransactions();
                  const total = await fetchTotalDeposits();
                  console.log('[Deposit] Refetched balance:', bal);
                  console.log('[Deposit] Refetched transactions:', txs);
                  console.log('[Deposit] Refetched total deposits:', total);
                }, 2000);
              } else {
                showToast.error('Deposit verification failed. Please contact support.');
              }
            } catch (err) {
              showToast.error('Error verifying deposit. Please contact support.');
              console.error('[Deposit] Error verifying deposit:', err);
            }
            setLoading(false);
          })();
        },
        onClose: function() {
          showToast.error('Deposit cancelled');
          setLoading(false);
        },
      }).openIframe();
    } catch (error) {
      console.error('[Deposit] Failed to process deposit:', error);
      showToast.error('Failed to process deposit');
      setLoading(false);
    }
  };

  // Paystack withdrawal handler
  const handleWithdraw = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      const amount = parseInt(depositForm.amount);
      console.log('[Withdraw] User:', user);
      console.log('[Withdraw] Amount:', amount);
      if (isNaN(amount) || amount < 1) {
        showToast.error('Please enter a valid amount (minimum ₦1)');
        setLoading(false);
        return;
      }
      const email = user?.email || '';
      const paystackKey = process.env.NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY || process.env.PAYSTACK_PUBLIC_KEY;
      console.log('[Withdraw] Paystack Key:', paystackKey);
      if (!paystackKey || !paystackKey.startsWith('pk_')) {
        showToast.error('Paystack public key is not set or invalid.');
        setLoading(false);
        return;
      }
      if (!email || !email.includes('@')) {
        showToast.error('A valid user email is required for withdrawal.');
        setLoading(false);
        return;
      }
      // Call backend to initiate withdrawal and get Paystack reference
      console.log('[Withdraw] Calling withdrawalsService.initiateWithdrawal', { amount, type: 'EXTERNAL_PAYOUT' });
      const res = await withdrawalsService.initiateWithdrawal({ amount, type: 'EXTERNAL_PAYOUT' });
      console.log('[Withdraw] initiateWithdrawal response:', res);
      // Accept both possible backend response shapes
      // Use withdrawal.reference as the Paystack reference
      const reference = (res.withdrawal && res.withdrawal.reference) || '';
      console.log('[Withdraw] Paystack reference:', reference);
      if (!reference) {
        showToast.error('Failed to get Paystack reference from backend.');
        setLoading(false);
        return;
      }
      // @ts-ignore
      if (!window.PaystackPop) {
        showToast.error('Paystack script not loaded');
        setLoading(false);
        return;
      }
      // Use Paystack inline modal with backend reference
      // @ts-ignore
      window.PaystackPop.setup({
        key: paystackKey,
        email,
        amount: amount * 100,
        currency: 'NGN',
        ref: reference,
        callback: function(response: any) {
          (async () => {
            try {
              showToast.success('Verifying withdrawal with backend...');
              const verifyRes = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/paystack/withdraw/verify/${reference}`);
              const verifyData = await verifyRes.json();
              if (verifyRes.ok && verifyData.status && verifyData.data && verifyData.data.status === 'success') {
                showToast.success('Withdrawal successful! Your balance has been updated.');
                setShowWithdrawModal(false);
                setDepositForm({ amount: '', method: 'BANK_TRANSFER', bankCode: '', accountNumber: '', accountName: '' });
                // Refetch from backend for consistency
                setTimeout(async () => {
                  console.log('[Withdraw] Refetching after Paystack verify...');
                  const bal = await fetchBalance();
                  const txs = await fetchTransactions();
                  const total = await fetchTotalDeposits();
                  console.log('[Withdraw] Refetched balance:', bal);
                  console.log('[Withdraw] Refetched transactions:', txs);
                  console.log('[Withdraw] Refetched total deposits:', total);
                }, 2000);
              } else {
                showToast.error('Withdrawal verification failed. Please contact support.');
              }
            } catch (err) {
              showToast.error('Error verifying withdrawal. Please contact support.');
              console.error('[Withdraw] Error verifying withdrawal:', err);
            }
            setLoading(false);
          })();
        },
        onClose: function() {
          showToast.error('Withdrawal cancelled');
          setLoading(false);
        },
      }).openIframe();
    } catch (error) {
      console.error('[Withdraw] Failed to process withdrawal:', error);
      showToast.error('Failed to process withdrawal');
      setLoading(false);
    }
  };

  // Helper to normalize type for color/icon
  const getTypeMeta = (type: string) => {
    const t = type.toLowerCase();
    if (t.includes('deposit')) return { color: 'text-green-400', icon: <FiArrowDownLeft className="w-4 h-4 text-green-400" />, sign: '+' };
    if (t.includes('withdrawal')) return { color: 'text-red-400', icon: <FiArrowUpRight className="w-4 h-4 text-red-400" />, sign: '-' };
    if (t.includes('interest')) return { color: 'text-purple-400', icon: <FiDollarSign className="w-4 h-4 text-purple-400" />, sign: '+' };
    if (t.includes('penalty')) return { color: 'text-yellow-400', icon: <FiRefreshCw className="w-4 h-4 text-yellow-400" />, sign: '-' };
    if (t.includes('fee')) return { color: 'text-yellow-400', icon: <FiCreditCard className="w-4 h-4 text-yellow-400" />, sign: '-' };
    if (t.includes('bonus')) return { color: 'text-green-400', icon: <FiPlus className="w-4 h-4 text-green-400" />, sign: '+' };
    if (t.includes('refund')) return { color: 'text-blue-400', icon: <FiSmartphone className="w-4 h-4 text-blue-400" />, sign: '+' };
    return { color: 'text-gray-400', icon: <FiSmartphone className="w-4 h-4 text-gray-400" />, sign: '' };
  };

  const transactionColumns = [
    {
      key: 'type' as keyof Transaction,
      title: 'Type',
      render: (value: string) => {
        const meta = getTypeMeta(value);
        return (
          <div className="flex items-center space-x-2">
            {meta.icon}
            <span className="capitalize">{value.replace(/_/g, ' ').toLowerCase()}</span>
          </div>
        );
      },
    },
    {
      key: 'amount' as keyof Transaction,
      title: 'Amount',
      render: (value: number, row: Transaction) => {
        const meta = getTypeMeta(row.type);
        return (
          <div className={`font-medium ${meta.color}`}>
            {meta.sign}₦{value.toLocaleString()}
          </div>
        );
      },
    },
    {
      key: 'status' as keyof Transaction,
      title: 'Status',
      render: (value: string) => {
        const status = value && value.trim() ? value.toUpperCase() : 'COMPLETED';
        let variant: 'success' | 'warning' | 'danger' = 'success';
        if (status === 'PENDING') variant = 'warning';
        else if (status !== 'COMPLETED') variant = 'danger';
        return (
          <StatusBadge 
            status={status}
            variant={variant}
          />
        );
      },
    },
    {
      key: 'date' as keyof Transaction,
      title: 'Date',
      render: (value: string) => new Date(value).toLocaleDateString(),
    },
    {
      key: 'reference' as keyof Transaction,
      title: 'Reference',
      render: (value: string) => (
        <span className="font-mono text-sm text-gray-400">{value}</span>
      ),
    },
    {
      key: 'id' as keyof Transaction,
      title: 'Actions',
      render: (value: string) => (
        <div className="flex space-x-2">
          <ActionButton onClick={() => console.log('View', value)} variant="default">
            <FiEye className="w-4 h-4" />
          </ActionButton>
          <ActionButton onClick={() => console.log('Download', value)} variant="primary">
            <FiDownload className="w-4 h-4" />
          </ActionButton>
        </div>
      ),
    },
  ];



  const totalWithdrawals = transactions
    .filter(t => t.type === 'WITHDRAWAL' && t.status === 'COMPLETED')
    .reduce((sum, t) => sum + t.amount, 0);

  const pendingTransactions = transactions.filter(t => t.status === 'PENDING').length;

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center" data-aos="fade-in">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto mb-4"></div>
            <p className="text-gray-400">Loading payments...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!isAuthenticated || user?.role === 'ADMIN') {
    return null;
  }

  return (
    <DashboardLayout title="Payments">
      <div className="space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0"
          data-aos="fade-down"
        >
          <div>
            <h1 className="text-2xl font-bold text-white mb-2">Payments & Transactions</h1>
            <p className="text-gray-400">Manage your deposits, withdrawals, and payment methods</p>
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={() => setShowDepositModal(true)}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
              data-aos="fade-left"
              data-aos-delay="100"
            >
              <FiArrowDownLeft className="w-4 h-4" />
              <span>Deposit</span>
            </button>
            <button
              onClick={() => setShowWithdrawModal(true)}
              className="flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
              data-aos="fade-left"
              data-aos-delay="200"
            >
              <FiArrowUpRight className="w-4 h-4" />
              <span>Withdraw</span>
            </button>
          </div>
        </motion.div>


        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div data-aos="fade-up" data-aos-delay="50">
            <StatCard
              title="Available Balance"
              value={balance !== null ? `₦${balance.toLocaleString()}` : '—'}
              subtitle="Current wallet balance"
              icon={FiDollarSign}
              color="blue"
            />
          </div>
          <div data-aos="fade-up" data-aos-delay="100">
            <StatCard
              title="Total Deposits"
              value={`₦${totalDeposits.toLocaleString()}`}
              subtitle="All time"
              icon={FiArrowDownLeft}
              color="green"
            />
          </div>
          <div data-aos="fade-up" data-aos-delay="200">
            <StatCard
              title="Total Withdrawals"
              value={`₦${totalWithdrawals.toLocaleString()}`}
              subtitle="All time"
              icon={FiArrowUpRight}
              color="red"
            />
          </div>
        </div>

        {/* Transactions Table */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          data-aos="fade-up"
          data-aos-delay="400"
        >
          <Table
            data={transactions}
            columns={transactionColumns}
            searchable
            searchPlaceholder="Search transactions..."
            emptyMessage="No transactions found"
          />
        </motion.div>


        {/* Deposit Modal */}
        <FormModal
          isOpen={showDepositModal}
          onClose={() => setShowDepositModal(false)}
          title="Make Deposit"
          onSubmit={handleDeposit}
          submitText="Deposit Funds"
          isLoading={loading}
        >
          <div className="space-y-4">
            <FormInput
              label="Amount"
              name="amount"
              type="number"
              value={depositForm.amount}
              onChange={(e) => setDepositForm(prev => ({ ...prev, amount: e.target.value }))}
              placeholder="100000"
              required
            />
            {/* Payment method is now always Paystack/Card */}
            <FormInput
              label="Payment Method"
              name="method"
              value="Paystack (Card/Bank/USSD)"
              readOnly
              disabled
            />
            {/* Savings Plan selection removed as it's not relevant for deposit */}
          </div>
        </FormModal>

        {/* Withdraw Modal */}
        <FormModal
          isOpen={showWithdrawModal}
          onClose={() => setShowWithdrawModal(false)}
          title="Withdraw Funds"
          onSubmit={handleWithdraw}
          submitText="Withdraw"
          isLoading={loading}
        >
          <div className="space-y-4">
            <FormInput
              label="Amount"
              name="amount"
              type="number"
              value={depositForm.amount}
              onChange={(e) => setDepositForm(prev => ({ ...prev, amount: e.target.value }))}
              placeholder="100000"
              required
            />
            <SelectInput
              label="Bank"
              name="bankCode"
              value={depositForm.bankCode}
              onChange={e => setDepositForm(prev => ({ ...prev, bankCode: e.target.value }))}
              required
              options={banks.map(bank => ({ label: bank.name, value: bank.code }))}
            />
            <FormInput
              label="Account Number"
              name="accountNumber"
              type="text"
              value={depositForm.accountNumber}
              onChange={e => setDepositForm(prev => ({ ...prev, accountNumber: e.target.value.replace(/\D/g, '').slice(0, 10) }))}
              placeholder="**********"
              required
            />
            <div className="flex items-center space-x-2">
              <FormInput
                label="Account Name"
                name="accountName"
                type="text"
                value={depositForm.accountName}
                readOnly
                disabled
              />
              <button
                type="button"
                className="px-3 py-2 bg-blue-600 text-white rounded-lg"
                onClick={verifyAccount}
              >Verify</button>
            </div>
            {/* Payment method is now always Paystack/Card */}
            <FormInput
              label="Payment Method"
              name="method"
              value="Paystack (Card/Bank/USSD)"
              readOnly
              disabled
            />
          </div>
        </FormModal>

      </div>
    </DashboardLayout>
  );
}
