"use client";

import { motion } from 'framer-motion';
import React, { useEffect, useState } from "react";
import {
    <PERSON>Bar<PERSON>hart,
    FiClock,
    FiDollarSign,
    FiDownload,
    FiEye,
    FiFileText,
    FiPieChart,
    FiRefreshCw,
    FiTrendingUp,
    FiUsers,
} from "react-icons/fi";
import AdminLayout from "../../../src/components/admin/AdminLayout";
import { Badge } from "../../../src/components/ui/Badge";
import { Button } from "../../../src/components/ui/Button";
import { Card } from "../../../src/components/ui/Card";
import { Input } from "../../../src/components/ui/Input";
import { Modal } from "../../../src/components/ui/Modal";
import { Select } from "../../../src/components/ui/Select";
import { Table } from "../../../src/components/ui/Table";
import { toast } from "../../../src/components/ui/Toast";
import { adminService } from "../../../src/services";

const reportTypes = [
  {
    id: "user-activity",
    name: "User Activity Report",
    description: "Detailed user engagement and activity metrics",
    icon: FiUsers,
    color: "bg-blue-600",
  },
  {
    id: "financial-summary",
    name: "Financial Summary Report",
    description: "Comprehensive financial transactions and balances",
    icon: FiDollarSign,
    color: "bg-green-600",
  },
  {
    id: "savings-performance",
    name: "Savings Performance Report",
    description: "Savings plans performance and completion rates",
    icon: FiTrendingUp,
    color: "bg-purple-600",
  },
  {
    id: "kyc-compliance",
    name: "KYC Compliance Report",
    description: "KYC verification status and compliance metrics",
    icon: FiFileText,
    color: "bg-orange-600",
  },
  {
    id: "transaction-analysis",
    name: "Transaction Analysis Report",
    description: "Detailed transaction patterns and analysis",
    icon: FiBarChart,
    color: "bg-indigo-600",
  },
  {
    id: "revenue-breakdown",
    name: "Revenue Breakdown Report",
    description: "Revenue sources and fee collection analysis",
    icon: FiPieChart,
    color: "bg-pink-600",
  },
];

export default function AdminReportsPage() {
  const [reports, setReports] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [showGenerateModal, setShowGenerateModal] = useState(false);
  const [selectedReportType, setSelectedReportType] = useState('');
  const [generatingReport, setGeneratingReport] = useState(false);
  
  const [reportConfig, setReportConfig] = useState({
    dateFrom: '',
    dateTo: '',
    format: 'PDF',
    includeCharts: true,
    emailDelivery: false,
    recipients: ''
  });

  const [filters, setFilters] = useState({
    type: '',
    status: '',
    dateFrom: '',
    dateTo: ''
  });

  useEffect(() => {
    loadReports();
  }, [filters]);

  const loadReports = async () => {
    try {
      setLoading(true);
      const response = await adminService.getReports(filters);
      setReports(response.reports);
    } catch (error) {
      toast.error('Failed to load reports');
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateReport = async () => {
    try {
      if (!selectedReportType || !reportConfig.dateFrom || !reportConfig.dateTo) {
        toast.error('Please fill in all required fields');
        return;
      }

      setGeneratingReport(true);
      
      const response = await adminService.generateReport({
        name: `${selectedReportType} Report`,
        type: selectedReportType as "CUSTOM" | "USER_ACTIVITY" | "FINANCIAL_SUMMARY" | "TRANSACTION_REPORT" | "KYC_REPORT" | "SAVINGS_REPORT",
        parameters: {},
        ...reportConfig,
        format: reportConfig.format as "CSV" | "EXCEL" | "PDF",
        recipients: reportConfig.recipients ? reportConfig.recipients.split(',').map((email: string) => email.trim()) : []
      });

      toast.success('Report generation started. You will be notified when ready.');
      setShowGenerateModal(false);
      setSelectedReportType('');
      setReportConfig({
        dateFrom: '',
        dateTo: '',
        format: 'PDF',
        includeCharts: true,
        emailDelivery: false,
        recipients: ''
      });
      
      loadReports();
    } catch (error: any) {
      toast.error(error.message || 'Failed to generate report');
    } finally {
      setGeneratingReport(false);
    }
  };

  const handleDownloadReport = async (reportId: string) => {
    try {
      const blob = await adminService.downloadReport(reportId);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `report-${reportId}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success('Report downloaded successfully');
    } catch (error) {
      toast.error('Failed to download report');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-NG', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string): 'default' | 'success' | 'warning' | 'error' | 'info' => {
    switch (status) {
      case 'COMPLETED': return 'success';
      case 'GENERATING': return 'warning';
      case 'FAILED': return 'error';
      case 'SCHEDULED': return 'info';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED': return <FiDownload className="text-green-500" />;
      case 'GENERATING': return <FiRefreshCw className="text-yellow-500 animate-spin" />;
      case 'FAILED': return <FiFileText className="text-red-500" />;
      case 'SCHEDULED': return <FiClock className="text-blue-500" />;
      default: return <FiFileText className="text-gray-500" />;
    }
  };

  const columns = [
    {
      key: 'name',
      title: 'Report Name',
      render: (report: any) => (
        <div className="flex items-center space-x-3">
          <div className={`w-10 h-10 ${reportTypes.find(t => t.id === report.type)?.color || 'bg-gray-600'} rounded-lg flex items-center justify-center`}>
            {React.createElement(reportTypes.find(t => t.id === report.type)?.icon || FiFileText, {
              className: "text-white"
            })}
          </div>
          <div>
            <p className="font-medium text-white">{report.name}</p>
            <p className="text-sm text-gray-400">{report.description}</p>
          </div>
        </div>
      )
    },
    {
      key: 'type',
      title: 'Type',
      render: (report: any) => (
        <Badge variant="default">
          {reportTypes.find(t => t.id === report.type)?.name || report.type}
        </Badge>
      )
    },
    {
      key: 'period',
      title: 'Period',
      render: (report: any) => (
        <div className="text-sm text-gray-400">
          <p>{formatDate(report.dateFrom)} -</p>
          <p>{formatDate(report.dateTo)}</p>
        </div>
      )
    },
    {
      key: 'status',
      title: 'Status',
      render: (report: any) => (
        <div className="flex items-center space-x-2">
          {getStatusIcon(report.status)}
          <Badge variant={getStatusColor(report.status)}>
            {report.status}
          </Badge>
        </div>
      )
    },
    {
      key: 'size',
      title: 'Size',
      render: (report: any) => (
        <span className="text-gray-400">
          {report.fileSize ? `${(report.fileSize / 1024 / 1024).toFixed(2)} MB` : '-'}
        </span>
      )
    },
    {
      key: 'createdAt',
      title: 'Generated',
      render: (report: any) => (
        <span className="text-gray-400">{formatDate(report.createdAt)}</span>
      )
    },
    {
      key: 'actions',
      title: 'Actions',
      render: (report: any) => (
        <div className="flex items-center space-x-2">
          {report.status === 'COMPLETED' && (
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleDownloadReport(report.id)}
            >
              <FiDownload />
            </Button>
          )}
          <Button
            size="sm"
            variant="outline"
          >
            <FiEye />
          </Button>
        </div>
      )
    }
  ];

  return (
    <AdminLayout title="Reports">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white">Reports</h1>
            <p className="text-gray-400 mt-2">Generate and manage platform reports</p>
          </div>
          <div className="flex space-x-2">
            <Button
              onClick={() => setShowGenerateModal(true)}
              className="bg-green-600 hover:bg-green-700"
            >
              <FiFileText className="mr-2" />
              Generate Report
            </Button>
            <Button variant="outline" onClick={loadReports}>
              <FiRefreshCw className="mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Report Types Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {reportTypes.map((reportType) => {
            const Icon = reportType.icon;
            return (
              <motion.div
                key={reportType.id}
                whileHover={{ scale: 1.02 }}
                className="cursor-pointer"
                onClick={() => {
                  setSelectedReportType(reportType.id);
                  setShowGenerateModal(true);
                }}
              >
                <Card className="bg-gray-800 border-gray-700 p-6 hover:border-green-500 transition-colors">
                  <div className="flex items-start space-x-4">
                    <div className={`w-12 h-12 ${reportType.color} rounded-lg flex items-center justify-center`}>
                      <Icon className="text-white text-xl" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-white mb-2">{reportType.name}</h3>
                      <p className="text-gray-400 text-sm">{reportType.description}</p>
                    </div>
                  </div>
                </Card>
              </motion.div>
            );
          })}
        </div>

        {/* Filters */}
        <Card className="bg-gray-800 border-gray-700 p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Select
              value={filters.type}
              onChange={(e) => setFilters({ ...filters, type: e.target.value })}
              options={[
                { value: '', label: 'All Types' },
                ...reportTypes.map(type => ({ value: type.id, label: type.name }))
              ]}
            />

            <Select
              value={filters.status}
              onChange={(e) => setFilters({ ...filters, status: e.target.value })}
              options={[
                { value: '', label: 'All Status' },
                { value: 'COMPLETED', label: 'Completed' },
                { value: 'GENERATING', label: 'Generating' },
                { value: 'FAILED', label: 'Failed' },
                { value: 'SCHEDULED', label: 'Scheduled' }
              ]}
            />

            <Input
              type="date"
              value={filters.dateFrom}
              onChange={(e) => setFilters({ ...filters, dateFrom: e.target.value })}
              placeholder="From Date"
            />

            <Input
              type="date"
              value={filters.dateTo}
              onChange={(e) => setFilters({ ...filters, dateTo: e.target.value })}
              placeholder="To Date"
            />
          </div>
        </Card>

        {/* Reports Table */}
        <Card className="bg-gray-800 border-gray-700">
          <div className="p-6 border-b border-gray-700">
            <h3 className="text-lg font-semibold text-white">Generated Reports</h3>
          </div>

          <Table
            columns={columns}
            data={reports}
            loading={loading}
            emptyMessage="No reports found"
          />
        </Card>
      </div>

      {/* Generate Report Modal */}
      <Modal
        isOpen={showGenerateModal}
        onClose={() => setShowGenerateModal(false)}
        title="Generate Report"
        size="lg"
      >
        <div className="space-y-6">
          {/* Report Type Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-3">
              Report Type
            </label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {reportTypes.map((type) => {
                const Icon = type.icon;
                return (
                  <div
                    key={type.id}
                    onClick={() => setSelectedReportType(type.id)}
                    className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                      selectedReportType === type.id
                        ? 'border-green-500 bg-green-600/10'
                        : 'border-gray-600 hover:border-gray-500'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <div className={`w-8 h-8 ${type.color} rounded-lg flex items-center justify-center`}>
                        <Icon className="text-white text-sm" />
                      </div>
                      <div>
                        <p className="font-medium text-white text-sm">{type.name}</p>
                        <p className="text-gray-400 text-xs">{type.description}</p>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Date Range */}
          <div className="grid grid-cols-2 gap-4">
            <Input
              label="From Date"
              type="date"
              value={reportConfig.dateFrom}
              onChange={(e) => setReportConfig({ ...reportConfig, dateFrom: e.target.value })}
              required
            />

            <Input
              label="To Date"
              type="date"
              value={reportConfig.dateTo}
              onChange={(e) => setReportConfig({ ...reportConfig, dateTo: e.target.value })}
              required
            />
          </div>

          {/* Report Options */}
          <div className="space-y-4">
            <Select
              label="Format"
              value={reportConfig.format}
              onChange={(e) => setReportConfig({ ...reportConfig, format: e.target.value })}
              options={[
                { value: 'PDF', label: 'PDF Document' },
                { value: 'EXCEL', label: 'Excel Spreadsheet' },
                { value: 'CSV', label: 'CSV File' }
              ]}
            />

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="includeCharts"
                checked={reportConfig.includeCharts}
                onChange={(e) => setReportConfig({ ...reportConfig, includeCharts: e.target.checked })}
                className="rounded border-gray-600 bg-gray-700 text-green-600"
              />
              <label htmlFor="includeCharts" className="text-white">
                Include charts and visualizations
              </label>
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="emailDelivery"
                checked={reportConfig.emailDelivery}
                onChange={(e) => setReportConfig({ ...reportConfig, emailDelivery: e.target.checked })}
                className="rounded border-gray-600 bg-gray-700 text-green-600"
              />
              <label htmlFor="emailDelivery" className="text-white">
                Email report when ready
              </label>
            </div>

            {reportConfig.emailDelivery && (
              <Input
                label="Email Recipients"
                value={reportConfig.recipients}
                onChange={(e) => setReportConfig({ ...reportConfig, recipients: e.target.value })}
                placeholder="<EMAIL>, <EMAIL>"
              />
            )}
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-700">
            <Button
              variant="outline"
              onClick={() => setShowGenerateModal(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleGenerateReport}
              className="bg-green-600 hover:bg-green-700"
              disabled={generatingReport || !selectedReportType || !reportConfig.dateFrom || !reportConfig.dateTo}
            >
              {generatingReport ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Generating...
                </div>
              ) : (
                <>
                  <FiFileText className="mr-2" />
                  Generate Report
                </>
              )}
            </Button>
          </div>
        </div>
      </Modal>
    </AdminLayout>
  );
}
