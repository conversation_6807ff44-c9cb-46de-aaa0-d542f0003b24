import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';

export const validateRequest = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const errorMessages: Record<string, string[]> = {};
    
    errors.array().forEach(error => {
      const field = error.type === 'field' ? error.path : 'general';
      if (!errorMessages[field]) {
        errorMessages[field] = [];
      }
      errorMessages[field].push(error.msg);
    });

    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      message: 'Please check your input and try again',
      errors: errorMessages
    });
  }
  
  return next();
};
