// Automatic Withdrawal System Types
export interface WithdrawalAccount {
  id: string;
  userId: string;
  type: 'bank_account' | 'mobile_money' | 'crypto_wallet';
  isDefault: boolean;
  isVerified: boolean;
  bankDetails?: BankAccountDetails;
  mobileMoneyDetails?: MobileMoneyDetails;
  cryptoDetails?: CryptoWalletDetails;
  recipientCode?: string; // Paystack recipient code
  createdAt: string;
  updatedAt: string;
}

export interface BankAccountDetails {
  bankName: string;
  bankCode: string;
  accountNumber: string;
  accountName: string;
  accountType: 'savings' | 'current';
}

export interface MobileMoneyDetails {
  provider: 'mtn' | 'airtel' | 'glo' | '9mobile';
  phoneNumber: string;
  accountName: string;
}

export interface CryptoWalletDetails {
  currency: 'btc' | 'eth' | 'usdt';
  walletAddress: string;
  network?: string;
}

// Automatic Withdrawal Rules
export interface AutomaticWithdrawalRule {
  id: string;
  userId: string;
  name: string;
  description?: string;
  triggerType: TriggerType;
  triggerConditions: TriggerConditions;
  withdrawalConfig: WithdrawalConfig;
  executionSettings: ExecutionSettings;
  isActive: boolean;
  statistics: RuleStatistics;
  createdAt: string;
  updatedAt: string;
  lastExecuted?: string;
  nextExecution?: string;
}

export type TriggerType = 
  | 'balance_threshold' 
  | 'date_based' 
  | 'interest_earned' 
  | 'goal_reached'
  | 'percentage_growth';

export interface TriggerConditions {
  // Balance threshold trigger
  balanceThreshold?: number;
  minimumBalance?: number;
  
  // Date-based trigger
  dayOfMonth?: number; // 1-31
  dayOfWeek?: number; // 0-6 (Sunday = 0)
  time?: string; // HH:MM format
  frequency?: 'daily' | 'weekly' | 'monthly' | 'quarterly';
  
  // Interest earned trigger
  interestThreshold?: number;
  interestPeriod?: 'daily' | 'weekly' | 'monthly';
  
  // Goal reached trigger
  goalId?: string;
  goalPercentage?: number; // Trigger when goal is X% complete
  
  // Percentage growth trigger
  growthPercentage?: number;
  growthPeriod?: 'daily' | 'weekly' | 'monthly';
}

export interface WithdrawalConfig {
  withdrawalAccountId: string;
  amountType: 'fixed' | 'percentage' | 'excess' | 'all';
  amount?: number; // For fixed amount
  percentage?: number; // For percentage of balance
  keepMinimumBalance?: number; // Minimum balance to maintain
  maxAmount?: number; // Maximum amount per withdrawal
  currency: string;
}

export interface ExecutionSettings {
  priority: 'low' | 'medium' | 'high';
  retryAttempts: number;
  retryDelay: number; // Minutes between retries
  notifyUser: boolean;
  requireApproval: boolean;
  executionWindow?: {
    startTime: string; // HH:MM
    endTime: string; // HH:MM
  };
}

export interface RuleStatistics {
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  totalAmountWithdrawn: number;
  lastExecutionStatus: 'success' | 'failed' | 'pending' | 'never';
  lastExecutionAmount?: number;
  lastExecutionDate?: string;
  averageExecutionTime?: number; // Seconds
}

// Withdrawal Requests (Enhanced)
export interface AutomaticWithdrawalRequest {
  id: string;
  userId: string;
  ruleId?: string; // If triggered by automatic rule
  withdrawalAccountId: string;
  amount: number;
  currency: string;
  status: WithdrawalStatus;
  type: 'manual' | 'automatic';
  reference: string;
  paystackTransferCode?: string;
  paystackStatus?: 'pending' | 'success' | 'failed';
  fees: number;
  netAmount: number;
  reason?: string;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  processedAt?: string;
  completedAt?: string;
  failureReason?: string;
}

export type WithdrawalStatus = 
  | 'pending' 
  | 'processing' 
  | 'completed' 
  | 'failed' 
  | 'cancelled'
  | 'requires_approval';

// Processing Engine Types
export interface ProcessingEngine {
  isProcessing: boolean;
  queueLength: number;
  lastProcessingTime?: string;
  processedRulesCount: number;
  failedRulesCount: number;
}

export interface ProcessingResult {
  ruleId: string;
  success: boolean;
  withdrawalRequestId?: string;
  amount?: number;
  error?: string;
  executionTime: number; // Milliseconds
}

// API Request/Response Types
export interface CreateWithdrawalAccountRequest {
  type: 'bank_account' | 'mobile_money' | 'crypto_wallet';
  bankDetails?: BankAccountDetails;
  mobileMoneyDetails?: MobileMoneyDetails;
  cryptoDetails?: CryptoWalletDetails;
  isDefault?: boolean;
}

export interface CreateAutomaticRuleRequest {
  name: string;
  description?: string;
  triggerType: TriggerType;
  triggerConditions: TriggerConditions;
  withdrawalConfig: WithdrawalConfig;
  executionSettings: ExecutionSettings;
}

export interface InstantWithdrawalRequest {
  withdrawalAccountId: string;
  amount: number;
  reason?: string;
  sourceType?: 'savings' | 'interest' | 'balance';
}

export interface PaystackRecipient {
  recipient_code: string;
  type: string;
  name: string;
  description?: string;
  metadata?: Record<string, any>;
  domain: string;
  details: {
    authorization_code?: string;
    account_number?: string;
    account_name?: string;
    bank_code?: string;
    bank_name?: string;
  };
  currency: string;
  active: boolean;
  created_at: string;
  updated_at: string;
}

export interface PaystackTransfer {
  transfer_code: string;
  reference: string;
  amount: number;
  currency: string;
  source: string;
  reason: string;
  status: 'pending' | 'success' | 'failed' | 'reversed';
  failures?: any;
  transfer_date: string;
  recipient: PaystackRecipient;
}

// Utility Types
export interface WithdrawalLimits {
  daily: number;
  weekly: number;
  monthly: number;
  perTransaction: number;
  minimum: number;
}

export interface WithdrawalFees {
  percentage: number;
  fixed: number;
  cap?: number;
  minimum?: number;
}

export interface AutomaticWithdrawalSettings {
  enabled: boolean;
  maxRulesPerUser: number;
  defaultRetryAttempts: number;
  defaultRetryDelay: number;
  processingInterval: number; // Minutes
  limits: WithdrawalLimits;
  fees: WithdrawalFees;
}

// Dashboard Types
export interface WithdrawalDashboardData {
  totalBalance: number;
  availableForWithdrawal: number;
  pendingWithdrawals: number;
  activeRules: number;
  recentWithdrawals: AutomaticWithdrawalRequest[];
  activeWithdrawalRules: AutomaticWithdrawalRule[];
  withdrawalAccounts: WithdrawalAccount[];
  monthlyWithdrawalStats: {
    totalAmount: number;
    totalCount: number;
    successRate: number;
  };
}

export interface RuleExecutionLog {
  id: string;
  ruleId: string;
  executedAt: string;
  status: 'success' | 'failed' | 'skipped';
  amount?: number;
  withdrawalRequestId?: string;
  error?: string;
  executionTime: number;
  triggerReason: string;
}

// Error Types
export interface WithdrawalError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

export interface ValidationResult {
  isValid: boolean;
  errors: WithdrawalError[];
  warnings?: string[];
}

// Export all types
export type {
  WithdrawalAccount,
  BankAccountDetails,
  MobileMoneyDetails,
  CryptoWalletDetails,
  AutomaticWithdrawalRule,
  TriggerType,
  TriggerConditions,
  WithdrawalConfig,
  ExecutionSettings,
  RuleStatistics,
  AutomaticWithdrawalRequest,
  WithdrawalStatus,
  ProcessingEngine,
  ProcessingResult,
  CreateWithdrawalAccountRequest,
  CreateAutomaticRuleRequest,
  InstantWithdrawalRequest,
  PaystackRecipient,
  PaystackTransfer,
  WithdrawalLimits,
  WithdrawalFees,
  AutomaticWithdrawalSettings,
  WithdrawalDashboardData,
  RuleExecutionLog,
  WithdrawalError,
  ValidationResult
};
