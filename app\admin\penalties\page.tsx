"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FiSave,
  FiRefreshCw,
  FiAlertTriangle,
  FiPercent,
  FiDollarSign,
  FiEdit,
  FiPlus,
  FiTrash2,
  FiClock,
  FiX,
} from 'react-icons/fi';
import AdminLayout from '../../../src/components/admin/AdminLayout';
import { Card3D } from '../../../src/components/ui/Card3D';
import { PrimaryButton, OutlineButton } from '../../../src/components/ui/AnimatedButton';
import { showToast } from '../../../src/components/ui/Toast';

interface PenaltyFee {
  id: string;
  name: string;
  type: 'early_withdrawal' | 'late_payment' | 'account_maintenance' | 'transaction_fee' | 'penalty';
  calculationType: 'fixed' | 'percentage';
  amount: number; // Fixed amount or percentage
  minAmount?: number;
  maxAmount?: number;
  description: string;
  isActive: boolean;
  applicableAfter?: number; // Days after which penalty applies
  createdAt: string;
  updatedAt: string;
}

const mockPenaltiesFees: PenaltyFee[] = [
  {
    id: '1',
    name: 'Early Withdrawal Penalty',
    type: 'early_withdrawal',
    calculationType: 'percentage',
    amount: 5.0,
    minAmount: 1000,
    maxAmount: 50000,
    description: 'Penalty for withdrawing before maturity date',
    isActive: true,
    applicableAfter: 0,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '2',
    name: 'Late Payment Fee',
    type: 'late_payment',
    calculationType: 'fixed',
    amount: 2500,
    description: 'Fee for late monthly contributions',
    isActive: true,
    applicableAfter: 7,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '3',
    name: 'Account Maintenance Fee',
    type: 'account_maintenance',
    calculationType: 'fixed',
    amount: 500,
    description: 'Monthly account maintenance fee',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '4',
    name: 'Transaction Fee',
    type: 'transaction_fee',
    calculationType: 'percentage',
    amount: 1.5,
    minAmount: 100,
    maxAmount: 1000,
    description: 'Fee for external transfers',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }
];

export default function PenaltiesFeesPage() {
  const [penaltiesFees, setPenaltiesFees] = useState<PenaltyFee[]>(mockPenaltiesFees);
  const [editingItem, setEditingItem] = useState<PenaltyFee | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);

  const [formData, setFormData] = useState({
    name: '',
    type: 'penalty' as const,
    calculationType: 'fixed' as const,
    amount: 0,
    minAmount: 0,
    maxAmount: 0,
    description: '',
    isActive: true,
    applicableAfter: 0
  });

  const handleSaveItem = async () => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (editingItem) {
        // Update existing item
        setPenaltiesFees(prev =>
          prev.map(item =>
            item.id === editingItem.id
              ? { ...item, ...formData, updatedAt: new Date().toISOString() }
              : item
          )
        );
        showToast.success('Penalty/Fee updated successfully');
        setEditingItem(null);
      } else {
        // Add new item
        const newItem: PenaltyFee = {
          id: Date.now().toString(),
          ...formData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        setPenaltiesFees(prev => [...prev, newItem]);
        showToast.success('Penalty/Fee added successfully');
        setShowAddForm(false);
      }

      // Reset form
      setFormData({
        name: '',
        type: 'penalty',
        calculationType: 'fixed',
        amount: 0,
        minAmount: 0,
        maxAmount: 0,
        description: '',
        isActive: true,
        applicableAfter: 0
      });
    } catch (error) {
      showToast.error('Failed to save penalty/fee');
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditItem = (item: PenaltyFee) => {
    setEditingItem(item);
    setFormData({
      name: item.name,
      type: item.type,
      calculationType: item.calculationType,
      amount: item.amount,
      minAmount: item.minAmount || 0,
      maxAmount: item.maxAmount || 0,
      description: item.description,
      isActive: item.isActive,
      applicableAfter: item.applicableAfter || 0
    });
    setShowAddForm(true);
  };

  const handleDeleteItem = async (itemId: string) => {
    if (!confirm('Are you sure you want to delete this penalty/fee?')) return;

    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setPenaltiesFees(prev => prev.filter(item => item.id !== itemId));
      showToast.success('Penalty/Fee deleted successfully');
    } catch (error) {
      showToast.error('Failed to delete penalty/fee');
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleActive = async (itemId: string) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setPenaltiesFees(prev =>
        prev.map(item =>
          item.id === itemId
            ? { ...item, isActive: !item.isActive, updatedAt: new Date().toISOString() }
            : item
        )
      );
      showToast.success('Status updated successfully');
    } catch (error) {
      showToast.error('Failed to update status');
    } finally {
      setIsLoading(false);
    }
  };

  const formatAmount = (amount: number, type: string) => {
    if (type === 'percentage') {
      return `${amount}%`;
    }
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN'
    }).format(amount);
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'early_withdrawal':
        return <FiClock className="text-orange-400" />;
      case 'late_payment':
        return <FiAlertTriangle className="text-red-400" />;
      case 'account_maintenance':
        return <FiDollarSign className="text-blue-400" />;
      case 'transaction_fee':
        return <FiPercent className="text-green-400" />;
      default:
        return <FiX className="text-gray-400" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-NG', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Calculate summary stats
  const activeItems = penaltiesFees.filter(item => item.isActive).length;
  const totalPenalties = penaltiesFees.filter(item => 
    item.type === 'early_withdrawal' || item.type === 'late_payment' || item.type === 'penalty'
  ).length;
  const totalFees = penaltiesFees.filter(item => 
    item.type === 'account_maintenance' || item.type === 'transaction_fee'
  ).length;

  return (
    <AdminLayout title="Penalties & Fees">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white">Penalties & Fees Management</h1>
            <p className="text-gray-400 mt-2">Configure penalties and fees for various scenarios</p>
          </div>
          <div className="flex space-x-2">
            <OutlineButton onClick={() => setShowAddForm(!showAddForm)}>
              <FiPlus className="mr-2" />
              Add Penalty/Fee
            </OutlineButton>
            <PrimaryButton>
              <FiRefreshCw className="mr-2" />
              Refresh
            </PrimaryButton>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card3D className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Active Items</p>
                <p className="text-2xl font-bold text-white">{activeItems}</p>
              </div>
              <FiAlertTriangle className="text-green-500 text-3xl" />
            </div>
          </Card3D>

          <Card3D className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Penalties</p>
                <p className="text-2xl font-bold text-white">{totalPenalties}</p>
              </div>
              <FiX className="text-red-500 text-3xl" />
            </div>
          </Card3D>

          <Card3D className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Fees</p>
                <p className="text-2xl font-bold text-white">{totalFees}</p>
              </div>
              <FiDollarSign className="text-blue-500 text-3xl" />
            </div>
          </Card3D>
        </div>

        {/* Add/Edit Form */}
        {showAddForm && (
          <Card3D className="bg-gray-800 border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-white mb-4">
              {editingItem ? 'Edit Penalty/Fee' : 'Add New Penalty/Fee'}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Name
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-green-500 focus:outline-none text-white"
                  placeholder="e.g., Early Withdrawal Penalty"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Type
                </label>
                <select
                  value={formData.type}
                  onChange={(e) => setFormData({ ...formData, type: e.target.value as any })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-green-500 focus:outline-none text-white"
                >
                  <option value="early_withdrawal">Early Withdrawal</option>
                  <option value="late_payment">Late Payment</option>
                  <option value="account_maintenance">Account Maintenance</option>
                  <option value="transaction_fee">Transaction Fee</option>
                  <option value="penalty">General Penalty</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Calculation Type
                </label>
                <select
                  value={formData.calculationType}
                  onChange={(e) => setFormData({ ...formData, calculationType: e.target.value as any })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-green-500 focus:outline-none text-white"
                >
                  <option value="fixed">Fixed Amount</option>
                  <option value="percentage">Percentage</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Amount {formData.calculationType === 'percentage' ? '(%)' : '(₦)'}
                </label>
                <input
                  type="number"
                  step={formData.calculationType === 'percentage' ? '0.1' : '1'}
                  value={formData.amount}
                  onChange={(e) => setFormData({ ...formData, amount: parseFloat(e.target.value) })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-green-500 focus:outline-none text-white"
                />
              </div>

              {formData.calculationType === 'percentage' && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Minimum Amount (₦)
                    </label>
                    <input
                      type="number"
                      value={formData.minAmount}
                      onChange={(e) => setFormData({ ...formData, minAmount: parseInt(e.target.value) })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-green-500 focus:outline-none text-white"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Maximum Amount (₦)
                    </label>
                    <input
                      type="number"
                      value={formData.maxAmount}
                      onChange={(e) => setFormData({ ...formData, maxAmount: parseInt(e.target.value) })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-green-500 focus:outline-none text-white"
                    />
                  </div>
                </>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Applicable After (days)
                </label>
                <input
                  type="number"
                  value={formData.applicableAfter}
                  onChange={(e) => setFormData({ ...formData, applicableAfter: parseInt(e.target.value) })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-green-500 focus:outline-none text-white"
                  placeholder="0 for immediate"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={3}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-green-500 focus:outline-none text-white"
                  placeholder="Describe when this penalty/fee applies"
                />
              </div>
            </div>

            <div className="flex items-center mt-4">
              <input
                type="checkbox"
                id="isActive"
                checked={formData.isActive}
                onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                className="mr-2"
              />
              <label htmlFor="isActive" className="text-sm text-gray-300">
                Active
              </label>
            </div>

            <div className="flex space-x-2 mt-6">
              <PrimaryButton
                onClick={handleSaveItem}
                disabled={isLoading || !formData.name || !formData.description}
              >
                <FiSave className="mr-2" />
                {editingItem ? 'Update' : 'Save'} Item
              </PrimaryButton>
              <OutlineButton
                onClick={() => {
                  setShowAddForm(false);
                  setEditingItem(null);
                  setFormData({
                    name: '',
                    type: 'penalty',
                    calculationType: 'fixed',
                    amount: 0,
                    minAmount: 0,
                    maxAmount: 0,
                    description: '',
                    isActive: true,
                    applicableAfter: 0
                  });
                }}
              >
                Cancel
              </OutlineButton>
            </div>
          </Card3D>
        )}

        {/* Penalties & Fees Table */}
        <Card3D className="bg-gray-800 border-gray-700 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Name & Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Applicable After
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Updated
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-700">
                {penaltiesFees.map((item) => (
                  <motion.tr
                    key={item.id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="hover:bg-gray-700/50"
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {getTypeIcon(item.type)}
                        <div className="ml-3">
                          <div className="text-sm font-medium text-white">{item.name}</div>
                          <div className="text-sm text-gray-400 capitalize">
                            {item.type.replace('_', ' ')}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-white">
                        {formatAmount(item.amount, item.calculationType)}
                      </div>
                      {item.calculationType === 'percentage' && item.minAmount && item.maxAmount && (
                        <div className="text-sm text-gray-400">
                          Min: {formatAmount(item.minAmount, 'fixed')} - Max: {formatAmount(item.maxAmount, 'fixed')}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-white">
                        {item.applicableAfter === 0 ? 'Immediate' : `${item.applicableAfter} days`}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <button
                        onClick={() => handleToggleActive(item.id)}
                        disabled={isLoading}
                        className={`px-2 py-1 text-xs rounded-full ${
                          item.isActive
                            ? 'bg-green-600 text-white'
                            : 'bg-gray-600 text-gray-300'
                        }`}
                      >
                        {item.isActive ? 'Active' : 'Inactive'}
                      </button>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                      {formatDate(item.updatedAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleEditItem(item)}
                          className="text-blue-400 hover:text-blue-300"
                        >
                          <FiEdit />
                        </button>
                        <button
                          onClick={() => handleDeleteItem(item.id)}
                          disabled={isLoading}
                          className="text-red-400 hover:text-red-300 disabled:opacity-50"
                        >
                          <FiTrash2 />
                        </button>
                      </div>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        </Card3D>
      </div>
    </AdminLayout>
  );
}
