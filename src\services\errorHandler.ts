import { ApiResponse, ErrorResponse } from '../types';

export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode: number,
    public code?: string,
    public details?: any,
    public timestamp?: string
  ) {
    super(message);
    this.name = 'ApiError';
    this.timestamp = timestamp || new Date().toISOString();
  }

  static fromResponse(response: ErrorResponse): ApiError {
    return new ApiError(
      response.message || response.error,
      500, // Default status code if not provided
      response.code,
      response.details,
      response.timestamp
    );
  }

  toJSON() {
    return {
      name: this.name,
      message: this.message,
      statusCode: this.statusCode,
      code: this.code,
      details: this.details,
      timestamp: this.timestamp
    };
  }
}

export class ValidationError extends ApiError {
  constructor(message: string, public validationErrors: Record<string, string[]>) {
    super(message, 422, 'VALIDATION_ERROR', validationErrors);
    this.name = 'ValidationError';
  }
}

export class AuthenticationError extends ApiError {
  constructor(message: string = 'Authentication required') {
    super(message, 401, 'AUTHENTICATION_ERROR');
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends ApiError {
  constructor(message: string = 'Access denied') {
    super(message, 403, 'AUTHORIZATION_ERROR');
    this.name = 'AuthorizationError';
  }
}

export class NotFoundError extends ApiError {
  constructor(message: string = 'Resource not found') {
    super(message, 404, 'NOT_FOUND_ERROR');
    this.name = 'NotFoundError';
  }
}

export class NetworkError extends ApiError {
  constructor(message: string = 'Network error occurred') {
    super(message, 0, 'NETWORK_ERROR');
    this.name = 'NetworkError';
  }
}

export class TimeoutError extends ApiError {
  constructor(message: string = 'Request timeout') {
    super(message, 408, 'TIMEOUT_ERROR');
    this.name = 'TimeoutError';
  }
}

export class ServerError extends ApiError {
  constructor(message: string = 'Internal server error') {
    super(message, 500, 'SERVER_ERROR');
    this.name = 'ServerError';
  }
}

export class ServiceUnavailableError extends ApiError {
  constructor(message: string = 'Service temporarily unavailable') {
    super(message, 503, 'SERVICE_UNAVAILABLE');
    this.name = 'ServiceUnavailableError';
  }
}

// Error handler utility functions
export const handleApiError = async (response: Response): Promise<never> => {
  let errorData: any;
  
  try {
    errorData = await response.json();
  } catch {
    errorData = { 
      error: 'Unknown error occurred',
      message: `HTTP ${response.status}: ${response.statusText}` 
    };
  }

  // Create appropriate error based on status code
  switch (response.status) {
    case 400:
      if (errorData.errors) {
        throw new ValidationError(errorData.message || 'Validation failed', errorData.errors);
      }
      throw new ApiError(errorData.message || errorData.error || 'Bad request', 400, errorData.code, errorData.details);
    
    case 401:
      throw new AuthenticationError(errorData.message || errorData.error);
    
    case 403:
      throw new AuthorizationError(errorData.message || errorData.error);
    
    case 404:
      throw new NotFoundError(errorData.message || errorData.error);
    
    case 408:
      throw new TimeoutError(errorData.message || errorData.error);
    
    case 422:
      throw new ValidationError(
        errorData.message || 'Validation failed', 
        errorData.errors || errorData.details || {}
      );
    
    case 500:
      throw new ServerError(errorData.message || errorData.error);
    
    case 503:
      throw new ServiceUnavailableError(errorData.message || errorData.error);
    
    default:
      throw new ApiError(
        errorData.message || errorData.error || 'Request failed',
        response.status,
        errorData.code,
        errorData.details
      );
  }
};

export const handleNetworkError = (error: any): never => {
  if (error.name === 'AbortError') {
    throw new TimeoutError('Request was cancelled');
  }
  
  if (error.name === 'TypeError' && error.message.includes('fetch')) {
    throw new NetworkError('Network connection failed');
  }
  
  throw new NetworkError(error.message || 'Network error occurred');
};

// Response standardization utilities
export const createSuccessResponse = <T>(data: T, message?: string, meta?: any): ApiResponse<T> => {
  return {
    success: true,
    data,
    message,
    meta
  };
};

export const createErrorResponse = (error: string, details?: any): ApiResponse => {
  return {
    success: false,
    error,
    errors: details
  };
};

// Enhanced fetch wrapper with error handling
export const apiRequest = async <T = any>(
  url: string,
  options: RequestInit = {},
  timeout: number = 30000
): Promise<T> => {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      await handleApiError(response);
    }

    // Handle empty responses
    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      return {} as T;
    }

    const data = await response.json();
    
    // Handle standardized API responses
    if (data.success === false) {
      throw ApiError.fromResponse(data);
    }

    return data.data || data;
  } catch (error) {
    clearTimeout(timeoutId);
    
    if (error instanceof ApiError) {
      throw error;
    }
    
    handleNetworkError(error);
  }
};

// Retry mechanism for failed requests
export const retryApiRequest = async <T>(
  requestFn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000,
  backoffMultiplier: number = 2
): Promise<T> => {
  let lastError: Error;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await requestFn();
    } catch (error) {
      lastError = error as Error;

      // Don't retry on client errors (4xx) except 408 (timeout)
      if (error instanceof ApiError) {
        if (error.statusCode >= 400 && error.statusCode < 500 && error.statusCode !== 408) {
          throw error;
        }
      }

      // Don't retry on the last attempt
      if (attempt === maxRetries) {
        break;
      }

      // Calculate delay with exponential backoff
      const delay = baseDelay * Math.pow(backoffMultiplier, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError!;
};

// Error logging utility
export const logError = (error: Error, context?: string) => {
  const errorInfo = {
    name: error.name,
    message: error.message,
    stack: error.stack,
    context,
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    url: window.location.href
  };

  // Log to console in development
  if (process.env.NODE_ENV === 'development') {
    console.error('API Error:', errorInfo);
  }

  // Send to error tracking service in production
  if (process.env.NODE_ENV === 'production') {
    // TODO: Integrate with error tracking service (e.g., Sentry, LogRocket)
    // errorTrackingService.captureError(errorInfo);
  }
};

// User-friendly error messages
export const getUserFriendlyErrorMessage = (error: ApiError): string => {
  switch (error.code) {
    case 'AUTHENTICATION_ERROR':
      return 'Please log in to continue';
    
    case 'AUTHORIZATION_ERROR':
      return 'You do not have permission to perform this action';
    
    case 'VALIDATION_ERROR':
      return 'Please check your input and try again';
    
    case 'NOT_FOUND_ERROR':
      return 'The requested resource was not found';
    
    case 'NETWORK_ERROR':
      return 'Please check your internet connection and try again';
    
    case 'TIMEOUT_ERROR':
      return 'The request took too long. Please try again';
    
    case 'SERVER_ERROR':
      return 'Something went wrong on our end. Please try again later';
    
    case 'SERVICE_UNAVAILABLE':
      return 'The service is temporarily unavailable. Please try again later';
    
    default:
      return error.message || 'An unexpected error occurred';
  }
};

// Error boundary helper for React components
export const createErrorBoundaryHandler = (onError?: (error: Error, errorInfo: any) => void) => {
  return (error: Error, errorInfo: any) => {
    logError(error, 'React Error Boundary');
    
    if (onError) {
      onError(error, errorInfo);
    }
  };
};
