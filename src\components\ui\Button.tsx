"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { IconType } from 'react-icons';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  loading?: boolean;
  leftIcon?: React.ReactNode | IconType;
  rightIcon?: React.ReactNode | IconType;
  fullWidth?: boolean;
  href?: string;
  effect3D?: boolean;
}

export function Button({
  children,
  variant = 'primary',
  size = 'md',
  loading = false,
  leftIcon,
  rightIcon,
  fullWidth = false,
  href,
  className = '',
  disabled,
  effect3D = true,
  ...props
}: ButtonProps) {
  const baseClasses = `
    relative inline-flex items-center justify-center font-semibold font-inter
    transition-all duration-200 ease-in-out transform-gpu
    focus:outline-none focus:ring-2 focus:ring-brand focus:ring-offset-2
    disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none
    overflow-hidden group
    ${fullWidth ? 'w-full' : ''}
  `;

  const variantClasses = {
    primary: `
      bg-brand hover:bg-brand-dark text-white
      shadow-lg hover:shadow-xl hover:shadow-brand/25
      border border-brand hover:border-brand-dark
      ${effect3D ? 'hover:scale-105 active:scale-95' : ''}
    `,
    secondary: `
      bg-theme-secondary hover:bg-opacity-80 text-theme
      border border-theme hover:border-brand
      ${effect3D ? 'hover:scale-105 active:scale-95' : ''}
    `,
    outline: `
      bg-transparent border-2 border-brand text-brand
      hover:bg-brand hover:text-white
      ${effect3D ? 'hover:scale-105 active:scale-95' : ''}
    `,
    ghost: `
      bg-transparent text-brand hover:text-brand-dark
      hover:bg-brand hover:bg-opacity-10
      ${effect3D ? 'hover:scale-105 active:scale-95' : ''}
    `,
    danger: `
      bg-red-600 hover:bg-red-700 text-white
      shadow-lg hover:shadow-xl hover:shadow-red-500/25
      border border-red-600 hover:border-red-700
      ${effect3D ? 'hover:scale-105 active:scale-95' : ''}
    `
  };

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm rounded-md gap-1.5',
    md: 'px-4 py-2 text-sm rounded-lg gap-2',
    lg: 'px-6 py-3 text-base rounded-lg gap-2',
    xl: 'px-8 py-4 text-lg rounded-xl gap-3'
  };

  const buttonClasses = `
    ${baseClasses}
    ${variantClasses[variant]}
    ${sizeClasses[size]}
    ${className}
  `;

  const renderIcon = (icon: React.ReactNode | IconType, className: string) => {
    if (React.isValidElement(icon)) {
      return <span className={className}>{icon}</span>;
    }
    if (typeof icon === 'function') {
      const IconComponent = icon as IconType;
      return <IconComponent className={`${className} text-brand`} />;
    }
    return null;
  };

  const ButtonContent = () => (
    <>
      {/* 3D Effect Background */}
      {effect3D && (
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-inherit opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
      )}

      {/* Shine Effect */}
      {effect3D && (
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700" />
      )}

      <span className="relative z-10 flex items-center justify-center gap-inherit">
        {loading && (
          <motion.div
            className="w-4 h-4 border-2 border-current border-t-transparent rounded-full"
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          />
        )}
        {leftIcon && !loading && renderIcon(leftIcon, "")}
        {children}
        {rightIcon && renderIcon(rightIcon, "")}
      </span>
    </>
  );

  if (href) {
    return (
      <motion.a
        href={href}
        className={buttonClasses}
        whileHover={{
          scale: disabled || loading ? 1 : (effect3D ? 1.05 : 1.02),
          boxShadow: disabled || loading ? undefined : "0 10px 25px rgba(34, 197, 94, 0.3)"
        }}
        whileTap={{ scale: disabled || loading ? 1 : 0.95 }}
        transition={{ type: "spring", stiffness: 400, damping: 17 }}
      >
        <ButtonContent />
      </motion.a>
    );
  }

  return (
    <motion.button
      className={buttonClasses}
      disabled={disabled || loading}
      whileHover={{
        scale: disabled || loading ? 1 : (effect3D ? 1.05 : 1.02),
        boxShadow: disabled || loading ? undefined : "0 10px 25px rgba(34, 197, 94, 0.3)"
      }}
      whileTap={{ scale: disabled || loading ? 1 : 0.95 }}
      transition={{ type: "spring", stiffness: 400, damping: 17 }}
      {...props}
    >
      <ButtonContent />
    </motion.button>
  );
}

// Icon Button Component for consistent icon usage
interface IconButtonProps {
  icon: IconType;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  onClick?: () => void;
  className?: string;
  tooltip?: string;
  effect3D?: boolean;
}

export function IconButton({
  icon: Icon,
  variant = 'ghost',
  size = 'md',
  disabled = false,
  onClick,
  className = '',
  tooltip,
  effect3D = true,
}: IconButtonProps) {

  const sizeClasses = {
    sm: 'p-1.5 rounded-md',
    md: 'p-2 rounded-lg',
    lg: 'p-3 rounded-xl',
  };

  const iconSizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
  };

  const baseClasses = `
    relative inline-flex items-center justify-center
    transition-all duration-200 ease-in-out transform-gpu
    focus:outline-none focus:ring-2 focus:ring-brand focus:ring-offset-2
    disabled:opacity-50 disabled:cursor-not-allowed
    group overflow-hidden
  `;

  const variantClasses = {
    primary: `
      bg-brand hover:bg-brand-dark text-white
      ${effect3D ? 'hover:scale-110 active:scale-95' : ''}
    `,
    secondary: `
      bg-theme-secondary hover:bg-opacity-80 text-theme
      border border-theme hover:border-brand
      ${effect3D ? 'hover:scale-110 active:scale-95' : ''}
    `,
    outline: `
      bg-transparent hover:bg-brand hover:text-white text-brand
      border border-brand hover:border-brand-dark
      ${effect3D ? 'hover:scale-110 active:scale-95' : ''}
    `,
    ghost: `
      bg-transparent hover:bg-brand hover:bg-opacity-10 text-brand
      hover:text-brand-dark
      ${effect3D ? 'hover:scale-110 active:scale-95' : ''}
    `,
  };

  const combinedClasses = `
    ${baseClasses}
    ${sizeClasses[size]}
    ${variantClasses[variant]}
    ${className}
  `;

  return (
    <motion.button
      onClick={onClick}
      disabled={disabled}
      className={combinedClasses}
      whileHover={{
        scale: disabled ? 1 : (effect3D ? 1.1 : 1.05),
        rotate: disabled ? 0 : (effect3D ? 5 : 0)
      }}
      whileTap={{ scale: disabled ? 1 : 0.95 }}
      transition={{ type: "spring", stiffness: 400, damping: 17 }}
      title={tooltip}
    >
      {/* 3D Effect Background */}
      {effect3D && (
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-inherit opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
      )}

      <Icon className={`${iconSizes[size]} text-brand relative z-10`} />
    </motion.button>
  );
}

export default Button;
