"use client";

import { useRef, useMemo } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { Points, PointMaterial } from '@react-three/drei';
import * as THREE from 'three';

function AnimatedPoints() {
  const ref = useRef<THREE.Points>(null);
  const { mouse, viewport } = useThree();

  // Generate random points in 3D space
  const [positions, colors] = useMemo(() => {
    const positions = new Float32Array(2000 * 3);
    const colors = new Float32Array(2000 * 3);

    for (let i = 0; i < 2000; i++) {
      // Position
      positions[i * 3] = (Math.random() - 0.5) * 10;
      positions[i * 3 + 1] = (Math.random() - 0.5) * 10;
      positions[i * 3 + 2] = (Math.random() - 0.5) * 10;

      // Green color variations
      colors[i * 3] = 0.1 + Math.random() * 0.3; // R
      colors[i * 3 + 1] = 0.6 + Math.random() * 0.4; // G
      colors[i * 3 + 2] = 0.2 + Math.random() * 0.3; // B
    }

    return [positions, colors];
  }, []);

  useFrame((state) => {
    if (ref.current) {
      const time = state.clock.getElapsedTime();

      // Rotate the entire point cloud
      ref.current.rotation.x = time * 0.1;
      ref.current.rotation.y = time * 0.05;

      // Move towards mouse position
      ref.current.position.x = THREE.MathUtils.lerp(
        ref.current.position.x,
        mouse.x * viewport.width * 0.1,
        0.02
      );
      ref.current.position.y = THREE.MathUtils.lerp(
        ref.current.position.y,
        mouse.y * viewport.height * 0.1,
        0.02
      );

      // Animate individual points
      const positions = ref.current.geometry.attributes.position.array as Float32Array;
      for (let i = 0; i < positions.length; i += 3) {
        positions[i + 1] += Math.sin(time + positions[i]) * 0.001;
      }
      ref.current.geometry.attributes.position.needsUpdate = true;
    }
  });

  return (
    <Points ref={ref} positions={positions} colors={colors}>
      <PointMaterial
        transparent
        vertexColors
        size={0.02}
        sizeAttenuation={true}
        depthWrite={false}
        blending={THREE.AdditiveBlending}
      />
    </Points>
  );
}

function FloatingOrbs() {
  const groupRef = useRef<THREE.Group>(null);
  const { mouse } = useThree();

  const orbs = useMemo(() => {
    return Array.from({ length: 8 }, (_, i) => ({
      position: [
        (Math.random() - 0.5) * 8,
        (Math.random() - 0.5) * 8,
        (Math.random() - 0.5) * 8,
      ] as [number, number, number],
      scale: Math.random() * 0.5 + 0.2,
      speed: Math.random() * 0.02 + 0.01,
    }));
  }, []);

  useFrame((state) => {
    if (groupRef.current) {
      const time = state.clock.getElapsedTime();

      // Follow mouse with some lag
      groupRef.current.rotation.x = mouse.y * 0.2;
      groupRef.current.rotation.y = mouse.x * 0.2;

      groupRef.current.children.forEach((child, i) => {
        const orb = orbs[i];
        child.position.y += Math.sin(time * orb.speed + i) * 0.01;
        child.position.x += Math.cos(time * orb.speed + i) * 0.005;
      });
    }
  });

  return (
    <group ref={groupRef}>
      {orbs.map((orb, i) => (
        <mesh key={i} position={orb.position} scale={orb.scale}>
          <sphereGeometry args={[1, 16, 16]} />
          <meshBasicMaterial
            color="#22c55e"
            transparent
            opacity={0.1}
            blending={THREE.AdditiveBlending}
          />
        </mesh>
      ))}
    </group>
  );
}

function WaveField() {
  const meshRef = useRef<THREE.Mesh>(null);
  const { mouse } = useThree();

  useFrame((state) => {
    if (meshRef.current) {
      const time = state.clock.getElapsedTime();
      const geometry = meshRef.current.geometry as THREE.PlaneGeometry;
      const positions = geometry.attributes.position.array as Float32Array;

      for (let i = 0; i < positions.length; i += 3) {
        const x = positions[i];
        const y = positions[i + 1];

        positions[i + 2] = Math.sin(x * 0.5 + time) * 0.1 +
                          Math.cos(y * 0.5 + time * 0.8) * 0.1 +
                          Math.sin((x + y) * 0.3 + time * 1.2) * 0.05;
      }

      geometry.attributes.position.needsUpdate = true;
      geometry.computeVertexNormals();

      // Subtle mouse interaction
      meshRef.current.rotation.x = mouse.y * 0.1 - Math.PI / 2;
      meshRef.current.rotation.z = mouse.x * 0.1;
    }
  });

  return (
    <mesh ref={meshRef} position={[0, 0, -5]} rotation={[-Math.PI / 2, 0, 0]}>
      <planeGeometry args={[20, 20, 50, 50]} />
      <meshBasicMaterial
        color="#16a34a"
        transparent
        opacity={0.05}
        wireframe
        blending={THREE.AdditiveBlending}
      />
    </mesh>
  );
}

export default function Background3D() {
  return (
    <div className="fixed inset-0 z-0 pointer-events-none">
      <Canvas
        camera={{ position: [0, 0, 5], fov: 75 }}
        style={{ background: 'transparent' }}
      >
        <AnimatedPoints />
        <FloatingOrbs />
        <WaveField />
      </Canvas>
    </div>
  );
}
