"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  FiCreditCard, 
  FiSmartphone, 
  FiDollarSign, 
  FiCheck,
  FiLoader,
  FiAlertCircle
} from 'react-icons/fi';
import { Button } from '../ui/Button';
import { Card3D } from '../ui/Card3D';
import { 
  WithdrawalAccount, 
  CreateWithdrawalAccountRequest,
  BankAccountDetails 
} from '../../types/automaticWithdrawal';
import { automaticWithdrawalService } from '../../services/automaticWithdrawalService';
import { paystackService } from '../../services/paystackService';

interface AddWithdrawalAccountFormProps {
  onAccountCreated: (account: WithdrawalAccount) => void;
  onCancel: () => void;
  editingAccount?: WithdrawalAccount;
}

interface BankOption {
  id: number;
  name: string;
  code: string;
}

export default function AddWithdrawalAccountForm({
  onAccountCreated,
  onCancel,
  editingAccount
}: AddWithdrawalAccountFormProps) {
  const [selectedType, setSelectedType] = useState<'bank_account' | 'mobile_money' | 'crypto_wallet'>('bank_account');
  const [loading, setLoading] = useState(false);
  const [verifying, setVerifying] = useState(false);
  const [banks, setBanks] = useState<BankOption[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Bank Account Form State
  const [bankForm, setBankForm] = useState({
    bankCode: '',
    accountNumber: '',
    accountName: '',
    accountType: 'savings' as 'savings' | 'current'
  });

  // Mobile Money Form State
  const [mobileForm, setMobileForm] = useState({
    provider: 'mtn' as 'mtn' | 'airtel' | 'glo' | '9mobile',
    phoneNumber: '',
    accountName: ''
  });

  // Crypto Wallet Form State
  const [cryptoForm, setCryptoForm] = useState({
    currency: 'btc' as 'btc' | 'eth' | 'usdt',
    walletAddress: '',
    network: ''
  });

  useEffect(() => {
    loadBanks();
    if (editingAccount) {
      populateEditForm();
    }
  }, [editingAccount]);

  const loadBanks = async () => {
    try {
      const response = await paystackService.getBankList();
      setBanks(response.data.map(bank => ({
        id: bank.id,
        name: bank.name,
        code: bank.code
      })));
    } catch (error) {
      console.error('Failed to load banks:', error);
    }
  };

  const populateEditForm = () => {
    if (!editingAccount) return;

    setSelectedType(editingAccount.type);
    
    if (editingAccount.type === 'bank_account' && editingAccount.bankDetails) {
      setBankForm({
        bankCode: editingAccount.bankDetails.bankCode,
        accountNumber: editingAccount.bankDetails.accountNumber,
        accountName: editingAccount.bankDetails.accountName,
        accountType: editingAccount.bankDetails.accountType
      });
    }
  };

  const verifyBankAccount = async () => {
    if (!bankForm.accountNumber || !bankForm.bankCode) {
      setError('Please enter account number and select a bank');
      return;
    }

    try {
      setVerifying(true);
      setError(null);
      
      const verification = await paystackService.verifyAccountNumber(
        bankForm.accountNumber,
        bankForm.bankCode
      );
      
      setBankForm(prev => ({
        ...prev,
        accountName: verification.data.account_name
      }));
      
      setSuccess('Account verified successfully!');
    } catch (error) {
      setError('Failed to verify account. Please check your details.');
    } finally {
      setVerifying(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      let accountData: CreateWithdrawalAccountRequest;

      switch (selectedType) {
        case 'bank_account':
          if (!bankForm.accountName) {
            setError('Please verify your bank account first');
            return;
          }
          
          const selectedBank = banks.find(bank => bank.code === bankForm.bankCode);
          accountData = {
            type: 'bank_account',
            bankDetails: {
              bankName: selectedBank?.name || '',
              bankCode: bankForm.bankCode,
              accountNumber: bankForm.accountNumber,
              accountName: bankForm.accountName,
              accountType: bankForm.accountType
            }
          };
          break;

        case 'mobile_money':
          accountData = {
            type: 'mobile_money',
            mobileMoneyDetails: {
              provider: mobileForm.provider,
              phoneNumber: mobileForm.phoneNumber,
              accountName: mobileForm.accountName
            }
          };
          break;

        case 'crypto_wallet':
          accountData = {
            type: 'crypto_wallet',
            cryptoDetails: {
              currency: cryptoForm.currency,
              walletAddress: cryptoForm.walletAddress,
              network: cryptoForm.network
            }
          };
          break;

        default:
          throw new Error('Invalid account type');
      }

      let result: WithdrawalAccount;
      
      if (editingAccount) {
        result = await automaticWithdrawalService.updateWithdrawalAccount(
          editingAccount.id,
          accountData
        );
      } else {
        result = await automaticWithdrawalService.createWithdrawalAccount(accountData);
      }

      onAccountCreated(result);
    } catch (error: any) {
      setError(error.message || 'Failed to save account');
    } finally {
      setLoading(false);
    }
  };

  const accountTypes = [
    {
      type: 'bank_account' as const,
      icon: FiCreditCard,
      title: 'Bank Account',
      description: 'Nigerian bank account for instant transfers'
    },
    {
      type: 'mobile_money' as const,
      icon: FiSmartphone,
      title: 'Mobile Money',
      description: 'MTN, Airtel, Glo, or 9mobile wallet'
    },
    {
      type: 'crypto_wallet' as const,
      icon: FiDollarSign,
      title: 'Crypto Wallet',
      description: 'Bitcoin, Ethereum, or USDT wallet'
    }
  ];

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Account Type Selection */}
      <div>
        <label className="block text-sm font-medium text-theme mb-3 font-inter">
          Account Type
        </label>
        <div className="grid grid-cols-1 gap-3">
          {accountTypes.map((type) => {
            const IconComponent = type.icon;
            const isSelected = selectedType === type.type;
            
            return (
              <Card3D
                key={type.type}
                className={`p-4 cursor-pointer transition-all duration-300 ${
                  isSelected 
                    ? 'ring-2 ring-brand bg-brand/5' 
                    : 'hover:bg-theme-secondary/50'
                }`}
                onClick={() => setSelectedType(type.type)}
                elevation={isSelected ? 2 : 1}
              >
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${
                    isSelected ? 'bg-brand text-white' : 'bg-theme-secondary text-theme-secondary'
                  }`}>
                    <IconComponent className="w-5 h-5" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-theme font-inter">{type.title}</h4>
                    <p className="text-sm text-theme-secondary font-inter">{type.description}</p>
                  </div>
                  {isSelected && (
                    <FiCheck className="w-5 h-5 text-brand ml-auto" />
                  )}
                </div>
              </Card3D>
            );
          })}
        </div>
      </div>

      {/* Bank Account Form */}
      {selectedType === 'bank_account' && (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-theme mb-2 font-inter">
              Bank
            </label>
            <select
              value={bankForm.bankCode}
              onChange={(e) => setBankForm(prev => ({ ...prev, bankCode: e.target.value }))}
              className="w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter"
              required
            >
              <option value="">Select your bank</option>
              {banks.map((bank) => (
                <option key={bank.code} value={bank.code}>
                  {bank.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-theme mb-2 font-inter">
              Account Number
            </label>
            <div className="flex space-x-2">
              <input
                type="text"
                value={bankForm.accountNumber}
                onChange={(e) => setBankForm(prev => ({ ...prev, accountNumber: e.target.value }))}
                className="flex-1 px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter"
                placeholder="Enter 10-digit account number"
                maxLength={10}
                required
              />
              <Button
                type="button"
                onClick={verifyBankAccount}
                loading={verifying}
                disabled={!bankForm.accountNumber || !bankForm.bankCode}
                className="font-inter"
              >
                Verify
              </Button>
            </div>
          </div>

          {bankForm.accountName && (
            <div>
              <label className="block text-sm font-medium text-theme mb-2 font-inter">
                Account Name
              </label>
              <div className="px-3 py-3 border border-green-500 rounded-lg bg-green-500/10 text-green-400 font-inter">
                {bankForm.accountName}
              </div>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-theme mb-2 font-inter">
              Account Type
            </label>
            <select
              value={bankForm.accountType}
              onChange={(e) => setBankForm(prev => ({ ...prev, accountType: e.target.value as 'savings' | 'current' }))}
              className="w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter"
              required
            >
              <option value="savings">Savings Account</option>
              <option value="current">Current Account</option>
            </select>
          </div>
        </div>
      )}

      {/* Mobile Money Form */}
      {selectedType === 'mobile_money' && (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-theme mb-2 font-inter">
              Provider
            </label>
            <select
              value={mobileForm.provider}
              onChange={(e) => setMobileForm(prev => ({ ...prev, provider: e.target.value as any }))}
              className="w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter"
              required
            >
              <option value="mtn">MTN</option>
              <option value="airtel">Airtel</option>
              <option value="glo">Glo</option>
              <option value="9mobile">9mobile</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-theme mb-2 font-inter">
              Phone Number
            </label>
            <input
              type="tel"
              value={mobileForm.phoneNumber}
              onChange={(e) => setMobileForm(prev => ({ ...prev, phoneNumber: e.target.value }))}
              className="w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter"
              placeholder="+234 ************"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-theme mb-2 font-inter">
              Account Name
            </label>
            <input
              type="text"
              value={mobileForm.accountName}
              onChange={(e) => setMobileForm(prev => ({ ...prev, accountName: e.target.value }))}
              className="w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter"
              placeholder="Enter account holder name"
              required
            />
          </div>
        </div>
      )}

      {/* Crypto Wallet Form */}
      {selectedType === 'crypto_wallet' && (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-theme mb-2 font-inter">
              Currency
            </label>
            <select
              value={cryptoForm.currency}
              onChange={(e) => setCryptoForm(prev => ({ ...prev, currency: e.target.value as any }))}
              className="w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter"
              required
            >
              <option value="btc">Bitcoin (BTC)</option>
              <option value="eth">Ethereum (ETH)</option>
              <option value="usdt">Tether (USDT)</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-theme mb-2 font-inter">
              Wallet Address
            </label>
            <input
              type="text"
              value={cryptoForm.walletAddress}
              onChange={(e) => setCryptoForm(prev => ({ ...prev, walletAddress: e.target.value }))}
              className="w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter"
              placeholder="Enter wallet address"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-theme mb-2 font-inter">
              Network (Optional)
            </label>
            <input
              type="text"
              value={cryptoForm.network}
              onChange={(e) => setCryptoForm(prev => ({ ...prev, network: e.target.value }))}
              className="w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter"
              placeholder="e.g., ERC-20, TRC-20"
            />
          </div>
        </div>
      )}

      {/* Error/Success Messages */}
      {error && (
        <div className="flex items-center space-x-2 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
          <FiAlertCircle className="w-5 h-5 text-red-400" />
          <span className="text-red-400 text-sm font-inter">{error}</span>
        </div>
      )}

      {success && (
        <div className="flex items-center space-x-2 p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
          <FiCheck className="w-5 h-5 text-green-400" />
          <span className="text-green-400 text-sm font-inter">{success}</span>
        </div>
      )}

      {/* Form Actions */}
      <div className="flex space-x-3">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          className="flex-1 font-inter"
        >
          Cancel
        </Button>
        <Button
          type="submit"
          loading={loading}
          className="flex-1 font-inter"
        >
          {editingAccount ? 'Update Account' : 'Add Account'}
        </Button>
      </div>
    </form>
  );
}
