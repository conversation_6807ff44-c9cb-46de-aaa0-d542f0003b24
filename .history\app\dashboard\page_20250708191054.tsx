"use client";

// Removed auth imports for demo mode
import DashboardLayout from "../../src/components/dashboard/DashboardLayout";
import { StatCard, ActionCard, ProgressCard } from "../../src/components/dashboard/DashboardCard";
import { SavingsLineChart } from "../../src/components/charts/SavingsChart";
import { LoadingLogo } from "../../components/LoadingLogo";
import { CardImage3D, TestimonialImage3D } from "../../src/components/ui/Image3D";
import Image from "next/image";
import {
  FiDollarSign,
  FiTrendingUp,
  FiUsers,
  FiTarget,
  FiPlus,
  FiCreditCard,
  FiPieChart,
  FiUserCheck
} from "react-icons/fi";





import React, { useEffect, useState } from "react";
import { userService, savingsService, groupSavingsService, transactionsService, depositsService } from "../../src/services";
import { UserProfile } from "../../src/types/user";
import { SavingsPlan } from "../../src/types/savings";
import { GroupSavings } from "../../src/types/groupSavings";
import { Transaction } from "../../src/types/transactions";


export default function UserDashboard() {
  const [user, setUser] = useState<UserProfile | null>(null);
  const [balance, setBalance] = useState<any>(null);
  const [savingsPlans, setSavingsPlans] = useState<SavingsPlan[]>([]);
  const [groupPlans, setGroupPlans] = useState<GroupSavings[]>([]);
  const [deposits, setDeposits] = useState<any[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      try {
        const [userRes, balanceRes, savingsRes, groupRes, depositRes, txRes] = await Promise.all([
          userService.getCurrentUserProfile(),
          userService.getUserBalance(),
          savingsService.getUserSavingsPlans(),
          groupSavingsService.getUserGroups(),
          depositsService.getUserDeposits(),
          transactionsService.getUserTransactions()
        ]);
        setUser(userRes);
        setBalance(balanceRes);
        setSavingsPlans(savingsRes);
        setGroupPlans(groupRes);
        // Fix: depositRes and txRes may not have .data property, use fallback
        // PaginatedDepositResponse: { deposits: Deposit[], ... }
        setDeposits(Array.isArray(depositRes?.deposits) ? depositRes.deposits : []);
        // PaginatedTransactionResponse: { transactions: Transaction[], ... }
        setTransactions(Array.isArray(txRes?.transactions) ? txRes.transactions : []);
      } catch (err) {
        // Optionally show error toast
        setUser(null);
      }
      setLoading(false);
    }
    fetchData();
  }, []);

  if (loading) {
    return <div className="flex justify-center items-center min-h-[60vh]"><LoadingLogo /></div>;
  }

  return (
    <DashboardLayout title="Dashboard">
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-green-500/20 to-blue-500/20 border border-green-500/30 rounded-lg p-6 relative overflow-hidden">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <h1 className="text-2xl font-display font-bold text-white mb-2 text-shadow">
                Welcome back, {user?.firstName}! 👋
              </h1>
              <p className="text-gray-300 mb-4">
                Here's an overview of your savings journey and financial progress.
              </p>
              <div className="flex items-center space-x-4 text-sm text-green-400">
                <span>💰 Better Interest Rates</span>
                <span>📈 Growing Savings</span>
                <span>🎯 Goals Achieved</span>
              </div>
            </div>
            <div className="hidden md:flex relative ml-6 justify-center">
              <CardImage3D
                src="/Celebrating with her iPhone 14 Pro.png"
                alt="Happy user celebrating savings success"
                width={128}
                height={128}
                intensity="light"
                className="border-2 border-green-400/50 mx-auto"
              />
            </div>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="Total Savings"
            value={balance ? `₦${balance.totalSavings?.toLocaleString()}` : "₦0"}
            subtitle="Across all plans"
            icon={FiDollarSign}
            color="green"
            trend={{ value: 0, isPositive: true }}
          />
          <StatCard
            title="Monthly Growth"
            value={balance ? `₦${balance.totalEarnings?.toLocaleString()}` : "₦0"}
            subtitle="This month"
            icon={FiTrendingUp}
            color="blue"
            trend={{ value: 0, isPositive: true }}
          />
          <StatCard
            title="Active Plans"
            value={savingsPlans.length + groupPlans.length}
            subtitle="Individual & Group"
            icon={FiPieChart}
            color="purple"
          />
          <StatCard
            title="Group Savings"
            value={groupPlans.length}
            subtitle="Active groups"
            icon={FiUsers}
            color="yellow"
          />
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <ActionCard
            title="Create Savings Plan"
            subtitle="Start a new individual savings plan"
            icon={FiPlus}
            color="green"
            onClick={() => window.location.href = '/dashboard/savings-plans'}
          />
          <ActionCard
            title="Join Group Savings"
            subtitle="Find and join group savings plans"
            icon={FiUsers}
            color="blue"
            onClick={() => window.location.href = '/dashboard/group-savings'}
          />
          <ActionCard
            title="Make Payment"
            subtitle="Add funds to your savings"
            icon={FiCreditCard}
            color="purple"
            onClick={() => window.location.href = '/dashboard/payments'}
          />
        </div>

        {/* Charts and Progress */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Savings Progress Chart */}
          <SavingsLineChart
            data={savingsPlans.map(plan => ({ name: plan.name, value: plan.currentAmount }))}
            title="Savings Progress"
            height={300}
          />

          {/* Goals Progress (show each plan as a progress card) */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">Goals Progress</h3>
            {savingsPlans.map(plan => (
              <ProgressCard
                key={plan.id}
                title={plan.name}
                current={plan.currentAmount}
                target={plan.targetAmount}
                unit="₦"
                color="green"
              />
            ))}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-gray-900/50 border border-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Recent Activity</h3>
            {/* You can add a View All button here if needed */}
          </div>
          <div className="space-y-3">
            {transactions.slice(0, 5).map((tx, index) => (
              <div key={index} className="flex items-center justify-between py-3 border-b border-gray-800 last:border-b-0">
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    tx.type === 'DEPOSIT' ? 'bg-green-500/20' :
                    tx.type === 'INTEREST' ? 'bg-purple-500/20' :
                    tx.type === 'WITHDRAWAL' ? 'bg-blue-500/20' :
                    'bg-yellow-500/20'
                  }`}>
                    {tx.type === 'DEPOSIT' && <FiDollarSign className="w-4 h-4 text-green-400" />}
                    {tx.type === 'INTEREST' && <FiTrendingUp className="w-4 h-4 text-purple-400" />}
                    {tx.type === 'WITHDRAWAL' && <FiCreditCard className="w-4 h-4 text-blue-400" />}
                    {tx.type === 'PENALTY' && <FiTarget className="w-4 h-4 text-yellow-400" />}
                  </div>
                  <div>
                    <p className="text-white text-sm font-medium">{tx.type}</p>
                    <p className="text-gray-400 text-xs">{tx.description || tx.planId || tx.goalId}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-green-400 text-sm font-medium">₦{tx.amount?.toLocaleString()}</p>
                  <p className="text-gray-500 text-xs">{new Date(tx.createdAt).toLocaleString()}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* KYC Status */}
        {user?.kycStatus !== 'APPROVED' && (
          <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-6">
            <div className="flex items-center space-x-3">
              <FiUserCheck className="w-6 h-6 text-yellow-400" />
              <div className="flex-1">
                <h3 className="text-white font-semibold">Complete Your KYC Verification</h3>
                <p className="text-gray-300 text-sm">
                  Verify your identity to unlock higher savings limits and additional features.
                </p>
              </div>
              <button
                onClick={() => window.location.href = '/dashboard/kyc'}
                className="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg transition-colors"
              >
                Verify Now
              </button>
            </div>
          </div>
        )}

      </div>
    </DashboardLayout>
  );
}
