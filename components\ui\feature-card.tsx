"use client";

import { ReactNode } from "react";

interface FeatureCardProps {
  icon: ReactNode;
  title: string;
  description: string;
  delay?: number;
}

export function FeatureCard({
  icon,
  title,
  description,
  delay = 0,
}: FeatureCardProps) {
  return (
    <div
      className="group bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-green-500/20 rounded-2xl p-8 hover:border-green-500/40 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl hover:shadow-green-500/10 hover-lift"
      style={{ animationDelay: `${delay}ms` }}
      data-oid="ys4yxgp"
    >
      <div
        className="text-green-400 mb-4 group-hover:scale-110 transition-transform duration-300 animate-float"
        data-oid="mpe_364"
      >
        {icon}
      </div>
      <h3
        className="text-xl font-semibold mb-3 text-white group-hover:text-green-400 transition-colors"
        data-oid="yck_ptc"
      >
        {title}
      </h3>
      <p className="text-gray-300 leading-relaxed" data-oid="97_lesb">
        {description}
      </p>
    </div>
  );
}
