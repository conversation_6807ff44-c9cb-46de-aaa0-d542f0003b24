"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  FiActivity, 
  FiSearch, 
  FiFilter,
  FiDownload,
  FiRefreshCw,
  FiTrendingUp,
  FiTrendingDown,
  FiDollarSign,
  FiCalendar,
  FiEye,
  FiArrowUpRight,
  FiArrowDownLeft,
  FiRepeat
} from 'react-icons/fi';
import DashboardLayout from '../../../src/components/dashboard/DashboardLayout';
import { transactionsService } from '../../../src/services';
import { Transaction, TransactionSearchFilters } from '../../../src/types';
import { Button } from '../../../src/components/ui/Button';
import { Card } from '../../../src/components/ui/Card';
import { Badge } from '../../../src/components/ui/Badge';
import { Input } from '../../../src/components/ui/Input';
import { Select } from '../../../src/components/ui/Select';
import { Modal } from '../../../src/components/ui/Modal';
import Table from '../../../src/components/ui/Table';
import { Pagination } from '../../../src/components/ui/Pagination';
import { toast } from '../../../src/components/ui/Toast';

const transactionTypeIcons = {
  DEPOSIT: { icon: FiArrowDownLeft, color: 'text-green-500', bg: 'bg-green-500/20' },
  WITHDRAWAL: { icon: FiArrowUpRight, color: 'text-red-500', bg: 'bg-red-500/20' },
  TRANSFER: { icon: FiRepeat, color: 'text-blue-500', bg: 'bg-blue-500/20' },
  CONTRIBUTION: { icon: FiTrendingUp, color: 'text-purple-500', bg: 'bg-purple-500/20' },
  INTEREST: { icon: FiDollarSign, color: 'text-yellow-500', bg: 'bg-yellow-500/20' },
  FEE: { icon: FiTrendingDown, color: 'text-orange-500', bg: 'bg-orange-500/20' },
  REFUND: { icon: FiArrowDownLeft, color: 'text-green-500', bg: 'bg-green-500/20' },
  PENALTY: { icon: FiTrendingDown, color: 'text-red-500', bg: 'bg-red-500/20' }
};

export default function TransactionsPage() {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  
  const [filters, setFilters] = useState<TransactionSearchFilters>({
    search: '',
    type: undefined,
    status: undefined,
    dateFrom: '',
    dateTo: '',
    amountMin: undefined,
    amountMax: undefined,
    page: 1,
    limit: 20
  });

  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 20,
    totalPages: 0
  });

  const [stats, setStats] = useState({
    totalTransactions: 0,
    totalInflow: 0,
    totalOutflow: 0,
    netFlow: 0,
    thisMonthTransactions: 0,
    avgTransactionAmount: 0
  });

  useEffect(() => {
    loadTransactions();
    loadStats();
  }, [filters]);

  const loadTransactions = async () => {
    try {
      setLoading(true);
      const response = await transactionsService.getUserTransactions(filters);
      setTransactions(response.transactions);
      setPagination({
        total: response.total,
        page: response.page,
        limit: response.limit,
        totalPages: response.totalPages
      });
    } catch (error) {
      toast.error('Failed to load transactions');
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const response = await transactionsService.getTransactionStats();
      setStats(response);
    } catch (error) {
      console.error('Failed to load transaction stats:', error);
    }
  };

  const handleExportTransactions = async () => {
    try {
      const blob = await transactionsService.exportTransactions(filters);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `transactions-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success('Transactions exported successfully');
    } catch (error) {
      toast.error('Failed to export transactions');
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-NG', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED': return 'success';
      case 'PENDING': return 'warning';
      case 'FAILED': return 'error';
      case 'CANCELLED': return 'secondary';
      default: return 'secondary';
    }
  };

  const getTransactionIcon = (type: string) => {
    const config = transactionTypeIcons[type] || transactionTypeIcons.TRANSFER;
    return config;
  };

  const getAmountDisplay = (transaction: Transaction) => {
    const isCredit = ['DEPOSIT', 'REFUND', 'INTEREST'].includes(transaction.type);
    const sign = isCredit ? '+' : '-';
    const colorClass = isCredit ? 'text-green-400' : 'text-red-400';
    
    return (
      <span className={`font-semibold ${colorClass}`}>
        {sign}{formatCurrency(Math.abs(transaction.amount))}
      </span>
    );
  };

  const columns = [
    {
      key: 'type',
      title: 'Type',
      render: (transaction: Transaction) => {
        const config = getTransactionIcon(transaction.type);
        const Icon = config.icon;
        return (
          <div className="flex items-center space-x-3">
            <div className={`w-10 h-10 ${config.bg} rounded-full flex items-center justify-center`}>
              <Icon className={`${config.color}`} />
            </div>
            <div>
              <p className="font-medium text-white">{transaction.type.replace('_', ' ')}</p>
              <p className="text-sm text-gray-400">{transaction.description}</p>
            </div>
          </div>
        );
      }
    },
    {
      key: 'reference',
      title: 'Reference',
      render: (transaction: Transaction) => (
        <span className="font-mono text-sm text-gray-300">{transaction.reference}</span>
      )
    },
    {
      key: 'amount',
      title: 'Amount',
      render: (transaction: Transaction) => getAmountDisplay(transaction)
    },
    {
      key: 'status',
      title: 'Status',
      render: (transaction: Transaction) => (
        <Badge variant={getStatusColor(transaction.status)}>
          {transaction.status}
        </Badge>
      )
    },
    {
      key: 'balanceAfter',
      title: 'Balance After',
      render: (transaction: Transaction) => (
        <span className="text-gray-300">{formatCurrency(transaction.balanceAfter)}</span>
      )
    },
    {
      key: 'createdAt',
      title: 'Date',
      render: (transaction: Transaction) => (
        <span className="text-gray-400">{formatDate(transaction.createdAt)}</span>
      )
    },
    {
      key: 'actions',
      title: 'Actions',
      render: (transaction: Transaction) => (
        <Button
          size="sm"
          variant="outline"
          onClick={() => {
            setSelectedTransaction(transaction);
            setShowDetailsModal(true);
          }}
        >
          <FiEye />
        </Button>
      )
    }
  ];

  return (
    <DashboardLayout title="Transactions">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white">Transaction History</h1>
            <p className="text-gray-400 mt-2">View all your account transactions</p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={loadTransactions}>
              <FiRefreshCw className="mr-2" />
              Refresh
            </Button>
            <Button variant="outline" onClick={handleExportTransactions}>
              <FiDownload className="mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Transactions</p>
                <p className="text-2xl font-bold text-white">{stats.totalTransactions.toLocaleString()}</p>
                <p className="text-blue-400 text-sm">+{stats.thisMonthTransactions} this month</p>
              </div>
              <FiActivity className="text-blue-500 text-2xl" />
            </div>
          </Card>

          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Inflow</p>
                <p className="text-2xl font-bold text-green-400">{formatCurrency(stats.totalInflow)}</p>
                <p className="text-gray-400 text-sm">Money received</p>
              </div>
              <FiArrowDownLeft className="text-green-500 text-2xl" />
            </div>
          </Card>

          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Outflow</p>
                <p className="text-2xl font-bold text-red-400">{formatCurrency(stats.totalOutflow)}</p>
                <p className="text-gray-400 text-sm">Money sent</p>
              </div>
              <FiArrowUpRight className="text-red-500 text-2xl" />
            </div>
          </Card>

          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Net Flow</p>
                <p className={`text-2xl font-bold ${stats.netFlow >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {formatCurrency(stats.netFlow)}
                </p>
                <p className="text-gray-400 text-sm">Avg: {formatCurrency(stats.avgTransactionAmount)}</p>
              </div>
              <FiDollarSign className="text-purple-500 text-2xl" />
            </div>
          </Card>
        </div>

        {/* Filters */}
        <Card className="bg-gray-800 border-gray-700 p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <Input
              placeholder="Search transactions..."
              value={filters.search}
              onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              icon={<FiSearch />}
            />

            <Select
              value={filters.type || ''}
              onChange={(value) => setFilters({ ...filters, type: value || undefined })}
              options={[
                { value: '', label: 'All Types' },
                { value: 'DEPOSIT', label: 'Deposits' },
                { value: 'WITHDRAWAL', label: 'Withdrawals' },
                { value: 'TRANSFER', label: 'Transfers' },
                { value: 'CONTRIBUTION', label: 'Contributions' },
                { value: 'INTEREST', label: 'Interest' },
                { value: 'FEE', label: 'Fees' },
                { value: 'REFUND', label: 'Refunds' },
                { value: 'PENALTY', label: 'Penalties' }
              ]}
            />

            <Select
              value={filters.status || ''}
              onChange={(value) => setFilters({ ...filters, status: value || undefined })}
              options={[
                { value: '', label: 'All Status' },
                { value: 'COMPLETED', label: 'Completed' },
                { value: 'PENDING', label: 'Pending' },
                { value: 'FAILED', label: 'Failed' },
                { value: 'CANCELLED', label: 'Cancelled' }
              ]}
            />

            <Input
              type="date"
              value={filters.dateFrom}
              onChange={(e) => setFilters({ ...filters, dateFrom: e.target.value })}
              placeholder="From Date"
            />

            <Input
              type="date"
              value={filters.dateTo}
              onChange={(e) => setFilters({ ...filters, dateTo: e.target.value })}
              placeholder="To Date"
            />

            <Button
              variant="outline"
              onClick={() => setFilters({
                search: '',
                type: undefined,
                status: undefined,
                dateFrom: '',
                dateTo: '',
                amountMin: undefined,
                amountMax: undefined,
                page: 1,
                limit: 20
              })}
            >
              Clear
            </Button>
          </div>

          {/* Amount Range */}
          <div className="grid grid-cols-2 gap-4 mt-4">
            <Input
              type="number"
              placeholder="Min amount"
              value={filters.amountMin || ''}
              onChange={(e) => setFilters({ ...filters, amountMin: Number(e.target.value) || undefined })}
            />
            <Input
              type="number"
              placeholder="Max amount"
              value={filters.amountMax || ''}
              onChange={(e) => setFilters({ ...filters, amountMax: Number(e.target.value) || undefined })}
            />
          </div>
        </Card>

        {/* Transactions Table */}
        <Card className="bg-gray-800 border-gray-700">
          <div className="p-6 border-b border-gray-700">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold text-white">
                Transactions ({pagination.total.toLocaleString()})
              </h3>
              <div className="flex items-center space-x-2">
                <Select
                  value={filters.limit?.toString() || '20'}
                  onChange={(value) => setFilters({ ...filters, limit: Number(value), page: 1 })}
                  options={[
                    { value: '10', label: '10 per page' },
                    { value: '20', label: '20 per page' },
                    { value: '50', label: '50 per page' },
                    { value: '100', label: '100 per page' }
                  ]}
                />
              </div>
            </div>
          </div>

          <Table
            data={transactions}
            columns={columns}
            loading={loading}
            emptyMessage="No transactions found"
          />

          <div className="p-6 border-t border-gray-700">
            <Pagination
              currentPage={pagination.page}
              totalPages={pagination.totalPages}
              onPageChange={(page) => setFilters({ ...filters, page })}
              showInfo={true}
              totalItems={pagination.total}
              itemsPerPage={pagination.limit}
            />
          </div>
        </Card>
      </div>

      {/* Transaction Details Modal */}
      <Modal
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        title="Transaction Details"
        size="lg"
      >
        {selectedTransaction && (
          <div className="space-y-6">
            {/* Transaction Header */}
            <div className="flex items-center space-x-4 p-4 bg-gray-700 rounded-lg">
              <div className={`w-16 h-16 ${getTransactionIcon(selectedTransaction.type).bg} rounded-full flex items-center justify-center`}>
                {React.createElement(getTransactionIcon(selectedTransaction.type).icon, {
                  className: `${getTransactionIcon(selectedTransaction.type).color} text-2xl`
                })}
              </div>
              <div>
                <h3 className="text-xl font-semibold text-white">
                  {selectedTransaction.type.replace('_', ' ')}
                </h3>
                <p className="text-gray-400">{selectedTransaction.description}</p>
                <Badge variant={getStatusColor(selectedTransaction.status)} className="mt-2">
                  {selectedTransaction.status}
                </Badge>
              </div>
            </div>

            {/* Transaction Details */}
            <div className="grid grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-white mb-3">Transaction Information</h4>
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Reference:</span>
                    <span className="text-white font-mono">{selectedTransaction.reference}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Amount:</span>
                    <span className="text-white">{getAmountDisplay(selectedTransaction)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Balance Before:</span>
                    <span className="text-white">{formatCurrency(selectedTransaction.balanceBefore)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Balance After:</span>
                    <span className="text-white">{formatCurrency(selectedTransaction.balanceAfter)}</span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-semibold text-white mb-3">Additional Details</h4>
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Date:</span>
                    <span className="text-white">{formatDate(selectedTransaction.createdAt)}</span>
                  </div>
                  {selectedTransaction.processedAt && (
                    <div className="flex justify-between">
                      <span className="text-gray-400">Processed:</span>
                      <span className="text-white">{formatDate(selectedTransaction.processedAt)}</span>
                    </div>
                  )}
                  {selectedTransaction.fees && selectedTransaction.fees > 0 && (
                    <div className="flex justify-between">
                      <span className="text-gray-400">Fees:</span>
                      <span className="text-white">{formatCurrency(selectedTransaction.fees)}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Related Information */}
            {selectedTransaction.relatedEntityType && (
              <div>
                <h4 className="font-semibold text-white mb-3">Related Information</h4>
                <div className="p-4 bg-gray-700 rounded-lg">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Related to:</span>
                    <span className="text-white">{selectedTransaction.relatedEntityType}</span>
                  </div>
                  {selectedTransaction.relatedEntityId && (
                    <div className="flex justify-between items-center mt-2">
                      <span className="text-gray-400">ID:</span>
                      <span className="text-white font-mono">{selectedTransaction.relatedEntityId}</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Notes */}
            {selectedTransaction.notes && (
              <div>
                <h4 className="font-semibold text-white mb-3">Notes</h4>
                <p className="text-gray-300 bg-gray-700 p-3 rounded-lg">
                  {selectedTransaction.notes}
                </p>
              </div>
            )}

            {/* Metadata */}
            {selectedTransaction.metadata && Object.keys(selectedTransaction.metadata).length > 0 && (
              <div>
                <h4 className="font-semibold text-white mb-3">Additional Data</h4>
                <div className="bg-gray-700 p-3 rounded-lg">
                  <pre className="text-sm text-gray-300 overflow-x-auto">
                    {JSON.stringify(selectedTransaction.metadata, null, 2)}
                  </pre>
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>
    </DashboardLayout>
  );
}
