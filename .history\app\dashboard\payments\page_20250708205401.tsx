"use client";

import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import AOS from 'aos';
import 'aos/dist/aos.css';
import { useAuth } from '../../../src/hooks/use-auth';
import { useRouter } from 'next/navigation';
import DashboardLayout from '../../../src/components/dashboard/DashboardLayout';
import { StatCard } from '../../../src/components/dashboard/DashboardCard';
import { FormModal } from '../../../src/components/ui/Modal';
import { FormInput, SelectInput } from '../../../src/components/ui/FormInput';
import Table, { StatusBadge, ActionButton } from '../../../src/components/ui/Table';
import { showToast } from '../../../src/components/ui/Toast';
import { 
  FiPlus, 
  FiDollarSign, 
  FiCreditCard, 
  FiArrowUpRight,
  FiArrowDownLeft,
  FiEye,
  FiDownload,
  FiRefreshCw,
  FiBank,
  FiSmartphone
} from 'react-icons/fi';

interface Transaction {
  id: string;
  type: 'DEPOSIT' | 'WITHDRAWAL' | 'TRANSFER' | 'INTEREST';
  amount: number;
  status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  method: 'BANK_TRANSFER' | 'CARD' | 'MOBILE_MONEY';
  description: string;
  date: string;
  reference: string;
  fee: number;
}

const mockTransactions: Transaction[] = [
  {
    id: '1',
    type: 'DEPOSIT',
    amount: 100000,
    status: 'COMPLETED',
    method: 'BANK_TRANSFER',
    description: 'Monthly savings contribution',
    date: '2024-01-15T10:30:00Z',
    reference: 'TXN001234567',
    fee: 0,
  },
  {
    id: '2',
    type: 'WITHDRAWAL',
    amount: 50000,
    status: 'PENDING',
    method: 'BANK_TRANSFER',
    description: 'Emergency fund withdrawal',
    date: '2024-01-14T14:20:00Z',
    reference: 'TXN001234568',
    fee: 500,
  },
];

export default function PaymentsPage() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const [transactions, setTransactions] = useState<Transaction[]>(mockTransactions);
  const [showDepositModal, setShowDepositModal] = useState(false);
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [depositForm, setDepositForm] = useState({
    amount: '',
    method: 'BANK_TRANSFER',
  });

  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: false,
      easing: 'ease-out-cubic',
      offset: 50,
    });
    AOS.refresh();
  }, []);

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login');
      return;
    }
    if (!isLoading && user?.role === 'ADMIN') {
      router.push('/admin/dashboard');
      return;
    }
  }, [isAuthenticated, isLoading, user, router]);

  // Paystack integration
  const paystackScriptAdded = useRef(false);
  useEffect(() => {
    if (!paystackScriptAdded.current) {
      const script = document.createElement('script');
      script.src = 'https://js.paystack.co/v1/inline.js';
      script.async = true;
      document.body.appendChild(script);
      paystackScriptAdded.current = true;
    }
  }, []);

  const handleDeposit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      const amountKobo = parseInt(depositForm.amount) * 100;
      const email = user?.email || '<EMAIL>';
      const paystackKey = process.env.NEXT_PUBLIC_PAYSTACK_KEY || 'pk_test_xxxxxxxxxxxxxxxxxxxxx';
      // @ts-ignore
      const handler = window.PaystackPop && window.PaystackPop.setup ? window.PaystackPop : window.PaystackPop;
      if (!window.PaystackPop) {
        showToast.error('Paystack script not loaded');
        setLoading(false);
        return;
      }
      window.PaystackPop.setup({
        key: paystackKey,
        email,
        amount: amountKobo,
        currency: 'NGN',
        ref: `PSK${Date.now()}`,
        callback: (response: any) => {
          // Payment successful
          const newTransaction: Transaction = {
            id: response.reference,
            type: 'DEPOSIT',
            amount: parseInt(depositForm.amount),
            status: 'COMPLETED',
            method: 'CARD',
            description: `Deposit`,
            date: new Date().toISOString(),
            reference: response.reference,
            fee: 0,
          };
          setTransactions([newTransaction, ...transactions]);
          setShowDepositModal(false);
          setDepositForm({ amount: '', method: 'BANK_TRANSFER' });
          showToast.success('Deposit successful!');
          setLoading(false);
        },
        onClose: () => {
          showToast.error('Deposit cancelled');
          setLoading(false);
        },
      }).openIframe();
    } catch (error) {
      showToast.error('Failed to process deposit');
      setLoading(false);
    }
  };

  const transactionColumns = [
    {
      key: 'type' as keyof Transaction,
      title: 'Type',
      render: (value: string) => (
        <div className="flex items-center space-x-2">
          {value === 'DEPOSIT' && <FiArrowDownLeft className="w-4 h-4 text-green-400" />}
          {value === 'WITHDRAWAL' && <FiArrowUpRight className="w-4 h-4 text-red-400" />}
          <span className="capitalize">{value.toLowerCase()}</span>
        </div>
      ),
    },
    {
      key: 'amount' as keyof Transaction,
      title: 'Amount',
      render: (value: number, row: Transaction) => (
        <div className={`font-medium ${
          row.type === 'DEPOSIT' ? 'text-green-400' : 'text-red-400'
        }`}>
          {row.type === 'DEPOSIT' ? '+' : '-'}₦{value.toLocaleString()}
        </div>
      ),
    },
    {
      key: 'status' as keyof Transaction,
      title: 'Status',
      render: (value: string) => (
        <StatusBadge 
          status={value} 
          variant={
            value === 'COMPLETED' ? 'success' : 
            value === 'PENDING' ? 'warning' : 'danger'
          } 
        />
      ),
    },
    {
      key: 'date' as keyof Transaction,
      title: 'Date',
      render: (value: string) => new Date(value).toLocaleDateString(),
    },
    {
      key: 'reference' as keyof Transaction,
      title: 'Reference',
      render: (value: string) => (
        <span className="font-mono text-sm text-gray-400">{value}</span>
      ),
    },
    {
      key: 'id' as keyof Transaction,
      title: 'Actions',
      render: (value: string) => (
        <div className="flex space-x-2">
          <ActionButton onClick={() => console.log('View', value)} variant="default">
            <FiEye className="w-4 h-4" />
          </ActionButton>
          <ActionButton onClick={() => console.log('Download', value)} variant="primary">
            <FiDownload className="w-4 h-4" />
          </ActionButton>
        </div>
      ),
    },
  ];

  const totalDeposits = transactions
    .filter(t => t.type === 'DEPOSIT' && t.status === 'COMPLETED')
    .reduce((sum, t) => sum + t.amount, 0);

  const totalWithdrawals = transactions
    .filter(t => t.type === 'WITHDRAWAL' && t.status === 'COMPLETED')
    .reduce((sum, t) => sum + t.amount, 0);

  const pendingTransactions = transactions.filter(t => t.status === 'PENDING').length;

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center" data-aos="fade-in">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto mb-4"></div>
            <p className="text-gray-400">Loading payments...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!isAuthenticated || user?.role === 'ADMIN') {
    return null;
  }

  return (
    <DashboardLayout title="Payments">
      <div className="space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0"
          data-aos="fade-down"
        >
          <div>
            <h1 className="text-2xl font-bold text-white mb-2">Payments & Transactions</h1>
            <p className="text-gray-400">Manage your deposits, withdrawals, and payment methods</p>
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={() => setShowDepositModal(true)}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
              data-aos="fade-left"
              data-aos-delay="100"
            >
              <FiArrowDownLeft className="w-4 h-4" />
              <span>Deposit</span>
            </button>
            <button
              onClick={() => setShowWithdrawModal(true)}
              className="flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
              data-aos="fade-left"
              data-aos-delay="200"
            >
              <FiArrowUpRight className="w-4 h-4" />
              <span>Withdraw</span>
            </button>
          </div>
        </motion.div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div data-aos="fade-up" data-aos-delay="100">
            <StatCard
              title="Total Deposits"
              value={`₦${totalDeposits.toLocaleString()}`}
              subtitle="All time"
              icon={FiArrowDownLeft}
              color="green"
            />
          </div>
          <div data-aos="fade-up" data-aos-delay="200">
            <StatCard
              title="Total Withdrawals"
              value={`₦${totalWithdrawals.toLocaleString()}`}
              subtitle="All time"
              icon={FiArrowUpRight}
              color="red"
            />
          </div>
          <div data-aos="fade-up" data-aos-delay="300">
            <StatCard
              title="Pending Transactions"
              value={pendingTransactions}
              subtitle="Awaiting processing"
              icon={FiRefreshCw}
              color="yellow"
            />
          </div>
        </div>

        {/* Transactions Table */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          data-aos="fade-up"
          data-aos-delay="400"
        >
          <Table
            data={transactions}
            columns={transactionColumns}
            searchable
            searchPlaceholder="Search transactions..."
            emptyMessage="No transactions found"
          />
        </motion.div>

        {/* Deposit Modal */}
        <FormModal
          isOpen={showDepositModal}
          onClose={() => setShowDepositModal(false)}
          title="Make Deposit"
          onSubmit={handleDeposit}
          submitText="Deposit Funds"
          isLoading={loading}
        >
          <div className="space-y-4">
            <FormInput
              label="Amount"
              name="amount"
              type="number"
              value={depositForm.amount}
              onChange={(e) => setDepositForm(prev => ({ ...prev, amount: e.target.value }))}
              placeholder="100000"
              required
            />

            {/* Payment method is now always Paystack/Card */}
            <FormInput
              label="Payment Method"
              name="method"
              value="Paystack (Card/Bank/USSD)"
              readOnly
              disabled
            />

            {/* Savings Plan selection removed as it's not relevant for deposit */}
          </div>
        </FormModal>

      </div>
    </DashboardLayout>
  );
}
