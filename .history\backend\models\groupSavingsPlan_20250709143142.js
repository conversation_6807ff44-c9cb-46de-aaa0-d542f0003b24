const mongoose = require('mongoose');

const GroupSavingsPlanSchema = new mongoose.Schema({
  status: {
    type: String,
    enum: ['RECRUITING', 'ACTIVE', 'COMPLETED', 'FILLED'],
    default: 'RECRUITING',
  },
  title: {
    type: String,
    required: true,
  },
  depositFrequency: {
    type: String,
    enum: ['Daily', 'Weekly', 'Bi-weekly', 'Monthly', 'Yearly', 'daily', 'weekly', 'bi-weekly', 'monthly', 'yearly'],
    required: true,
  },
  depositAmount: {
    type: Number,
    required: true,
  },
  targetDate: {
    type: Date,
    required: true,
  },
  targetAmount: {
    type: Number,
    required: true,
  },
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  members: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  }],
  pendingInvites: [{
    type: String, // email addresses
  }],
  inviteCode: {
    type: String,
    unique: true,
    index: true,
  },
  isPublic: {
    type: Boolean,
    default: false,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  savedAmount: {
    type: Number,
    default: 0,
  },
  lastDepositDate: {
    type: Date,
    default: null,
  },
});

module.exports = mongoose.model('GroupSavingsPlan', GroupSavingsPlanSchema);
