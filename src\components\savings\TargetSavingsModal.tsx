"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  FiX, 
  FiTarget, 
  FiCalendar, 
  FiDollarSign,
  FiTrendingUp,
  FiRepeat,
  FiClock,
  FiCalculator
} from 'react-icons/fi';
import { SavingsPlanFormData, CreateSavingsPlanData } from '../../types/savings';
import { savingsService } from '../../services/savings';

interface TargetSavingsModalProps {
  onClose: () => void;
  onSuccess: () => void;
  editPlan?: any; // Will be properly typed later
}

export default function TargetSavingsModal({ onClose, onSuccess, editPlan }: TargetSavingsModalProps) {
  const [formData, setFormData] = useState<SavingsPlanFormData>({
    name: editPlan?.name || '',
    description: editPlan?.description || '',
    planType: 'TARGET',
    targetAmount: editPlan?.targetAmount?.toString() || '',
    contributionAmount: editPlan?.contributionAmount?.toString() || '',
    frequency: editPlan?.frequency || 'MONTHLY',
    duration: editPlan?.duration?.toString() || '',
    autoDebit: editPlan?.autoDebit || false,
    startDate: editPlan?.startDate || new Date().toISOString().split('T')[0],
  });

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [calculationMode, setCalculationMode] = useState<'amount' | 'duration'>('amount');

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = 'Plan name is required';
    }

    if (!formData.targetAmount || parseFloat(formData.targetAmount) <= 0) {
      errors.targetAmount = 'Target amount must be greater than 0';
    }

    if (calculationMode === 'amount') {
      if (!formData.duration || parseInt(formData.duration) <= 0) {
        errors.duration = 'Duration must be greater than 0';
      }
    } else {
      if (!formData.contributionAmount || parseFloat(formData.contributionAmount) <= 0) {
        errors.contributionAmount = 'Contribution amount must be greater than 0';
      }
    }

    if (!formData.startDate) {
      errors.startDate = 'Start date is required';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const calculateMissingValue = () => {
    const target = parseFloat(formData.targetAmount) || 0;
    const contribution = parseFloat(formData.contributionAmount) || 0;
    const duration = parseInt(formData.duration) || 0;

    if (calculationMode === 'amount' && target > 0 && duration > 0) {
      // Calculate required contribution amount
      const frequencyMultiplier = formData.frequency === 'DAILY' ? 30 : formData.frequency === 'WEEKLY' ? 4 : 1;
      const totalContributions = duration * frequencyMultiplier;
      const requiredContribution = target / totalContributions;
      
      setFormData(prev => ({
        ...prev,
        contributionAmount: requiredContribution.toFixed(2)
      }));
    } else if (calculationMode === 'duration' && target > 0 && contribution > 0) {
      // Calculate required duration
      const frequencyMultiplier = formData.frequency === 'DAILY' ? 30 : formData.frequency === 'WEEKLY' ? 4 : 1;
      const contributionsPerMonth = frequencyMultiplier;
      const requiredMonths = Math.ceil(target / (contribution * contributionsPerMonth));
      
      setFormData(prev => ({
        ...prev,
        duration: requiredMonths.toString()
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      setIsLoading(true);
      setError(null);

      const planData: CreateSavingsPlanData = {
        name: formData.name,
        description: formData.description,
        planType: 'TARGET',
        targetAmount: parseFloat(formData.targetAmount),
        contributionAmount: parseFloat(formData.contributionAmount),
        frequency: formData.frequency,
        duration: parseInt(formData.duration),
        autoDebit: formData.autoDebit,
        startDate: formData.startDate,
      };

      if (editPlan) {
        await savingsService.updateSavingsPlan(editPlan.id, planData);
      } else {
        await savingsService.createSavingsPlan(planData);
      }

      onSuccess();
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to save target savings plan');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const getFrequencyText = () => {
    switch (formData.frequency) {
      case 'DAILY': return 'daily';
      case 'WEEKLY': return 'weekly';
      case 'MONTHLY': return 'monthly';
      default: return 'monthly';
    }
  };

  const calculateEndDate = () => {
    if (!formData.startDate || !formData.duration) return '';
    
    const startDate = new Date(formData.startDate);
    const endDate = new Date(startDate);
    endDate.setMonth(endDate.getMonth() + parseInt(formData.duration));
    
    return endDate.toLocaleDateString();
  };

  const calculateTotalContributions = () => {
    const contribution = parseFloat(formData.contributionAmount) || 0;
    const duration = parseInt(formData.duration) || 0;
    const frequencyMultiplier = formData.frequency === 'DAILY' ? 30 : formData.frequency === 'WEEKLY' ? 4 : 1;
    
    return contribution * duration * frequencyMultiplier;
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-gray-900 border border-gray-800 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-800">
          <div className="flex items-center space-x-3">
            <FiTarget className="text-blue-400 text-2xl" />
            <h2 className="text-xl font-bold text-white">
              {editPlan ? 'Edit Target Savings' : 'Create Target Savings Plan'}
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <FiX size={24} />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Error Display */}
          {error && (
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          )}

          {/* Plan Name */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Plan Name</label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:border-blue-500 focus:outline-none text-white"
              placeholder="e.g., House Down Payment, New Car Fund"
            />
            {validationErrors.name && (
              <p className="text-red-400 text-xs mt-1">{validationErrors.name}</p>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Description (Optional)</label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={2}
              className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:border-blue-500 focus:outline-none text-white resize-none"
              placeholder="Describe your target savings plan..."
            />
          </div>

          {/* Target Amount */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Target Amount (₦)</label>
            <div className="relative">
              <FiDollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="number"
                name="targetAmount"
                value={formData.targetAmount}
                onChange={handleInputChange}
                className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:border-blue-500 focus:outline-none text-white"
                placeholder="1000000"
                min="0"
                step="1000"
              />
            </div>
            {validationErrors.targetAmount && (
              <p className="text-red-400 text-xs mt-1">{validationErrors.targetAmount}</p>
            )}
          </div>

          {/* Calculation Mode Toggle */}
          <div>
            <label className="block text-sm font-medium text-white mb-3">What would you like to calculate?</label>
            <div className="flex space-x-4">
              <label className={`flex items-center space-x-2 px-4 py-3 border-2 rounded-lg cursor-pointer transition-colors ${
                calculationMode === 'amount' 
                  ? 'border-blue-500 bg-blue-500/10' 
                  : 'border-gray-700 hover:border-gray-600'
              }`}>
                <input
                  type="radio"
                  name="calculationMode"
                  value="amount"
                  checked={calculationMode === 'amount'}
                  onChange={(e) => setCalculationMode(e.target.value as 'amount' | 'duration')}
                  className="sr-only"
                />
                <FiCalculator className={calculationMode === 'amount' ? 'text-blue-400' : 'text-gray-400'} />
                <span className="text-sm font-medium text-white">Calculate contribution amount</span>
              </label>
              
              <label className={`flex items-center space-x-2 px-4 py-3 border-2 rounded-lg cursor-pointer transition-colors ${
                calculationMode === 'duration' 
                  ? 'border-blue-500 bg-blue-500/10' 
                  : 'border-gray-700 hover:border-gray-600'
              }`}>
                <input
                  type="radio"
                  name="calculationMode"
                  value="duration"
                  checked={calculationMode === 'duration'}
                  onChange={(e) => setCalculationMode(e.target.value as 'amount' | 'duration')}
                  className="sr-only"
                />
                <FiClock className={calculationMode === 'duration' ? 'text-blue-400' : 'text-gray-400'} />
                <span className="text-sm font-medium text-white">Calculate time needed</span>
              </label>
            </div>
          </div>

          {/* Frequency */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Contribution Frequency</label>
            <div className="relative">
              <FiRepeat className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <select
                name="frequency"
                value={formData.frequency}
                onChange={handleInputChange}
                className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:border-blue-500 focus:outline-none text-white appearance-none"
              >
                <option value="DAILY">Daily</option>
                <option value="WEEKLY">Weekly</option>
                <option value="MONTHLY">Monthly</option>
              </select>
            </div>
          </div>

          {/* Dynamic Input Fields */}
          <div className="grid grid-cols-2 gap-4">
            {calculationMode === 'amount' ? (
              <>
                <div>
                  <label className="block text-sm font-medium text-white mb-2">Duration (Months)</label>
                  <div className="relative">
                    <FiClock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="number"
                      name="duration"
                      value={formData.duration}
                      onChange={handleInputChange}
                      className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:border-blue-500 focus:outline-none text-white"
                      placeholder="12"
                      min="1"
                      max="120"
                    />
                  </div>
                  {validationErrors.duration && (
                    <p className="text-red-400 text-xs mt-1">{validationErrors.duration}</p>
                  )}
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-white mb-2">
                    Calculated Contribution (₦)
                  </label>
                  <div className="relative">
                    <FiDollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="number"
                      name="contributionAmount"
                      value={formData.contributionAmount}
                      onChange={handleInputChange}
                      className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:border-blue-500 focus:outline-none text-white"
                      placeholder="Calculated automatically"
                      readOnly
                    />
                  </div>
                </div>
              </>
            ) : (
              <>
                <div>
                  <label className="block text-sm font-medium text-white mb-2">
                    Contribution Amount (₦)
                  </label>
                  <div className="relative">
                    <FiDollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="number"
                      name="contributionAmount"
                      value={formData.contributionAmount}
                      onChange={handleInputChange}
                      className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:border-blue-500 focus:outline-none text-white"
                      placeholder="50000"
                      min="0"
                      step="1000"
                    />
                  </div>
                  {validationErrors.contributionAmount && (
                    <p className="text-red-400 text-xs mt-1">{validationErrors.contributionAmount}</p>
                  )}
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-white mb-2">
                    Calculated Duration (Months)
                  </label>
                  <div className="relative">
                    <FiClock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="number"
                      name="duration"
                      value={formData.duration}
                      onChange={handleInputChange}
                      className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:border-blue-500 focus:outline-none text-white"
                      placeholder="Calculated automatically"
                      readOnly
                    />
                  </div>
                </div>
              </>
            )}
          </div>

          {/* Calculate Button */}
          <button
            type="button"
            onClick={calculateMissingValue}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg transition-colors flex items-center justify-center space-x-2"
          >
            <FiCalculator />
            <span>Calculate {calculationMode === 'amount' ? 'Contribution Amount' : 'Duration'}</span>
          </button>

          {/* Start Date */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Start Date</label>
            <div className="relative">
              <FiCalendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="date"
                name="startDate"
                value={formData.startDate}
                onChange={handleInputChange}
                className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:border-blue-500 focus:outline-none text-white"
              />
            </div>
            {validationErrors.startDate && (
              <p className="text-red-400 text-xs mt-1">{validationErrors.startDate}</p>
            )}
          </div>

          {/* Auto Debit */}
          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              name="autoDebit"
              checked={formData.autoDebit}
              onChange={handleInputChange}
              className="w-4 h-4 text-blue-600 bg-gray-800 border-gray-700 rounded focus:ring-blue-500"
            />
            <label className="text-sm text-white">
              Enable automatic debit for {getFrequencyText()} contributions
            </label>
          </div>

          {/* Plan Summary */}
          {formData.targetAmount && formData.contributionAmount && formData.duration && (
            <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
              <h4 className="text-blue-400 font-medium mb-3 flex items-center">
                <FiTrendingUp className="mr-2" />
                Plan Summary
              </h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-400">Target Amount:</span>
                  <p className="text-white font-medium">
                    {savingsService.formatCurrency(parseFloat(formData.targetAmount))}
                  </p>
                </div>
                <div>
                  <span className="text-gray-400">Duration:</span>
                  <p className="text-white font-medium">{formData.duration} months</p>
                </div>
                <div>
                  <span className="text-gray-400">{getFrequencyText().charAt(0).toUpperCase() + getFrequencyText().slice(1)} Contribution:</span>
                  <p className="text-blue-400 font-medium">
                    {savingsService.formatCurrency(parseFloat(formData.contributionAmount))}
                  </p>
                </div>
                <div>
                  <span className="text-gray-400">End Date:</span>
                  <p className="text-white font-medium">{calculateEndDate()}</p>
                </div>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex space-x-4 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-3 border border-gray-700 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="flex-1 px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50"
            >
              {isLoading ? 'Saving...' : editPlan ? 'Update Plan' : 'Create Target Plan'}
            </button>
          </div>
        </form>
      </motion.div>
    </div>
  );
}
