"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  FiX, 
  FiTarget, 
  FiCalendar, 
  FiDollarSign,
  FiImage,
  FiFlag,
  FiTrendingUp
} from 'react-icons/fi';
import { SavingsGoalFormData, CreateSavingsGoalData } from '../../types/savings';
import { savingsService } from '../../services/savings';

interface SavingsGoalModalProps {
  onClose: () => void;
  onSuccess: () => void;
  editGoal?: any; // Will be properly typed later
}

const goalCategories = [
  { value: 'EMERGENCY', label: 'Emergency Fund', icon: '🚨', color: 'red' },
  { value: 'VACATION', label: 'Vacation', icon: '✈️', color: 'blue' },
  { value: 'EDUCATION', label: 'Education', icon: '🎓', color: 'purple' },
  { value: 'HOUSE', label: 'House', icon: '🏠', color: 'green' },
  { value: 'CAR', label: 'Car', icon: '🚗', color: 'yellow' },
  { value: 'BUSINESS', label: 'Business', icon: '💼', color: 'indigo' },
  { value: 'OTHER', label: 'Other', icon: '🎯', color: 'gray' }
];

const priorities = [
  { value: 'LOW', label: 'Low', color: 'gray' },
  { value: 'MEDIUM', label: 'Medium', color: 'yellow' },
  { value: 'HIGH', label: 'High', color: 'red' }
];

export default function SavingsGoalModal({ onClose, onSuccess, editGoal }: SavingsGoalModalProps) {
  const [formData, setFormData] = useState<SavingsGoalFormData>({
    title: editGoal?.title || '',
    description: editGoal?.description || '',
    targetAmount: editGoal?.targetAmount?.toString() || '',
    targetDate: editGoal?.targetDate || '',
    category: editGoal?.category || 'OTHER',
    priority: editGoal?.priority || 'MEDIUM',
    imageUrl: editGoal?.imageUrl || '',
  });

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.title.trim()) {
      errors.title = 'Goal title is required';
    }

    if (!formData.description.trim()) {
      errors.description = 'Goal description is required';
    }

    if (!formData.targetAmount || parseFloat(formData.targetAmount) <= 0) {
      errors.targetAmount = 'Target amount must be greater than 0';
    }

    if (!formData.targetDate) {
      errors.targetDate = 'Target date is required';
    } else {
      const targetDate = new Date(formData.targetDate);
      const today = new Date();
      if (targetDate <= today) {
        errors.targetDate = 'Target date must be in the future';
      }
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      setIsLoading(true);
      setError(null);

      const goalData: CreateSavingsGoalData = {
        title: formData.title,
        description: formData.description,
        targetAmount: parseFloat(formData.targetAmount),
        targetDate: formData.targetDate,
        category: formData.category,
        priority: formData.priority,
        imageUrl: formData.imageUrl || undefined,
      };

      if (editGoal) {
        await savingsService.updateSavingsGoal(editGoal.id, goalData);
      } else {
        await savingsService.createSavingsGoal(goalData);
      }

      onSuccess();
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to save goal');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const calculateMonthsToTarget = () => {
    if (!formData.targetDate) return 0;
    const target = new Date(formData.targetDate);
    const today = new Date();
    const diffTime = target.getTime() - today.getTime();
    const diffMonths = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 30));
    return Math.max(0, diffMonths);
  };

  const calculateMonthlyContribution = () => {
    const months = calculateMonthsToTarget();
    const target = parseFloat(formData.targetAmount) || 0;
    return months > 0 ? target / months : 0;
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-gray-900 border border-gray-800 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-800">
          <div className="flex items-center space-x-3">
            <FiTarget className="text-purple-400 text-2xl" />
            <h2 className="text-xl font-bold text-white">
              {editGoal ? 'Edit Savings Goal' : 'Create Savings Goal'}
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <FiX size={24} />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Error Display */}
          {error && (
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          )}

          {/* Goal Title */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Goal Title</label>
            <input
              type="text"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:border-purple-500 focus:outline-none text-white"
              placeholder="e.g., Dream Vacation to Dubai"
            />
            {validationErrors.title && (
              <p className="text-red-400 text-xs mt-1">{validationErrors.title}</p>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Description</label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={3}
              className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:border-purple-500 focus:outline-none text-white resize-none"
              placeholder="Describe your goal and why it's important to you..."
            />
            {validationErrors.description && (
              <p className="text-red-400 text-xs mt-1">{validationErrors.description}</p>
            )}
          </div>

          {/* Category Selection */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Category</label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {goalCategories.map((category) => (
                <label
                  key={category.value}
                  className={`relative flex flex-col items-center p-3 border-2 rounded-lg cursor-pointer transition-colors ${
                    formData.category === category.value
                      ? `border-${category.color}-500 bg-${category.color}-500/10`
                      : 'border-gray-700 hover:border-gray-600'
                  }`}
                >
                  <input
                    type="radio"
                    name="category"
                    value={category.value}
                    checked={formData.category === category.value}
                    onChange={handleInputChange}
                    className="sr-only"
                  />
                  <span className="text-2xl mb-1">{category.icon}</span>
                  <span className="text-xs font-medium text-white text-center">{category.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Target Amount & Date */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-white mb-2">Target Amount (₦)</label>
              <div className="relative">
                <FiDollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="number"
                  name="targetAmount"
                  value={formData.targetAmount}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:border-purple-500 focus:outline-none text-white"
                  placeholder="500000"
                  min="0"
                  step="1000"
                />
              </div>
              {validationErrors.targetAmount && (
                <p className="text-red-400 text-xs mt-1">{validationErrors.targetAmount}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-white mb-2">Target Date</label>
              <div className="relative">
                <FiCalendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="date"
                  name="targetDate"
                  value={formData.targetDate}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:border-purple-500 focus:outline-none text-white"
                  min={new Date().toISOString().split('T')[0]}
                />
              </div>
              {validationErrors.targetDate && (
                <p className="text-red-400 text-xs mt-1">{validationErrors.targetDate}</p>
              )}
            </div>
          </div>

          {/* Priority */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Priority</label>
            <div className="flex space-x-4">
              {priorities.map((priority) => (
                <label
                  key={priority.value}
                  className={`flex items-center space-x-2 px-4 py-2 border-2 rounded-lg cursor-pointer transition-colors ${
                    formData.priority === priority.value
                      ? `border-${priority.color}-500 bg-${priority.color}-500/10`
                      : 'border-gray-700 hover:border-gray-600'
                  }`}
                >
                  <input
                    type="radio"
                    name="priority"
                    value={priority.value}
                    checked={formData.priority === priority.value}
                    onChange={handleInputChange}
                    className="sr-only"
                  />
                  <FiFlag className={`${
                    formData.priority === priority.value ? `text-${priority.color}-400` : 'text-gray-400'
                  }`} />
                  <span className="text-sm font-medium text-white">{priority.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Image URL */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Goal Image (Optional)</label>
            <div className="relative">
              <FiImage className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="url"
                name="imageUrl"
                value={formData.imageUrl}
                onChange={handleInputChange}
                className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:border-purple-500 focus:outline-none text-white"
                placeholder="https://example.com/image.jpg"
              />
            </div>
          </div>

          {/* Goal Summary */}
          {formData.targetAmount && formData.targetDate && (
            <div className="bg-purple-500/10 border border-purple-500/20 rounded-lg p-4">
              <h4 className="text-purple-400 font-medium mb-3 flex items-center">
                <FiTrendingUp className="mr-2" />
                Goal Summary
              </h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-400">Target Amount:</span>
                  <p className="text-white font-medium">
                    {savingsService.formatCurrency(parseFloat(formData.targetAmount))}
                  </p>
                </div>
                <div>
                  <span className="text-gray-400">Time to Goal:</span>
                  <p className="text-white font-medium">{calculateMonthsToTarget()} months</p>
                </div>
                <div>
                  <span className="text-gray-400">Monthly Contribution:</span>
                  <p className="text-purple-400 font-medium">
                    {savingsService.formatCurrency(calculateMonthlyContribution())}
                  </p>
                </div>
                <div>
                  <span className="text-gray-400">Priority:</span>
                  <p className="text-white font-medium">{formData.priority}</p>
                </div>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex space-x-4 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-3 border border-gray-700 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="flex-1 px-4 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors disabled:opacity-50"
            >
              {isLoading ? 'Saving...' : editGoal ? 'Update Goal' : 'Create Goal'}
            </button>
          </div>
        </form>
      </motion.div>
    </div>
  );
}
