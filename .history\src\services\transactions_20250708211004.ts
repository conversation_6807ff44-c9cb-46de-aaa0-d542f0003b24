import { 
  Transaction, 
  CreateTransactionData,
  TransactionStats,
  TransactionSearchFilters,
  PaginatedTransactionResponse,
  TransactionSummary,
  TransactionExport,
  ApiResponse
} from '../types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

class TransactionsService {
  private getAuthHeaders() {
    const token = localStorage.getItem('auth_token');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` })
    };
  }

  // Transaction Management
  async getUserTransactions(userId: string, filters?: TransactionSearchFilters): Promise<PaginatedTransactionResponse> {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }
    const url = `${API_BASE_URL}/transactions/user/${userId}?${params}`;
    const response = await fetch(url, {
      headers: this.getAuthHeaders()
    });
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch user transactions');
    }
    return response.json();
  }

  async getAllTransactions(filters?: TransactionSearchFilters): Promise<PaginatedTransactionResponse> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/transactions/all?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch all transactions');
    }

    return response.json();
  }

  async getTransactionById(transactionId: string): Promise<Transaction> {
    const response = await fetch(`${API_BASE_URL}/transactions/${transactionId}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch transaction');
    }

    return response.json();
  }

  async createTransaction(data: CreateTransactionData): Promise<Transaction> {
    const response = await fetch(`${API_BASE_URL}/transactions`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to create transaction');
    }

    return response.json();
  }

  async updateTransactionStatus(
    transactionId: string, 
    status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'CANCELLED',
    notes?: string
  ): Promise<Transaction> {
    const response = await fetch(`${API_BASE_URL}/transactions/${transactionId}/status`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ status, notes })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update transaction status');
    }

    return response.json();
  }

  // Statistics and Analytics
  async getTransactionStats(filters?: {
    dateFrom?: string;
    dateTo?: string;
    userId?: string;
    type?: string;
  }): Promise<TransactionStats> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/transactions/stats?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch transaction statistics');
    }

    return response.json();
  }

  async getTransactionSummary(
    period: 'TODAY' | 'WEEK' | 'MONTH' | 'YEAR',
    userId?: string
  ): Promise<TransactionSummary> {
    const params = new URLSearchParams({ period });
    if (userId) params.append('userId', userId);

    const response = await fetch(`${API_BASE_URL}/transactions/summary?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch transaction summary');
    }

    return response.json();
  }

  async getTransactionAnalytics(
    period: 'daily' | 'weekly' | 'monthly' | 'yearly' = 'monthly',
    filters?: {
      dateFrom?: string;
      dateTo?: string;
      userId?: string;
      type?: string;
    }
  ): Promise<any> {
    const params = new URLSearchParams({ period });
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/transactions/analytics?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch transaction analytics');
    }

    return response.json();
  }

  // Export and Reporting
  async exportTransactions(exportData: TransactionExport): Promise<{ downloadUrl: string; fileName: string }> {
    const response = await fetch(`${API_BASE_URL}/transactions/export`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(exportData)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to export transactions');
    }

    return response.json();
  }

  async generateTransactionReport(
    reportType: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'CUSTOM',
    filters?: TransactionSearchFilters
  ): Promise<{ reportUrl: string; reportId: string }> {
    const response = await fetch(`${API_BASE_URL}/transactions/report`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ reportType, filters })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to generate transaction report');
    }

    return response.json();
  }

  // Search and Filtering
  async searchTransactions(
    query: string,
    filters?: Partial<TransactionSearchFilters>
  ): Promise<Transaction[]> {
    const params = new URLSearchParams({ search: query });
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/transactions/search?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to search transactions');
    }

    return response.json();
  }

  async getTransactionsByReference(reference: string): Promise<Transaction[]> {
    const response = await fetch(`${API_BASE_URL}/transactions/reference/${reference}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch transactions by reference');
    }

    return response.json();
  }

  // Utility Methods
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN'
    }).format(amount);
  }

  getTransactionTypeIcon(type: string): string {
    const icons: Record<string, string> = {
      DEPOSIT: '💰',
      WITHDRAWAL: '🏦',
      CONTRIBUTION: '📈',
      INTEREST: '💎',
      PENALTY: '⚠️',
      REFUND: '↩️',
      TRANSFER: '🔄',
      FEE: '💳',
      BONUS: '🎁'
    };
    
    return icons[type] || '💰';
  }

  getStatusColor(status: string): string {
    const colors: Record<string, string> = {
      PENDING: '#F59E0B',
      PROCESSING: '#3B82F6',
      COMPLETED: '#10B981',
      FAILED: '#EF4444',
      CANCELLED: '#6B7280'
    };
    
    return colors[status] || '#6B7280';
  }

  getTransactionTypeColor(type: string): string {
    const colors: Record<string, string> = {
      DEPOSIT: '#10B981',
      WITHDRAWAL: '#EF4444',
      CONTRIBUTION: '#3B82F6',
      INTEREST: '#8B5CF6',
      PENALTY: '#F59E0B',
      REFUND: '#10B981',
      TRANSFER: '#6B7280',
      FEE: '#F59E0B',
      BONUS: '#10B981'
    };
    
    return colors[type] || '#6B7280';
  }

  formatTransactionDescription(transaction: Transaction): string {
    const { type, subType, description, plan, goal, target, group } = transaction;
    
    if (description) return description;
    
    // Generate description based on type and related entities
    switch (type) {
      case 'DEPOSIT':
        return 'Account deposit';
      case 'WITHDRAWAL':
        return plan ? `Withdrawal from ${plan.name}` : 'Account withdrawal';
      case 'CONTRIBUTION':
        if (plan) return `Contribution to ${plan.name}`;
        if (goal) return `Contribution to ${goal.title}`;
        if (target) return `Contribution to ${target.title}`;
        if (group) return `Contribution to ${group.name}`;
        return 'Savings contribution';
      case 'INTEREST':
        return plan ? `Interest earned on ${plan.name}` : 'Interest earned';
      case 'PENALTY':
        return 'Penalty charge';
      case 'REFUND':
        return 'Refund processed';
      case 'TRANSFER':
        return 'Account transfer';
      case 'FEE':
        return 'Service fee';
      case 'BONUS':
        return 'Bonus credit';
      default:
        return 'Transaction';
    }
  }

  generateReference(): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8);
    return `TXN_${timestamp}_${random}`.toUpperCase();
  }

  calculateRunningBalance(transactions: Transaction[]): Transaction[] {
    let runningBalance = 0;
    
    return transactions.map(transaction => {
      // Calculate balance change based on transaction type
      let balanceChange = 0;
      
      switch (transaction.type) {
        case 'DEPOSIT':
        case 'REFUND':
        case 'INTEREST':
        case 'BONUS':
          balanceChange = transaction.netAmount;
          break;
        case 'WITHDRAWAL':
        case 'CONTRIBUTION':
        case 'PENALTY':
        case 'FEE':
          balanceChange = -transaction.netAmount;
          break;
        case 'TRANSFER':
          // Transfer direction depends on metadata or other factors
          balanceChange = transaction.metadata?.direction === 'IN' ? transaction.netAmount : -transaction.netAmount;
          break;
      }
      
      runningBalance += balanceChange;
      
      return {
        ...transaction,
        balanceAfter: runningBalance
      };
    });
  }
}

export const transactionsService = new TransactionsService();
