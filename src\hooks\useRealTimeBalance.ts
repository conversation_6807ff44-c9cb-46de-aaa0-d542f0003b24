import { useEffect, useState, useCallback } from 'react';
import { webSocketService, BalanceUpdateEvent, TransactionEvent } from '../services/websocket.service';
import { userService } from '../services';

export interface BalanceData {
  balance: number;
  totalSavings: number;
  totalEarnings: number;
  pendingTransactions: number;
  lastUpdated: string;
}

export interface UseRealTimeBalanceOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
  enableAnimations?: boolean;
}

export interface UseRealTimeBalanceReturn {
  balanceData: BalanceData | null;
  isLoading: boolean;
  isConnected: boolean;
  error: string | null;
  refreshBalance: () => Promise<void>;
  balanceChange: number | null;
  showBalanceAnimation: boolean;
}

const defaultOptions: UseRealTimeBalanceOptions = {
  autoRefresh: true,
  refreshInterval: 30000, // 30 seconds
  enableAnimations: true
};

export function useRealTimeBalance(
  options: UseRealTimeBalanceOptions = {}
): UseRealTimeBalanceReturn {
  const opts = { ...defaultOptions, ...options };
  
  const [balanceData, setBalanceData] = useState<BalanceData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [balanceChange, setBalanceChange] = useState<number | null>(null);
  const [showBalanceAnimation, setShowBalanceAnimation] = useState(false);

  const refreshBalance = useCallback(async () => {
    try {
      setError(null);
      const profile = await userService.getCurrentUserProfile();
      const newBalanceData: BalanceData = {
        balance: profile.balance,
        totalSavings: profile.totalSavings,
        totalEarnings: profile.totalEarnings,
        pendingTransactions: profile.pendingTransactions || 0,
        lastUpdated: new Date().toISOString()
      };

      // Calculate balance change for animation
      if (balanceData && opts.enableAnimations) {
        const change = newBalanceData.balance - balanceData.balance;
        if (change !== 0) {
          setBalanceChange(change);
          setShowBalanceAnimation(true);
          
          // Hide animation after 3 seconds
          setTimeout(() => {
            setShowBalanceAnimation(false);
            setBalanceChange(null);
          }, 3000);
        }
      }

      setBalanceData(newBalanceData);
      setIsLoading(false);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch balance');
      setIsLoading(false);
    }
  }, [balanceData, opts.enableAnimations]);

  // Handle real-time balance updates
  const handleBalanceUpdate = useCallback((event: BalanceUpdateEvent) => {
    const newBalanceData: BalanceData = {
      balance: event.data.newBalance,
      totalSavings: balanceData?.totalSavings || 0,
      totalEarnings: balanceData?.totalEarnings || 0,
      pendingTransactions: balanceData?.pendingTransactions || 0,
      lastUpdated: event.timestamp
    };

    // Show animation for balance change
    if (opts.enableAnimations && event.data.change !== 0) {
      setBalanceChange(event.data.change);
      setShowBalanceAnimation(true);
      
      setTimeout(() => {
        setShowBalanceAnimation(false);
        setBalanceChange(null);
      }, 3000);
    }

    setBalanceData(newBalanceData);
  }, [balanceData, opts.enableAnimations]);

  // Handle transaction updates that might affect balance
  const handleTransactionUpdate = useCallback((event: TransactionEvent) => {
    if (event.data.status === 'COMPLETED') {
      // Request fresh balance data when transaction completes
      webSocketService.requestBalanceUpdate();
    }
    
    // Update pending transactions count
    setBalanceData(prev => {
      if (!prev) return prev;
      
      const pendingChange = event.data.status === 'PENDING' ? 1 : 
                           event.data.status === 'COMPLETED' || event.data.status === 'FAILED' ? -1 : 0;
      
      return {
        ...prev,
        pendingTransactions: Math.max(0, prev.pendingTransactions + pendingChange),
        lastUpdated: event.timestamp
      };
    });
  }, []);

  // WebSocket event subscriptions
  useEffect(() => {
    const unsubscribeBalance = webSocketService.on('balance_update', handleBalanceUpdate);
    const unsubscribeTransaction = webSocketService.on('transaction', handleTransactionUpdate);
    const unsubscribeConnection = webSocketService.onConnectionChange(setIsConnected);

    return () => {
      unsubscribeBalance();
      unsubscribeTransaction();
      unsubscribeConnection();
    };
  }, [handleBalanceUpdate, handleTransactionUpdate]);

  // Initial balance fetch
  useEffect(() => {
    refreshBalance();
  }, []);

  // Auto-refresh balance
  useEffect(() => {
    if (!opts.autoRefresh || !opts.refreshInterval) return;

    const interval = setInterval(() => {
      if (isConnected) {
        // Request real-time update instead of full refresh when connected
        webSocketService.requestBalanceUpdate();
      } else {
        // Fall back to API call when not connected
        refreshBalance();
      }
    }, opts.refreshInterval);

    return () => clearInterval(interval);
  }, [opts.autoRefresh, opts.refreshInterval, isConnected, refreshBalance]);

  // Connect to WebSocket
  useEffect(() => {
    webSocketService.connect();
  }, []);

  return {
    balanceData,
    isLoading,
    isConnected,
    error,
    refreshBalance,
    balanceChange,
    showBalanceAnimation
  };
}

// Hook for real-time transaction monitoring
export function useRealTimeTransactions() {
  const [recentTransactions, setRecentTransactions] = useState<any[]>([]);
  const [pendingCount, setPendingCount] = useState(0);

  useEffect(() => {
    const unsubscribe = webSocketService.on('transaction', (event) => {
      setRecentTransactions(prev => {
        const updated = [event.data, ...prev];
        return updated.slice(0, 10); // Keep only last 10 transactions
      });

      // Update pending count
      if (event.data.status === 'PENDING') {
        setPendingCount(prev => prev + 1);
      } else if (event.data.status === 'COMPLETED' || event.data.status === 'FAILED') {
        setPendingCount(prev => Math.max(0, prev - 1));
      }
    });

    return unsubscribe;
  }, []);

  return {
    recentTransactions,
    pendingCount
  };
}

// Hook for real-time group savings updates
export function useRealTimeGroupSavings(groupId?: string) {
  const [groupUpdates, setGroupUpdates] = useState<any[]>([]);
  const [memberActivity, setMemberActivity] = useState<any[]>([]);

  useEffect(() => {
    if (groupId) {
      webSocketService.joinRoom(`group_${groupId}`);
    }

    const unsubscribe = webSocketService.on('group_savings', (event) => {
      if (!groupId || event.data.groupId === groupId) {
        setGroupUpdates(prev => [event.data, ...prev.slice(0, 19)]); // Keep last 20 updates
        
        if (event.data.eventType === 'MEMBER_JOINED' || 
            event.data.eventType === 'MEMBER_LEFT' || 
            event.data.eventType === 'CONTRIBUTION_MADE') {
          setMemberActivity(prev => [event.data, ...prev.slice(0, 9)]); // Keep last 10 activities
        }
      }
    });

    return () => {
      unsubscribe();
      if (groupId) {
        webSocketService.leaveRoom(`group_${groupId}`);
      }
    };
  }, [groupId]);

  return {
    groupUpdates,
    memberActivity
  };
}

// Hook for real-time system status
export function useRealTimeSystemStatus() {
  const [systemStatus, setSystemStatus] = useState({
    isOnline: true,
    maintenanceMode: false,
    lastHeartbeat: new Date().toISOString()
  });

  useEffect(() => {
    const unsubscribeConnection = webSocketService.onConnectionChange((connected) => {
      setSystemStatus(prev => ({
        ...prev,
        isOnline: connected,
        lastHeartbeat: new Date().toISOString()
      }));
    });

    const unsubscribeMaintenance = webSocketService.on('system_maintenance', (event) => {
      setSystemStatus(prev => ({
        ...prev,
        maintenanceMode: event.data.type === 'SCHEDULED' || event.data.type === 'EMERGENCY',
        lastHeartbeat: event.timestamp
      }));
    });

    // Send periodic heartbeat
    const heartbeatInterval = setInterval(() => {
      if (webSocketService.isSocketConnected()) {
        webSocketService.sendHeartbeat();
        setSystemStatus(prev => ({
          ...prev,
          lastHeartbeat: new Date().toISOString()
        }));
      }
    }, 30000);

    return () => {
      unsubscribeConnection();
      unsubscribeMaintenance();
      clearInterval(heartbeatInterval);
    };
  }, []);

  return systemStatus;
}
