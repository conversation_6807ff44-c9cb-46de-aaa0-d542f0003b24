export interface UserProfile {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  dateOfBirth?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  profileImage?: string;
  isVerified: boolean;
  kycStatus: 'PENDING' | 'APPROVED' | 'REJECTED';
  balance: number;
  totalSavings: number;
  totalEarnings: number;
  isActive: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface UpdateUserProfileData {
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  dateOfBirth?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  profileImage?: string;
}

export interface UserBalance {
  userId: string;
  availableBalance: number;
  totalSavings: number;
  totalEarnings: number;
  pendingDeposits: number;
  pendingWithdrawals: number;
  lastUpdated: string;
}

export interface UserStats {
  totalUsers: number;
  activeUsers: number;
  newUsersThisMonth: number;
  totalSavings: number;
  totalEarnings: number;
  averageSavingsPerUser: number;
  kycPendingCount: number;
  kycApprovedCount: number;
  kycRejectedCount: number;
}

export interface AdminUserListItem {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  role: 'USER' | 'ADMIN';
  isVerified: boolean;
  kycStatus: 'PENDING' | 'APPROVED' | 'REJECTED';
  balance: number;
  totalSavings: number;
  isActive: boolean;
  lastLoginAt?: string;
  createdAt: string;
}

export interface UserSearchFilters {
  search?: string;
  role?: 'USER' | 'ADMIN';
  kycStatus?: 'PENDING' | 'APPROVED' | 'REJECTED';
  isActive?: boolean;
  dateFrom?: string;
  dateTo?: string;
  sortBy?: 'createdAt' | 'lastLoginAt' | 'balance' | 'totalSavings';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface PaginatedUserResponse {
  users: AdminUserListItem[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
