"use client";

import React, { useState } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
  fill?: boolean;
  sizes?: string;
  quality?: number;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down';
  rounded?: boolean;
  shadow?: boolean;
  hover?: boolean;
  loading?: 'lazy' | 'eager';
}

export function OptimizedImage({
  src,
  alt,
  width,
  height,
  className = '',
  priority = false,
  fill = false,
  sizes,
  quality = 75,
  placeholder = 'empty',
  blurDataURL,
  objectFit = 'cover',
  rounded = false,
  shadow = false,
  hover = false,
  loading = 'lazy',
  ...props
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const handleLoad = () => {
    setIsLoading(false);
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  const imageClasses = [
    className,
    rounded && 'rounded-lg',
    shadow && 'shadow-lg',
    hover && 'transition-transform duration-300 hover:scale-105',
    objectFit === 'cover' && 'object-cover',
    objectFit === 'contain' && 'object-contain',
    objectFit === 'fill' && 'object-fill',
    objectFit === 'none' && 'object-none',
    objectFit === 'scale-down' && 'object-scale-down',
  ].filter(Boolean).join(' ');

  if (hasError) {
    return (
      <div 
        className={`bg-gray-800 border border-gray-700 flex items-center justify-center ${imageClasses}`}
        style={{ width, height }}
      >
        <div className="text-center text-gray-400">
          <svg className="w-8 h-8 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
          </svg>
          <p className="text-xs">Image not found</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative">
      {isLoading && (
        <div 
          className={`absolute inset-0 bg-gray-800 animate-pulse ${rounded ? 'rounded-lg' : ''}`}
          style={{ width, height }}
        >
          <div className="flex items-center justify-center h-full">
            <div className="w-8 h-8 border-2 border-green-400 border-t-transparent rounded-full animate-spin"></div>
          </div>
        </div>
      )}
      
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: isLoading ? 0 : 1 }}
        transition={{ duration: 0.3 }}
      >
        <Image
          src={src}
          alt={alt}
          width={fill ? undefined : width}
          height={fill ? undefined : height}
          fill={fill}
          priority={priority}
          quality={quality}
          sizes={sizes}
          placeholder={placeholder}
          blurDataURL={blurDataURL}
          className={imageClasses}
          onLoad={handleLoad}
          onError={handleError}
          loading={loading}
          {...props}
        />
      </motion.div>
    </div>
  );
}

// Predefined image configurations for common use cases
export const ImageConfigs = {
  hero: {
    width: 1200,
    height: 675,
    quality: 85,
    priority: true,
    sizes: '(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 1200px'
  },
  
  testimonial: {
    width: 64,
    height: 64,
    quality: 80,
    rounded: true,
    objectFit: 'cover' as const
  },
  
  feature: {
    width: 400,
    height: 300,
    quality: 75,
    rounded: true,
    shadow: true,
    hover: true
  },
  
  dashboard: {
    width: 128,
    height: 128,
    quality: 80,
    rounded: true,
    shadow: true
  },
  
  card: {
    width: 300,
    height: 200,
    quality: 75,
    rounded: true,
    objectFit: 'cover' as const
  }
};

// Hero Image Component
export function HeroImage({ src, alt, className = '' }: { src: string; alt: string; className?: string }) {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      {...ImageConfigs.hero}
      className={`w-full h-auto ${className}`}
    />
  );
}

// Testimonial Avatar Component
export function TestimonialAvatar({ src, alt, className = '' }: { src: string; alt: string; className?: string }) {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      {...ImageConfigs.testimonial}
      className={`w-16 h-16 ${className}`}
    />
  );
}

// Feature Image Component
export function FeatureImage({ src, alt, className = '' }: { src: string; alt: string; className?: string }) {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      {...ImageConfigs.feature}
      className={`w-full h-auto ${className}`}
    />
  );
}

// Dashboard Image Component
export function DashboardImage({ src, alt, className = '' }: { src: string; alt: string; className?: string }) {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      {...ImageConfigs.dashboard}
      className={`w-32 h-32 ${className}`}
    />
  );
}

// Card Image Component
export function CardImage({ src, alt, className = '' }: { src: string; alt: string; className?: string }) {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      {...ImageConfigs.card}
      className={`w-full h-auto ${className}`}
    />
  );
}

// Background Image Component
export function BackgroundImage({ 
  src, 
  alt, 
  children, 
  className = '',
  overlay = true 
}: { 
  src: string; 
  alt: string; 
  children?: React.ReactNode;
  className?: string;
  overlay?: boolean;
}) {
  return (
    <div className={`relative overflow-hidden ${className}`}>
      <OptimizedImage
        src={src}
        alt={alt}
        fill
        objectFit="cover"
        quality={60}
        sizes="100vw"
        className="absolute inset-0"
      />
      {overlay && (
        <div className="absolute inset-0 bg-black/50"></div>
      )}
      {children && (
        <div className="relative z-10">
          {children}
        </div>
      )}
    </div>
  );
}

export default OptimizedImage;
