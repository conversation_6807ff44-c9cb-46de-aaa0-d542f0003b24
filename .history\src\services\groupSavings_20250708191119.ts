import { 
  GroupSavings, 
  GroupMember,
  GroupContribution,
  PayoutSchedule,
  CreateGroupSavingsData, 
  JoinGroupData,
  InviteGroupMemberData,
  GroupContributionData,
  GroupSearchFilters,
  GroupStats,
  ApiResponse
} from '../types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

class GroupSavingsService {
  private getAuthHeaders() {
    const token = localStorage.getItem('auth_token');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` })
    };
  }

  // Group Management
  async getAllGroups(filters?: GroupSearchFilters): Promise<{ groups: GroupSavings[]; total: number; page: number; limit: number; totalPages: number }> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/group-savings?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch group savings');
    }

    return response.json();
  }

  async getUserGroups(): Promise<GroupSavings[]> {
    // Use /api/group-savings/group-plans/my (returns array of group plans)
    const response = await fetch(`${API_BASE_URL}/group-savings/group-plans/my`, {
      headers: this.getAuthHeaders()
    });
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch user groups');
    }
    return response.json();
  }

  async getGroupById(groupId: string): Promise<GroupSavings> {
    const response = await fetch(`${API_BASE_URL}/group-savings/${groupId}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch group details');
    }

    return response.json();
  }

  async createGroup(data: CreateGroupSavingsData): Promise<GroupSavings> {
    const response = await fetch(`${API_BASE_URL}/group-savings/create`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to create group');
    }

    return response.json();
  }

  async updateGroup(groupId: string, data: Partial<CreateGroupSavingsData>): Promise<GroupSavings> {
    const response = await fetch(`${API_BASE_URL}/group-savings/${groupId}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update group');
    }

    return response.json();
  }

  async deleteGroup(groupId: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/group-savings/${groupId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to delete group');
    }
  }

  // Group Membership
  async joinGroup(data: JoinGroupData): Promise<{ group: GroupSavings; member: GroupMember }> {
    const response = await fetch(`${API_BASE_URL}/group-savings/join`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to join group');
    }

    return response.json();
  }

  async leaveGroup(groupId: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/group-savings/${groupId}/leave`, {
      method: 'POST',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to leave group');
    }
  }

  async inviteMember(data: InviteGroupMemberData): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/group-savings/invite`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to send invitation');
    }
  }

  async removeMember(groupId: string, memberId: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/group-savings/${groupId}/members/${memberId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to remove member');
    }
  }

  async updateMemberRole(groupId: string, memberId: string, role: 'ADMIN' | 'MEMBER'): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/group-savings/${groupId}/members/${memberId}/role`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ role })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update member role');
    }
  }

  // Group Contributions
  async makeContribution(data: GroupContributionData): Promise<GroupContribution> {
    const response = await fetch(`${API_BASE_URL}/group-savings/contribute`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to make contribution');
    }

    return response.json();
  }

  async getGroupContributions(groupId: string, filters?: {
    userId?: string;
    status?: string;
    dateFrom?: string;
    dateTo?: string;
    page?: number;
    limit?: number;
  }): Promise<{ contributions: GroupContribution[]; total: number; page: number; limit: number }> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/group-savings/${groupId}/contributions?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch contributions');
    }

    return response.json();
  }

  async getUserContributions(groupId: string): Promise<GroupContribution[]> {
    const response = await fetch(`${API_BASE_URL}/group-savings/${groupId}/contributions/user`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch user contributions');
    }

    return response.json();
  }

  // Payout Management
  async getPayoutSchedule(groupId: string): Promise<PayoutSchedule[]> {
    const response = await fetch(`${API_BASE_URL}/group-savings/${groupId}/payout-schedule`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch payout schedule');
    }

    return response.json();
  }

  async updatePayoutSchedule(groupId: string, schedule: Partial<PayoutSchedule>[]): Promise<PayoutSchedule[]> {
    const response = await fetch(`${API_BASE_URL}/group-savings/${groupId}/payout-schedule`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ schedule })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update payout schedule');
    }

    return response.json();
  }

  async processPayout(groupId: string, payoutId: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/group-savings/${groupId}/payout/${payoutId}/process`, {
      method: 'POST',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to process payout');
    }
  }

  // Group Statistics
  async getGroupStats(): Promise<GroupStats> {
    const response = await fetch(`${API_BASE_URL}/group-savings/stats`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch group statistics');
    }

    return response.json();
  }

  async getGroupAnalytics(groupId: string): Promise<any> {
    const response = await fetch(`${API_BASE_URL}/group-savings/${groupId}/analytics`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch group analytics');
    }

    return response.json();
  }

  // Utility Methods
  calculateGroupProgress(group: GroupSavings): number {
    return Math.min((group.currentAmount / group.targetAmount) * 100, 100);
  }

  calculateMemberContributionRate(member: GroupMember, totalContributions: number): number {
    if (totalContributions === 0) return 0;
    return (member.totalContributions / totalContributions) * 100;
  }

  getNextPayoutDate(schedule: PayoutSchedule[]): string | null {
    const upcomingPayouts = schedule
      .filter(p => p.status === 'PENDING')
      .sort((a, b) => new Date(a.scheduledDate).getTime() - new Date(b.scheduledDate).getTime());
    
    return upcomingPayouts.length > 0 ? upcomingPayouts[0].scheduledDate : null;
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN'
    }).format(amount);
  }

  generateInviteCode(): string {
    return Math.random().toString(36).substring(2, 8).toUpperCase();
  }
}

export const groupSavingsService = new GroupSavingsService();
