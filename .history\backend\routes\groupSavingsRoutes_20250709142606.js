const express = require('express');
const GroupSavingsPlan = require('../models/groupSavingsPlan');
const { authenticateToken } = require('../middleware/authMiddleware');
const router = express.Router();

// Create a new group savings plan
const { sendGroupInvite } = require('../utils/mailer');
// Original route
router.post('/group-plan', authenticateToken, async (req, res) => {
  const crypto = require('crypto');
  try {
    const { title, depositFrequency, depositAmount, targetDate, targetAmount, isPublic, pendingInvites } = req.body;
    const owner = req.user && req.user.id;
    const ownerEmail = req.user && req.user.email;
    if (!owner) {
      return res.status(401).json({ error: 'User authentication failed. Please log in again.' });
    }
    if (!title || !depositFrequency || !depositAmount || !targetDate || !targetAmount) {
      return res.status(400).json({ error: 'All fields are required.' });
    }
    if (typeof depositAmount !== 'number' || depositAmount <= 0) {
      return res.status(400).json({ error: 'Deposit amount must be a positive number.' });
    }
    if (typeof targetAmount !== 'number' || targetAmount <= 0) {
      return res.status(400).json({ error: 'Target amount must be a positive number.' });
    }
    if (isNaN(Date.parse(targetDate))) {
      return res.status(400).json({ error: 'Target date is invalid.' });
    }
    // Generate a unique invite code
    let inviteCode = crypto.randomBytes(6).toString('hex');
    // Ensure uniqueness (very unlikely to collide, but check anyway)
    while (await GroupSavingsPlan.findOne({ inviteCode })) {
      inviteCode = crypto.randomBytes(6).toString('hex');
    }
    // Set status to RECRUITING for new public groups, otherwise ACTIVE
    let status = !!isPublic ? 'RECRUITING' : 'ACTIVE';
    const groupPlan = new GroupSavingsPlan({
      title,
      depositFrequency,
      depositAmount,
      targetDate,
      targetAmount,
      owner,
      members: [owner],
      isPublic: !!isPublic,
      pendingInvites: Array.isArray(pendingInvites) ? pendingInvites : [],
      inviteCode,
      status,
    });
    const savedPlan = await groupPlan.save();

    // Send invite emails if there are pendingInvites
    if (Array.isArray(pendingInvites) && pendingInvites.length > 0) {
      const baseUrl = process.env.FRONTEND_URL;
      const inviteLink = `${baseUrl}/group-invite/${savedPlan.inviteCode}`;
      for (const email of pendingInvites) {
        try {
          await sendGroupInvite(email, title, inviteLink);
        } catch (err) {
          console.error(`Failed to send invite to ${email}:`, err);
        }
      }
    }

    res.status(201).json(savedPlan);
  } catch (error) {
    console.error('Error creating group savings plan:', error);
    res.status(500).json({ error: 'Failed to create group savings plan', details: error.message });
  }
});

// List all public group savings plans
router.get('/group-plans/public', async (req, res) => {
  try {
    const plans = await GroupSavingsPlan.find({ isPublic: true }).sort({ createdAt: -1 });
    res.status(200).json(plans);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch public group savings plans', details: error.message });
  }
});

// Join a public group savings plan
router.post('/group-plans/join/:id', authenticateToken, async (req, res) => {
  try {
    const userId = req.user && req.user.id;
    const { id } = req.params;
    const plan = await GroupSavingsPlan.findById(id);
    console.log('[JOIN PUBLIC GROUP] userId:', userId, 'groupId:', id);
    if (!plan) {
      console.log('[JOIN PUBLIC GROUP] Not found');
      return res.status(404).json({ error: 'Group savings plan not found.' });
    }
    console.log('[JOIN PUBLIC GROUP] isPublic:', plan.isPublic, 'members:', plan.members);
    if (!plan.isPublic) {
      console.log('[JOIN PUBLIC GROUP] Forbidden: not public');
      return res.status(403).json({ error: 'This group is not public.' });
    }
    if (plan.members.includes(userId)) {
      console.log('[JOIN PUBLIC GROUP] Already a member');
      return res.status(400).json({ error: 'Already a member.' });
    }
    plan.members.push(userId);
    // If group is recruiting and now full, set to FILLED
    if (
      plan.status === 'RECRUITING' &&
      typeof plan.maxMembers === 'number' &&
      plan.maxMembers > 0 &&
      plan.members.length >= plan.maxMembers
    ) {
      plan.status = 'FILLED';
    }
    await plan.save();
    console.log('[JOIN PUBLIC GROUP] Joined successfully');
    res.status(200).json({ message: 'Joined group successfully', plan });
  } catch (error) {
    console.error('[JOIN PUBLIC GROUP] Error:', error);
    res.status(500).json({ error: 'Failed to join group', details: error.message });
  }
});

// Accept invite to a private group (by inviteCode)
router.post('/group-plans/accept-invite/:inviteCode', authenticateToken, async (req, res) => {
  try {
    const userId = req.user && req.user.id;
    const userEmail = req.user && req.user.email;
    const { inviteCode } = req.params;
    const plan = await GroupSavingsPlan.findOne({ inviteCode });
    if (!plan) return res.status(404).json({ error: 'Group savings plan not found.' });
    // Optionally, you can skip the pendingInvites check to allow anyone with the link to join
    if (plan.members.some(m => m.toString() === userId)) return res.status(400).json({ error: 'Already a member.' });
    plan.members.push(userId);
    // If group is recruiting and now full, set to FILLED
    if (
      plan.status === 'RECRUITING' &&
      typeof plan.maxMembers === 'number' &&
      plan.maxMembers > 0 &&
      plan.members.length >= plan.maxMembers
    ) {
      plan.status = 'FILLED';
    }
    await plan.save();
    res.status(200).json({ message: 'Joined group successfully', plan });
  } catch (error) {
    res.status(500).json({ error: 'Failed to accept invite', details: error.message });
  }
});

// Get all group savings plans for the logged-in user (owner or member)
router.get('/group-plans/my', authenticateToken, async (req, res) => {
  const { getInterestRate } = require('../utils/interestRateUtil');
  const Transaction = require('../models/transaction');
  try {
    const userId = req.user && req.user.id;
    const plans = await GroupSavingsPlan.find({ $or: [ { owner: userId }, { members: userId } ] }).sort({ createdAt: -1 });
    // Add currentAmount, interestAccrued, and interestRate to each group plan
    for (let i = 0; i < plans.length; i++) {
      const plan = plans[i];
      const planObj = plan.toObject();
      planObj.currentAmount = planObj.savedAmount || 0;
      planObj.progress = planObj.targetAmount > 0 ? Math.round((planObj.currentAmount / planObj.targetAmount) * 100) : 0;
      // Calculate total interest accrued for this plan
      const interestTxs = await Transaction.find({ userId, type: 'interest', description: { $regex: plan._id.toString() } });
      planObj.interestAccrued = interestTxs.reduce((sum, tx) => sum + (tx.amount || 0), 0);
      // Add interest rate for this plan (group)
      planObj.interestRate = await getInterestRate('group');
      plans[i] = planObj;
    }
    res.status(200).json(plans);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch user group savings plans', details: error.message });
  }
});


// --- Invite by Email API ---
// POST /api/group-savings/group-plans/invite
router.post('/group-plans/invite', authenticateToken, async (req, res) => {
  try {
    const { groupId, email } = req.body;
    if (!groupId || !email) {
      return res.status(400).json({ error: 'Missing groupId or email.' });
    }
    const group = await GroupSavingsPlan.findById(groupId);
    if (!group) {
      return res.status(404).json({ error: 'Group not found.' });
    }
    // Only owner can invite
    if (group.owner.toString() !== req.user.id) {
      return res.status(403).json({ error: 'Only the group owner can send invites.' });
    }
    // Generate invite link using inviteCode
    const baseUrl = process.env.FRONTEND_URL || 'https://kojapay.com';
    const inviteLink = `${baseUrl}/join?inviteCode=${group.inviteCode}`;
    await sendGroupInvite(email, group.title || group.name || 'Group Savings', inviteLink);
    return res.status(200).json({ message: 'Invite sent successfully.' });
  } catch (error) {
    console.error('[INVITE BY EMAIL] Error:', error);
    return res.status(500).json({ error: 'Failed to send invite', details: error.message });
  }
});

module.exports = router;

// --- NEW: Get all group savings (regular + rotational) for the logged-in user ---
const RotationalGroupSavings = require('../models/rotationalGroupSavings');
router.get('/all-my', authenticateToken, async (req, res) => {
  try {
    const userId = req.user && req.user.id;
    // Regular group savings (owner or member)
    const groupPlans = await GroupSavingsPlan.find({ $or: [ { owner: userId }, { members: userId } ] }).sort({ createdAt: -1 });
    // Rotational group savings (createdBy or member)
    const rotationalGroups = await RotationalGroupSavings.find({
      $or: [
        { createdBy: userId },
        { 'members.userId': userId }
      ]
    }).sort({ createdAt: -1 });
    res.status(200).json({ groupPlans, rotationalGroups });
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch all user group savings', details: error.message });
  }
});
