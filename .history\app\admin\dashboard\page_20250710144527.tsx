"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  FiUsers, 
  FiDollarSign, 
  FiTrendingUp, 
  FiTrendingDown,
  FiActivity,
  FiCreditCard,
  FiShield,
  FiBell,
  FiAlertTriangle,
  FiCheckCircle,
  FiClock,
  FiBarChart,
  FiPieChart
} from 'react-icons/fi';
import AdminLayout from '../../../src/components/admin/AdminLayout';
import { adminService } from '../../../src/services';
import { AdminDashboardStats } from '../../../src/types';

import Button from '../../../src/components/ui/Button';
import Card from '../../../src/components/ui/Card';
import Badge from '../../../src/components/ui/Badge';

export default function AdminDashboardPage() {
  const [stats, setStats] = useState<AdminDashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeframe, setTimeframe] = useState('month');

  useEffect(() => {
    loadDashboardStats();
  }, [timeframe]);

  const loadDashboardStats = async () => {
    try {
      setLoading(true);
      const data = await adminService.getDashboardStats({ period: timeframe as any });
      setStats(data);
    } catch (error) {
      console.error('Failed to load dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN'
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  if (loading) {
    return (
      <AdminLayout title="Dashboard">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"></div>
        </div>
      </AdminLayout>
    );
  }

  if (!stats) {
    return (
      <AdminLayout title="Dashboard">
        <div className="text-center py-12">
          <p className="text-gray-400">Failed to load dashboard data</p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title="Dashboard">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white">Admin Dashboard</h1>
            <p className="text-gray-400 mt-2">Overview of platform performance and metrics</p>
          </div>
          <div className="flex space-x-2">
            {['week', 'month', 'year'].map((period) => (
              <Button
                key={period}
                variant={timeframe === period ? 'default' : 'outline'}
                onClick={() => setTimeframe(period)}
                size="sm"
              >
                {period.charAt(0).toUpperCase() + period.slice(1)}
              </Button>
            ))}
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Users</p>
                <p className="text-2xl font-bold text-white">{stats.users.total.toLocaleString()}</p>
                <p className="text-green-400 text-sm">
                  +{stats.users.newThisMonth} this month
                </p>
              </div>
              <FiUsers className="text-blue-500 text-3xl" />
            </div>
          </Card>

          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Savings</p>
                <p className="text-2xl font-bold text-white">
                  {formatCurrency(stats.savings.totalSavingsAmount)}
                </p>
                <p className="text-green-400 text-sm">
                  {stats.savings.activePlans} active plans
                </p>
              </div>
              <FiDollarSign className="text-green-500 text-3xl" />
            </div>
          </Card>

          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Transaction Volume</p>
                <p className="text-2xl font-bold text-white">
                  {formatCurrency(stats.transactions.totalVolume)}
                </p>
                <p className="text-green-400 text-sm">
                  {formatPercentage(stats.transactions.successRate)} success rate
                </p>
              </div>
              <FiActivity className="text-purple-500 text-3xl" />
            </div>
          </Card>

          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Pending Withdrawals</p>
                <p className="text-2xl font-bold text-white">
                  {stats.withdrawals.pendingWithdrawals}
                </p>
                <p className="text-yellow-400 text-sm">
                  {formatCurrency(stats.withdrawals.pendingAmount)}
                </p>
              </div>
              <FiCreditCard className="text-yellow-500 text-3xl" />
            </div>
          </Card>
        </div>

        {/* Charts Row */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* User Growth Chart */}
          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">User Growth</h3>
              <FiTrendingUp className="text-green-500" />
            </div>
            <div className="h-64">
              {/* Placeholder for chart - would integrate with actual chart library */}
              <div className="w-full h-full bg-gray-700 rounded-lg flex items-center justify-center">
                <p className="text-gray-400">User Growth Chart</p>
              </div>
            </div>
          </Card>

          {/* Transaction Volume Chart */}
          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">Transaction Volume</h3>
              <FiBarChart className="text-blue-500" />
            </div>
            <div className="h-64">
              <div className="w-full h-full bg-gray-700 rounded-lg flex items-center justify-center">
                <p className="text-gray-400">Transaction Volume Chart</p>
              </div>
            </div>
          </Card>
        </div>

        {/* Detailed Stats */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* User Stats */}
          <Card className="bg-gray-800 border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <FiUsers className="mr-2" />
              User Statistics
            </h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Active Users</span>
                <span className="text-white font-semibold">{stats.users.active.toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">KYC Approved</span>
                <span className="text-green-400 font-semibold">{stats.users.kycApproved.toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">KYC Pending</span>
                <span className="text-yellow-400 font-semibold">{stats.users.kycPending.toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">KYC Rejected</span>
                <span className="text-red-400 font-semibold">{stats.users.kycRejected.toLocaleString()}</span>
              </div>
            </div>
          </Card>

          {/* Savings Stats */}
          <Card className="bg-gray-800 border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <FiDollarSign className="mr-2" />
              Savings Overview
            </h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Total Plans</span>
                <span className="text-white font-semibold">{stats.savings.totalPlans.toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Active Plans</span>
                <span className="text-green-400 font-semibold">{stats.savings.activePlans.toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Completed Plans</span>
                <span className="text-blue-400 font-semibold">{stats.savings.completedPlans.toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Interest Paid</span>
                <span className="text-purple-400 font-semibold">{formatCurrency(stats.savings.totalInterestPaid)}</span>
              </div>
            </div>
          </Card>

          {/* System Health */}
          <Card className="bg-gray-800 border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <FiShield className="mr-2" />
              System Health
            </h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Deposit Success Rate</span>
                <Badge variant="success">{formatPercentage(stats.deposits.successRate)}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Transaction Success Rate</span>
                <Badge variant="success">{formatPercentage(stats.transactions.successRate)}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Avg Processing Time</span>
                <span className="text-white font-semibold">{stats.withdrawals.averageProcessingTime}h</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Notification Delivery</span>
                <Badge variant="success">{formatPercentage(stats.notifications.deliveryRate)}</Badge>
              </div>
            </div>
          </Card>
        </div>

        {/* Recent Activity */}
        <Card className="bg-gray-800 border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <FiActivity className="mr-2" />
            Recent Activity
          </h3>
          <div className="space-y-3">
            {/* Placeholder for recent activity items */}
            {[
              { type: 'user', message: 'New user registration: <EMAIL>', time: '2 minutes ago', icon: FiUsers, color: 'text-blue-500' },
              { type: 'withdrawal', message: 'Withdrawal request pending approval: ₦50,000', time: '5 minutes ago', icon: FiCreditCard, color: 'text-yellow-500' },
              { type: 'kyc', message: 'KYC document submitted for review', time: '10 minutes ago', icon: FiShield, color: 'text-green-500' },
              { type: 'system', message: 'System backup completed successfully', time: '15 minutes ago', icon: FiCheckCircle, color: 'text-green-500' },
              { type: 'alert', message: 'High transaction volume detected', time: '20 minutes ago', icon: FiAlertTriangle, color: 'text-orange-500' }
            ].map((activity, index) => (
              <div key={index} className="flex items-center space-x-3 p-3 bg-gray-700 rounded-lg">
                <activity.icon className={`${activity.color} text-lg`} />
                <div className="flex-1">
                  <p className="text-white text-sm">{activity.message}</p>
                  <p className="text-gray-400 text-xs">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Quick Actions */}
        <Card className="bg-gray-800 border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Quick Actions</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button className="bg-blue-600 hover:bg-blue-700 h-16 flex flex-col items-center justify-center">
              <FiUsers className="mb-1" />
              <span className="text-sm">Manage Users</span>
            </Button>
            <Button className="bg-green-600 hover:bg-green-700 h-16 flex flex-col items-center justify-center">
              <FiCreditCard className="mb-1" />
              <span className="text-sm">Review Withdrawals</span>
            </Button>
            <Button className="bg-purple-600 hover:bg-purple-700 h-16 flex flex-col items-center justify-center">
              <FiShield className="mb-1" />
              <span className="text-sm">KYC Reviews</span>
            </Button>
            <Button className="bg-orange-600 hover:bg-orange-700 h-16 flex flex-col items-center justify-center">
              <FiBell className="mb-1" />
              <span className="text-sm">Send Notification</span>
            </Button>
          </div>
        </Card>
      </div>
    </AdminLayout>
  );
}
