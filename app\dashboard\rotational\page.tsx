"use client";

import { motion } from 'framer-motion';
import React, { useEffect, useState } from 'react';
import {
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>Clock,
    FiDollarSign,
    FiEye,
    FiPlus,
    FiTrendingUp,
    FiUserPlus,
    FiUsers,
    FiX
} from 'react-icons/fi';
import DashboardLayout from '../../../src/components/dashboard/DashboardLayout';
import { OutlineButton, PrimaryButton } from '../../../src/components/ui/AnimatedButton';
import { Card3D } from '../../../src/components/ui/Card3D';
import { showToast } from '../../../src/components/ui/Toast';

interface RotationalGroup {
  _id: string;
  name: string;
  description: string;
  amountPerInterval: number;
  intervalType: 'daily' | 'weekly' | 'monthly' | 'yearly';
  members: Array<{
    userId: {
      _id: string;
      name: string;
      email: string;
    };
    hasPaid: boolean;
    hasReceivedPayout: boolean;
    joinedAt: string;
  }>;
  currentCycle: number;
  nextPayoutDate: string;
  createdBy: {
    _id: string;
    name: string;
    email: string;
  };
  createdAt: string;
  isActive: boolean;
  payouts: Array<{
    userId: string;
    amount: number;
    date: string;
    cycle: number;
  }>;
}

interface CreateGroupForm {
  name: string;
  description: string;
  amountPerInterval: number;
  intervalType: 'daily' | 'weekly' | 'monthly' | 'yearly';
  nextPayoutDate: string;
}

export default function RotationalSavingsPage() {
  const [groups, setGroups] = useState<RotationalGroup[]>([]);
  const [allGroups, setAllGroups] = useState<RotationalGroup[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [activeTab, setActiveTab] = useState<'my-groups' | 'all-groups'>('my-groups');
  const [formData, setFormData] = useState<CreateGroupForm>({
    name: '',
    description: '',
    amountPerInterval: 0,
    intervalType: 'monthly',
    nextPayoutDate: '',
  });

  // Mock user ID - in real app, get from auth context
  const currentUserId = '507f1f77bcf86cd799439011';

  useEffect(() => {
    fetchMyGroups();
    fetchAllGroups();
  }, []);

  const fetchMyGroups = async () => {
    try {
      setLoading(true);
      const data = await rotationalSavingsService.getMyGroups();
      setGroups(data);
    } catch (error) {
      console.error('Error fetching groups:', error);
      // Fallback to mock data for development
      if (true) { // Enable mock data for development
        setGroups([
          {
            _id: '1',
            name: 'Monthly Savers Circle',
            description: 'A group for monthly savings with friends',
            amountPerInterval: 50000,
            intervalType: 'monthly',
            members: [
              {
                userId: { _id: '1', name: 'John Doe', email: '<EMAIL>' },
                hasPaid: true,
                hasReceivedPayout: false,
                joinedAt: '2024-01-01T00:00:00Z'
              },
              {
                userId: { _id: '2', name: 'Jane Smith', email: '<EMAIL>' },
                hasPaid: false,
                hasReceivedPayout: false,
                joinedAt: '2024-01-02T00:00:00Z'
              }
            ],
            currentCycle: 1,
            nextPayoutDate: '2024-02-01T00:00:00Z',
            createdBy: { _id: '1', name: 'John Doe', email: '<EMAIL>' },
            createdAt: '2024-01-01T00:00:00Z',
            isActive: true,
            payouts: []
          }
        ]);
      }
      showToast.error('Failed to fetch your groups');
    } finally {
      setLoading(false);
    }
  };

  const fetchAllGroups = async () => {
    try {
      const data = await rotationalSavingsService.getAllGroups();
      setAllGroups(data);
    } catch (error) {
      console.error('Error fetching all groups:', error);
      // Mock data for development
      setAllGroups([
        {
          _id: '2',
          name: 'Weekly Hustlers',
          description: 'Fast-paced weekly savings for entrepreneurs',
          amountPerInterval: 10000,
          intervalType: 'weekly',
          members: [
            {
              userId: { _id: '3', name: 'Mike Johnson', email: '<EMAIL>' },
              hasPaid: true,
              hasReceivedPayout: false,
              joinedAt: '2024-01-01T00:00:00Z'
            }
          ],
          currentCycle: 1,
          nextPayoutDate: '2024-01-15T00:00:00Z',
          createdBy: { _id: '3', name: 'Mike Johnson', email: '<EMAIL>' },
          createdAt: '2024-01-01T00:00:00Z',
          isActive: true,
          payouts: []
        }
      ]);
    }
  };

  const handleCreateGroup = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const newGroup = await rotationalSavingsService.createGroup({
        ...formData,
        createdBy: currentUserId,
      });

      setGroups(prev => [newGroup, ...prev]);
      setShowCreateForm(false);
      setFormData({
        name: '',
        description: '',
        amountPerInterval: 0,
        intervalType: 'monthly',
        nextPayoutDate: '',
      });
      showToast.success('Rotational group created successfully!');
    } catch (error) {
      console.error('Error creating group:', error);
      showToast.error('Failed to create group');
    }
  };

  const handleJoinGroup = async (groupId: string) => {
    try {
      await rotationalSavingsService.joinGroup(groupId, currentUserId);
      showToast.success('Successfully joined the group!');
      fetchMyGroups();
      fetchAllGroups();
    } catch (error) {
      console.error('Error joining group:', error);
      showToast.error('Failed to join group');
    }
  };

  const handleMakePayment = async (groupId: string) => {
    try {
      await rotationalSavingsService.makePayment(groupId, currentUserId);
      showToast.success('Payment recorded successfully!');
      fetchMyGroups();
    } catch (error) {
      console.error('Error making payment:', error);
      showToast.error('Failed to record payment');
    }
  };

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-NG', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getIntervalText = (intervalType: string) => {
    const intervals = {
      daily: 'Daily',
      weekly: 'Weekly',
      monthly: 'Monthly',
      yearly: 'Yearly'
    };
    return intervals[intervalType as keyof typeof intervals] || intervalType;
  };

  const isUserInGroup = (group: RotationalGroup) => {
    return group.members.some(member => member.userId._id === currentUserId);
  };

  const getUserPaymentStatus = (group: RotationalGroup) => {
    const userMember = group.members.find(member => member.userId._id === currentUserId);
    return userMember?.hasPaid || false;
  };

  return (
    <DashboardLayout title="Rotational Savings">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white">Rotational Savings</h1>
            <p className="text-gray-400 mt-2">Join rotating savings groups and grow your wealth together</p>
          </div>
          <PrimaryButton onClick={() => setShowCreateForm(true)}>
            <FiPlus className="mr-2" />
            Create Group
          </PrimaryButton>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card3D className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">My Groups</p>
                <p className="text-2xl font-bold text-white">{groups.length}</p>
              </div>
              <FiUsers className="text-green-500 text-3xl" />
            </div>
          </Card3D>

          <Card3D className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Contributions</p>
                <p className="text-2xl font-bold text-white">
                  {formatAmount(groups.reduce((sum, group) => sum + group.amountPerInterval, 0))}
                </p>
              </div>
              <FiDollarSign className="text-blue-500 text-3xl" />
            </div>
          </Card3D>

          <Card3D className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Active Cycles</p>
                <p className="text-2xl font-bold text-white">
                  {groups.filter(group => group.isActive).length}
                </p>
              </div>
              <FiTrendingUp className="text-purple-500 text-3xl" />
            </div>
          </Card3D>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 bg-gray-800 p-1 rounded-lg">
          <button
            onClick={() => setActiveTab('my-groups')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'my-groups'
                ? 'bg-green-600 text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            My Groups
          </button>
          <button
            onClick={() => setActiveTab('all-groups')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'all-groups'
                ? 'bg-green-600 text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            Browse Groups
          </button>
        </div>

        {/* Content based on active tab */}
        {activeTab === 'my-groups' ? (
          <div className="space-y-4">
            {loading ? (
              <div className="flex justify-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"></div>
              </div>
            ) : groups.length === 0 ? (
              <Card3D className="bg-gray-800 border-gray-700 p-8 text-center">
                <FiUsers className="text-gray-500 text-4xl mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-400 mb-2">No Groups Yet</h3>
                <p className="text-gray-500 mb-4">
                  You haven't joined any rotational savings groups yet.
                </p>
                <PrimaryButton onClick={() => setShowCreateForm(true)}>
                  Create Your First Group
                </PrimaryButton>
              </Card3D>
            ) : (
              groups.map((group) => (
                <GroupCard
                  key={group._id}
                  group={group}
                  isUserGroup={true}
                  onMakePayment={handleMakePayment}
                  formatAmount={formatAmount}
                  formatDate={formatDate}
                  getIntervalText={getIntervalText}
                  getUserPaymentStatus={getUserPaymentStatus}
                  currentUserId={currentUserId}
                />
              ))
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {allGroups.filter(group => !isUserInGroup(group)).length === 0 ? (
              <Card3D className="bg-gray-800 border-gray-700 p-8 text-center">
                <FiUsers className="text-gray-500 text-4xl mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-400 mb-2">No Available Groups</h3>
                <p className="text-gray-500">
                  There are no groups available to join at the moment.
                </p>
              </Card3D>
            ) : (
              allGroups
                .filter(group => !isUserInGroup(group))
                .map((group) => (
                  <GroupCard
                    key={group._id}
                    group={group}
                    isUserGroup={false}
                    onJoinGroup={handleJoinGroup}
                    formatAmount={formatAmount}
                    formatDate={formatDate}
                    getIntervalText={getIntervalText}
                    currentUserId={currentUserId}
                  />
                ))
            )}
          </div>
        )}

        {/* Create Group Modal */}
        {showCreateForm && (
          <CreateGroupModal
            formData={formData}
            setFormData={setFormData}
            onSubmit={handleCreateGroup}
            onClose={() => setShowCreateForm(false)}
          />
        )}
      </div>
    </DashboardLayout>
  );
}

// GroupCard Component
interface GroupCardProps {
  group: RotationalGroup;
  isUserGroup: boolean;
  onMakePayment?: (groupId: string) => void;
  onJoinGroup?: (groupId: string) => void;
  formatAmount: (amount: number) => string;
  formatDate: (date: string) => string;
  getIntervalText: (interval: string) => string;
  getUserPaymentStatus?: (group: RotationalGroup) => boolean;
  currentUserId: string;
}

function GroupCard({
  group,
  isUserGroup,
  onMakePayment,
  onJoinGroup,
  formatAmount,
  formatDate,
  getIntervalText,
  getUserPaymentStatus,
  currentUserId
}: GroupCardProps) {
  const hasPaid = getUserPaymentStatus ? getUserPaymentStatus(group) : false;
  const isCreator = group.createdBy._id === currentUserId;
  const totalPayout = group.amountPerInterval * group.members.length;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="w-full"
    >
      <Card3D className="bg-gray-800 border-gray-700 p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div className="flex-1">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-xl font-semibold text-white">{group.name}</h3>
                <p className="text-gray-400 text-sm">{group.description}</p>
              </div>
              {isCreator && (
                <span className="bg-green-600 text-white text-xs px-2 py-1 rounded-full">
                  Creator
                </span>
              )}
            </div>

            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
              <div>
                <p className="text-gray-400 text-xs">Contribution</p>
                <p className="text-white font-semibold">{formatAmount(group.amountPerInterval)}</p>
                <p className="text-gray-500 text-xs">{getIntervalText(group.intervalType)}</p>
              </div>
              <div>
                <p className="text-gray-400 text-xs">Members</p>
                <p className="text-white font-semibold">{group.members.length}</p>
                <p className="text-gray-500 text-xs">Active</p>
              </div>
              <div>
                <p className="text-gray-400 text-xs">Total Payout</p>
                <p className="text-white font-semibold">{formatAmount(totalPayout)}</p>
                <p className="text-gray-500 text-xs">Per cycle</p>
              </div>
              <div>
                <p className="text-gray-400 text-xs">Next Payout</p>
                <p className="text-white font-semibold">{formatDate(group.nextPayoutDate)}</p>
                <p className="text-gray-500 text-xs">Cycle {group.currentCycle}</p>
              </div>
            </div>

            {/* Members List */}
            <div className="mb-4">
              <p className="text-gray-400 text-sm mb-2">Members:</p>
              <div className="flex flex-wrap gap-2">
                {group.members.map((member, index) => (
                  <div
                    key={member.userId._id}
                    className={`flex items-center space-x-2 px-3 py-1 rounded-full text-xs ${
                      member.hasPaid
                        ? 'bg-green-600/20 text-green-400'
                        : 'bg-gray-700 text-gray-400'
                    }`}
                  >
                    <span>{member.userId.name}</span>
                    {member.hasPaid ? <FiCheck size={12} /> : <FiClock size={12} />}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-col space-y-2 lg:ml-6">
            {isUserGroup ? (
              <>
                {hasPaid ? (
                  <div className="flex items-center text-green-400 text-sm">
                    <FiCheck className="mr-2" />
                    Payment Complete
                  </div>
                ) : (
                  <PrimaryButton
                    onClick={() => onMakePayment?.(group._id)}
                    className="whitespace-nowrap"
                  >
                    <FiDollarSign className="mr-2" />
                    Make Payment
                  </PrimaryButton>
                )}
                <OutlineButton className="whitespace-nowrap">
                  <FiEye className="mr-2" />
                  View Details
                </OutlineButton>
              </>
            ) : (
              <PrimaryButton
                onClick={() => onJoinGroup?.(group._id)}
                className="whitespace-nowrap"
              >
                <FiUserPlus className="mr-2" />
                Join Group
              </PrimaryButton>
            )}
          </div>
        </div>
      </Card3D>
    </motion.div>
  );
}

// CreateGroupModal Component
interface CreateGroupModalProps {
  formData: CreateGroupForm;
  setFormData: React.Dispatch<React.SetStateAction<CreateGroupForm>>;
  onSubmit: (e: React.FormEvent) => void;
  onClose: () => void;
}

function CreateGroupModal({ formData, setFormData, onSubmit, onClose }: CreateGroupModalProps) {
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'amountPerInterval' ? Number(value) : value
    }));
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-gray-900 rounded-2xl border border-gray-700 p-6 w-full max-w-md"
      >
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-white">Create Rotational Group</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <FiX size={24} />
          </button>
        </div>

        <form onSubmit={onSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Group Name
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:border-green-500 focus:outline-none text-white"
              placeholder="e.g., Monthly Savers Circle"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Description
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={3}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:border-green-500 focus:outline-none text-white"
              placeholder="Describe your group..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Amount Per Interval (₦)
            </label>
            <input
              type="number"
              name="amountPerInterval"
              value={formData.amountPerInterval}
              onChange={handleInputChange}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:border-green-500 focus:outline-none text-white"
              placeholder="50000"
              min="1000"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Interval Type
            </label>
            <select
              name="intervalType"
              value={formData.intervalType}
              onChange={handleInputChange}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:border-green-500 focus:outline-none text-white"
            >
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
              <option value="yearly">Yearly</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              First Payout Date
            </label>
            <input
              type="date"
              name="nextPayoutDate"
              value={formData.nextPayoutDate}
              onChange={handleInputChange}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:border-green-500 focus:outline-none text-white"
              required
            />
          </div>

          <div className="flex space-x-3 pt-4">
            <PrimaryButton type="submit" className="flex-1">
              Create Group
            </PrimaryButton>
            <OutlineButton onClick={onClose} className="flex-1">
              Cancel
            </OutlineButton>
          </div>
        </form>
      </motion.div>
    </div>
  );
}
