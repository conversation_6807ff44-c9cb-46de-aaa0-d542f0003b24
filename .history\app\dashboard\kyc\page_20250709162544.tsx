"use client";


import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../src/hooks/use-auth';
import { useRouter } from 'next/navigation';
import DashboardLayout from '../../../src/components/dashboard/DashboardLayout';
import { showToast } from '../../../src/components/ui/Toast';



interface MinimalKYCData {
  fullName: string;
  phoneNumber: string;
  ninOrBvn: string;
}

const initialKYCData: MinimalKYCData = {
  fullName: '',
  phoneNumber: '',
  ninOrBvn: '',
};

export default function KYCPage() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const [kycData, setKycData] = useState<MinimalKYCData>(initialKYCData);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login');
      return;
    }
    if (!isLoading && user?.role === 'ADMIN') {
      router.push('/admin/dashboard');
      return;
    }
    // Pre-fill user data if available
    if (user) {
      setKycData(prev => ({
        ...prev,
        fullName: user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : prev.fullName,
        phoneNumber: user.phoneNumber || prev.phoneNumber,
      }));
    }
  }, [isAuthenticated, isLoading, user, router]);

  const handleInputChange = (field: keyof MinimalKYCData, value: string) => {
    setKycData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!kycData.fullName || !kycData.phoneNumber || !kycData.ninOrBvn) {
      showToast.error('Please fill in all required fields');
      return;
    }
    setLoading(true);
    try {
      const res = await fetch('/api/kyc/verify-nin', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(kycData),
      });
      if (!res.ok) throw new Error('Verification failed');
      showToast.success('KYC verification submitted successfully!');
      router.push('/dashboard');
    } catch (error) {
      showToast.error('Failed to submit KYC verification');
    } finally {
      setLoading(false);
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center" data-aos="fade-in">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto mb-4"></div>
            <p className="text-gray-400">Loading KYC verification...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!isAuthenticated || user?.role === 'ADMIN') {
    return null;
  }

  // Check if user is already verified

  if (user?.kycStatus === 'APPROVED') {
    return (
      <DashboardLayout title="KYC Verification">
        <div className="max-w-2xl mx-auto text-center py-12">
          <div className="w-20 h-20 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-green-400 text-4xl">✓</span>
          </div>
          <h2 className="text-2xl font-bold text-white mb-4">KYC Verification Complete</h2>
          <p className="text-gray-300 mb-6">
            Your identity has been successfully verified. You now have access to all platform features.
          </p>
          <button
            onClick={() => router.push('/dashboard')}
            className="px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
          >
            Return to Dashboard
          </button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title="KYC Verification">
      <div className="max-w-xl mx-auto mt-12 bg-gray-900/50 border border-gray-800 rounded-lg p-8">
        <h1 className="text-2xl font-bold text-white mb-6 text-center">Identity Verification</h1>
        <p className="text-gray-400 mb-8 text-center">
          Please complete this short form to verify your identity. Your information is secure and encrypted.
        </p>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label className="block text-gray-300 mb-2">Full Name</label>
            <input
              type="text"
              className="w-full px-4 py-2 rounded-lg bg-gray-800 text-white border border-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500"
              value={kycData.fullName}
              onChange={e => handleInputChange('fullName', e.target.value)}
              required
            />
          </div>
          <div>
            <label className="block text-gray-300 mb-2">Phone Number</label>
            <input
              type="tel"
              className="w-full px-4 py-2 rounded-lg bg-gray-800 text-white border border-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500"
              value={kycData.phoneNumber}
              onChange={e => handleInputChange('phoneNumber', e.target.value)}
              required
            />
          </div>
          <div>
            <label className="block text-gray-300 mb-2">NIN or BVN</label>
            <input
              type="text"
              className="w-full px-4 py-2 rounded-lg bg-gray-800 text-white border border-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500"
              value={kycData.ninOrBvn}
              onChange={e => handleInputChange('ninOrBvn', e.target.value)}
              required
            />
            <p className="text-gray-400 text-xs mt-1">Enter your National Identification Number (NIN) or Bank Verification Number (BVN).</p>
          </div>
          <button
            type="submit"
            disabled={loading}
            className="w-full py-3 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg transition-colors font-semibold mt-4"
          >
            {loading ? 'Submitting...' : 'Submit for Verification'}
          </button>
        </form>
      </div>
    </DashboardLayout>
  );
}
