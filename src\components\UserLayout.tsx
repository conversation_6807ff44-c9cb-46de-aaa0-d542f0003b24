"use client";

import React from 'react';
import { useAuth } from '../hooks/use-auth';
import UserNavigation from './UserNavigation';
import { PrivateRoute } from './auth/PrivateRoute';

interface UserLayoutProps {
  children: React.ReactNode;
}

export default function UserLayout({ children }: UserLayoutProps) {
  return (
    <PrivateRoute requiredRole="USER">
      <div className="min-h-screen bg-black text-white">
        <UserNavigation />
        
        {/* Main Content */}
        <div className="lg:pl-64">
          <main className="flex-1">
            {children}
          </main>
        </div>
      </div>
    </PrivateRoute>
  );
}
