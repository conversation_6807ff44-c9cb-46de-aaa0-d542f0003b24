"use client";

import React from 'react';
import { motion } from 'framer-motion';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
  onClick?: () => void;
  variant?: 'default' | 'gradient' | 'glass';
}

export function Card({ 
  children, 
  className = '', 
  hover = false,
  onClick,
  variant = 'default'
}: CardProps) {
  const baseClasses = `
    rounded-xl border transition-all duration-300
    ${hover ? 'hover:shadow-lg hover:scale-[1.02] cursor-pointer' : ''}
    ${onClick ? 'cursor-pointer' : ''}
  `;

  const variantClasses = {
    default: 'bg-gray-800 border-gray-700',
    gradient: 'bg-gradient-to-br from-gray-800 to-gray-900 border-gray-700',
    glass: 'bg-gray-800/50 backdrop-blur-lg border-gray-700/50'
  };

  const cardClasses = `${baseClasses} ${variantClasses[variant]} ${className}`;

  if (hover || onClick) {
    return (
      <motion.div
        className={cardClasses}
        onClick={onClick}
        whileHover={hover ? { scale: 1.02 } : undefined}
        whileTap={onClick ? { scale: 0.98 } : undefined}
        transition={{ type: "spring", stiffness: 400, damping: 10 }}
      >
        {children}
      </motion.div>
    );
  }

  return (
    <div className={cardClasses}>
      {children}
    </div>
  );
}

export default Card;
