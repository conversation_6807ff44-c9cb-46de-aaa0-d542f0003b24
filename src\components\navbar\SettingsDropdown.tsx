"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { IconButton } from '../ui/Button';
import { ThemeToggleButton } from '../ui/ThemedButton';
import { 
  FiSettings, 
  FiUser, 
  FiShield, 
  FiBell, 
  FiHelpCircle, 
  FiLogOut,
  FiChevronRight,
  FiMoon,
  FiSun
} from 'react-icons/fi';
import { useTheme } from '../../contexts/ThemeContext';
import { useAuth } from '../../hooks/use-auth';
import { useRouter } from 'next/navigation';

interface SettingsItem {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  action?: () => void;
  href?: string;
  badge?: string;
  divider?: boolean;
}

export default function SettingsDropdown() {
  const [isOpen, setIsOpen] = useState(false);
  const { theme, toggleTheme } = useTheme();
  const { logout } = useAuth();
  const router = useRouter();

  const handleLogout = async () => {
    try {
      await logout();
      router.push('/');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const settingsItems: SettingsItem[] = [
    {
      id: 'profile',
      label: 'Profile Settings',
      icon: FiUser,
      href: '/dashboard/profile'
    },
    {
      id: 'security',
      label: 'Security & Privacy',
      icon: FiShield,
      href: '/dashboard/security'
    },
    {
      id: 'notifications',
      label: 'Notification Preferences',
      icon: FiBell,
      href: '/dashboard/notifications'
    },
    {
      id: 'theme',
      label: `Switch to ${theme === 'light' ? 'Dark' : 'Light'} Mode`,
      icon: theme === 'light' ? FiMoon : FiSun,
      action: toggleTheme
    },
    {
      id: 'help',
      label: 'Help & Support',
      icon: FiHelpCircle,
      href: '/dashboard/help',
      divider: true
    },
    {
      id: 'logout',
      label: 'Sign Out',
      icon: FiLogOut,
      action: handleLogout
    }
  ];

  const handleItemClick = (item: SettingsItem) => {
    if (item.action) {
      item.action();
    } else if (item.href) {
      router.push(item.href);
    }
    setIsOpen(false);
  };

  return (
    <div className="relative">
      <IconButton
        icon={FiSettings}
        onClick={() => setIsOpen(!isOpen)}
        tooltip="Settings"
      />

      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <div
              className="fixed inset-0 z-40"
              onClick={() => setIsOpen(false)}
            />
            
            {/* Dropdown */}
            <motion.div
              initial={{ opacity: 0, y: 10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: 10, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className="absolute right-0 top-full mt-2 w-64 bg-theme border border-theme rounded-xl shadow-2xl z-50 overflow-hidden"
              style={{
                boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(34, 197, 94, 0.1)'
              }}
            >
              {/* Header */}
              <div className="p-4 border-b border-theme bg-theme-secondary">
                <h3 className="font-inter font-semibold text-theme flex items-center">
                  <FiSettings className="w-4 h-4 mr-2 text-brand" />
                  Settings
                </h3>
              </div>

              {/* Settings List */}
              <div className="py-2">
                {settingsItems.map((item, index) => (
                  <React.Fragment key={item.id}>
                    {item.divider && index > 0 && (
                      <div className="my-2 border-t border-theme" />
                    )}
                    <motion.button
                      onClick={() => handleItemClick(item)}
                      className="w-full px-4 py-3 flex items-center justify-between hover:bg-theme-secondary transition-colors group"
                      whileHover={{ x: 4 }}
                      transition={{ duration: 0.2 }}
                    >
                      <div className="flex items-center space-x-3">
                        <div className="p-1.5 rounded-lg bg-brand/10 group-hover:bg-brand/20 transition-colors">
                          <item.icon className="w-4 h-4 text-brand" />
                        </div>
                        <span className="font-inter text-sm text-theme group-hover:text-brand transition-colors">
                          {item.label}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        {item.badge && (
                          <span className="px-2 py-1 text-xs bg-brand text-white rounded-full">
                            {item.badge}
                          </span>
                        )}
                        {item.id === 'theme' ? (
                          <ThemeToggleButton variant="switch" className="scale-75" />
                        ) : (
                          <FiChevronRight className="w-4 h-4 text-theme-secondary group-hover:text-brand transition-colors" />
                        )}
                      </div>
                    </motion.button>
                  </React.Fragment>
                ))}
              </div>

              {/* Footer */}
              <div className="p-3 border-t border-theme bg-theme-secondary">
                <div className="text-center">
                  <p className="text-xs text-theme-secondary font-inter">
                    BetterInterest v1.0.0
                  </p>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
}
