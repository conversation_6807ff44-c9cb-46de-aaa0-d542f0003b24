import {
  WithdrawalAccount,
  AutomaticWithdrawalRule,
  AutomaticWithdrawalRequest,
  CreateWithdrawalAccountRequest,
  CreateAutomaticRuleRequest,
  InstantWithdrawalRequest,
  WithdrawalDashboardData,
  ValidationResult,
  WithdrawalError,
  ProcessingResult
} from '../types/automaticWithdrawal';
import { paystackService } from './paystackService';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

class AutomaticWithdrawalService {
  private getAuthHeaders() {
    const token = localStorage.getItem('auth_token');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` })
    };
  }

  private async makeRequest<T>(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    data?: any
  ): Promise<T> {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method,
      headers: this.getAuthHeaders(),
      ...(data && { body: JSON.stringify(data) })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }

  // Withdrawal Account Management
  async getWithdrawalAccounts(): Promise<WithdrawalAccount[]> {
    return this.makeRequest<WithdrawalAccount[]>('/withdrawal-accounts');
  }

  async createWithdrawalAccount(accountData: CreateWithdrawalAccountRequest): Promise<WithdrawalAccount> {
    // Validate bank account if provided
    if (accountData.type === 'bank_account' && accountData.bankDetails) {
      const validation = paystackService.validateBankAccount(accountData.bankDetails);
      if (!validation.isValid) {
        throw new Error(validation.errors[0].message);
      }

      // Verify account with Paystack
      try {
        const verification = await paystackService.verifyAccountNumber(
          accountData.bankDetails.accountNumber,
          accountData.bankDetails.bankCode
        );
        
        // Update account name with verified name
        accountData.bankDetails.accountName = verification.data.account_name;
      } catch (error) {
        throw new Error('Failed to verify bank account details');
      }
    }

    return this.makeRequest<WithdrawalAccount>('/withdrawal-accounts', 'POST', accountData);
  }

  async updateWithdrawalAccount(
    accountId: string, 
    updateData: Partial<CreateWithdrawalAccountRequest>
  ): Promise<WithdrawalAccount> {
    return this.makeRequest<WithdrawalAccount>(`/withdrawal-accounts/${accountId}`, 'PUT', updateData);
  }

  async deleteWithdrawalAccount(accountId: string): Promise<void> {
    await this.makeRequest<void>(`/withdrawal-accounts/${accountId}`, 'DELETE');
  }

  async setDefaultAccount(accountId: string): Promise<void> {
    await this.makeRequest<void>(`/withdrawal-accounts/${accountId}/set-default`, 'POST');
  }

  async verifyWithdrawalAccount(accountId: string): Promise<WithdrawalAccount> {
    return this.makeRequest<WithdrawalAccount>(`/withdrawal-accounts/${accountId}/verify`, 'POST');
  }

  // Automatic Withdrawal Rules
  async getAutomaticRules(): Promise<AutomaticWithdrawalRule[]> {
    return this.makeRequest<AutomaticWithdrawalRule[]>('/automatic-withdrawal-rules');
  }

  async createAutomaticRule(ruleData: CreateAutomaticRuleRequest): Promise<AutomaticWithdrawalRule> {
    // Validate rule data
    const validation = this.validateAutomaticRule(ruleData);
    if (!validation.isValid) {
      throw new Error(validation.errors[0].message);
    }

    return this.makeRequest<AutomaticWithdrawalRule>('/automatic-withdrawal-rules', 'POST', ruleData);
  }

  async updateAutomaticRule(
    ruleId: string, 
    updateData: Partial<CreateAutomaticRuleRequest>
  ): Promise<AutomaticWithdrawalRule> {
    return this.makeRequest<AutomaticWithdrawalRule>(`/automatic-withdrawal-rules/${ruleId}`, 'PUT', updateData);
  }

  async deleteAutomaticRule(ruleId: string): Promise<void> {
    await this.makeRequest<void>(`/automatic-withdrawal-rules/${ruleId}`, 'DELETE');
  }

  async toggleRuleStatus(ruleId: string, isActive: boolean): Promise<AutomaticWithdrawalRule> {
    return this.makeRequest<AutomaticWithdrawalRule>(
      `/automatic-withdrawal-rules/${ruleId}/toggle`, 
      'POST', 
      { isActive }
    );
  }

  async triggerRule(ruleId: string): Promise<ProcessingResult> {
    return this.makeRequest<ProcessingResult>(`/automatic-withdrawal-rules/${ruleId}/trigger`, 'POST');
  }

  // Instant Withdrawals
  async createInstantWithdrawal(withdrawalData: InstantWithdrawalRequest): Promise<AutomaticWithdrawalRequest> {
    // Validate withdrawal amount
    const validation = paystackService.validateTransferAmount(withdrawalData.amount);
    if (!validation.isValid) {
      throw new Error(validation.errors[0].message);
    }

    return this.makeRequest<AutomaticWithdrawalRequest>('/withdrawals/instant', 'POST', withdrawalData);
  }

  // Withdrawal History
  async getWithdrawalHistory(
    page: number = 1, 
    limit: number = 20,
    status?: string,
    type?: string
  ): Promise<{
    withdrawals: AutomaticWithdrawalRequest[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...(status && { status }),
      ...(type && { type })
    });

    return this.makeRequest<{
      withdrawals: AutomaticWithdrawalRequest[];
      total: number;
      page: number;
      totalPages: number;
    }>(`/withdrawals/history?${params}`);
  }

  async getWithdrawalById(withdrawalId: string): Promise<AutomaticWithdrawalRequest> {
    return this.makeRequest<AutomaticWithdrawalRequest>(`/withdrawals/${withdrawalId}`);
  }

  async cancelWithdrawal(withdrawalId: string): Promise<AutomaticWithdrawalRequest> {
    return this.makeRequest<AutomaticWithdrawalRequest>(`/withdrawals/${withdrawalId}/cancel`, 'POST');
  }

  // Dashboard Data
  async getDashboardData(): Promise<WithdrawalDashboardData> {
    return this.makeRequest<WithdrawalDashboardData>('/withdrawals/dashboard');
  }

  // Processing Engine
  async getProcessingStatus(): Promise<{
    isProcessing: boolean;
    queueLength: number;
    lastProcessingTime?: string;
    processedRulesCount: number;
    failedRulesCount: number;
  }> {
    return this.makeRequest<{
      isProcessing: boolean;
      queueLength: number;
      lastProcessingTime?: string;
      processedRulesCount: number;
      failedRulesCount: number;
    }>('/withdrawals/processing-status');
  }

  async triggerProcessing(): Promise<{ message: string; processedRules: number }> {
    return this.makeRequest<{ message: string; processedRules: number }>('/withdrawals/trigger-processing', 'POST');
  }

  // Validation Methods
  validateAutomaticRule(ruleData: CreateAutomaticRuleRequest): ValidationResult {
    const errors: WithdrawalError[] = [];

    // Validate name
    if (!ruleData.name || ruleData.name.trim().length < 3) {
      errors.push({
        code: 'INVALID_NAME',
        message: 'Rule name must be at least 3 characters long'
      });
    }

    // Validate trigger conditions
    if (!this.validateTriggerConditions(ruleData.triggerType, ruleData.triggerConditions)) {
      errors.push({
        code: 'INVALID_TRIGGER_CONDITIONS',
        message: 'Invalid trigger conditions for the selected trigger type'
      });
    }

    // Validate withdrawal config
    if (!this.validateWithdrawalConfig(ruleData.withdrawalConfig)) {
      errors.push({
        code: 'INVALID_WITHDRAWAL_CONFIG',
        message: 'Invalid withdrawal configuration'
      });
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  private validateTriggerConditions(triggerType: string, conditions: any): boolean {
    switch (triggerType) {
      case 'balance_threshold':
        return conditions.balanceThreshold && conditions.balanceThreshold > 0;
      case 'date_based':
        return (conditions.dayOfMonth && conditions.dayOfMonth >= 1 && conditions.dayOfMonth <= 31) ||
               (conditions.dayOfWeek && conditions.dayOfWeek >= 0 && conditions.dayOfWeek <= 6);
      case 'interest_earned':
        return conditions.interestThreshold && conditions.interestThreshold > 0;
      case 'goal_reached':
        return conditions.goalId || (conditions.goalPercentage && conditions.goalPercentage > 0);
      default:
        return false;
    }
  }

  private validateWithdrawalConfig(config: any): boolean {
    if (!config.withdrawalAccountId) return false;
    if (!config.amountType) return false;
    
    switch (config.amountType) {
      case 'fixed':
        return config.amount && config.amount > 0;
      case 'percentage':
        return config.percentage && config.percentage > 0 && config.percentage <= 100;
      case 'excess':
      case 'all':
        return true;
      default:
        return false;
    }
  }

  // Utility Methods
  calculateWithdrawalAmount(
    amountType: string,
    config: any,
    currentBalance: number,
    triggerAmount?: number
  ): number {
    switch (amountType) {
      case 'fixed':
        return config.amount;
      case 'percentage':
        const percentageAmount = (currentBalance * config.percentage) / 100;
        return config.keepMinimumBalance 
          ? Math.max(0, percentageAmount - config.keepMinimumBalance)
          : percentageAmount;
      case 'excess':
        return triggerAmount ? Math.max(0, currentBalance - triggerAmount) : 0;
      case 'all':
        return config.keepMinimumBalance 
          ? Math.max(0, currentBalance - config.keepMinimumBalance)
          : currentBalance;
      default:
        return 0;
    }
  }

  formatCurrency(amount: number, currency: string = 'NGN'): string {
    const symbol = currency === 'NGN' ? '₦' : '$';
    return `${symbol}${amount.toLocaleString()}`;
  }

  getNextExecutionTime(rule: AutomaticWithdrawalRule): Date | null {
    const now = new Date();
    
    switch (rule.triggerType) {
      case 'date_based':
        const conditions = rule.triggerConditions;
        if (conditions.dayOfMonth) {
          const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, conditions.dayOfMonth);
          if (conditions.time) {
            const [hours, minutes] = conditions.time.split(':').map(Number);
            nextMonth.setHours(hours, minutes, 0, 0);
          }
          return nextMonth;
        }
        break;
      default:
        return null; // Other trigger types don't have scheduled execution times
    }
    
    return null;
  }
}

// Export singleton instance
export const automaticWithdrawalService = new AutomaticWithdrawalService();
export default automaticWithdrawalService;
