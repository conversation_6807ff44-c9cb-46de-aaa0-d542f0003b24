# 🚀 Deployment Guide

This guide covers various deployment options for the Latest Green Better Interest App.

## 📋 **Prerequisites**

- Node.js 18+
- Git
- Environment variables configured
- Backend API running (if deploying frontend only)

## 🌐 **Vercel Deployment (Recommended)**

Vercel provides the easiest deployment for Next.js applications.

### **Automatic Deployment**

1. **Connect to GitHub**
   - Go to [vercel.com](https://vercel.com)
   - Sign in with GitHub
   - Import your repository: `koja-pay/latestgreenbetterinterestapp`

2. **Configure Environment Variables**
   ```
   NEXT_PUBLIC_API_URL=https://your-api-domain.com/api
   NEXT_PUBLIC_WS_URL=wss://your-api-domain.com
   NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY=pk_live_your_live_key
   NEXT_PUBLIC_APP_URL=https://your-app-domain.vercel.app
   ```

3. **Deploy**
   - Click "Deploy"
   - Vercel will automatically build and deploy your app
   - Get your live URL: `https://your-app-name.vercel.app`

### **Manual Deployment**

```bash
# Install Vercel CLI
npm install -g vercel

# Login to Vercel
vercel login

# Deploy
vercel

# Deploy to production
vercel --prod
```

## 🐳 **Docker Deployment**

### **Single Container**

```bash
# Build the image
docker build -t latestgreenbetterinterestapp .

# Run the container
docker run -p 3000:3000 \
  -e NEXT_PUBLIC_API_URL=https://your-api-domain.com/api \
  -e NEXT_PUBLIC_WS_URL=wss://your-api-domain.com \
  latestgreenbetterinterestapp
```

### **Docker Compose (Full Stack)**

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## ☁️ **AWS Deployment**

### **AWS Amplify**

1. **Connect Repository**
   - Go to AWS Amplify Console
   - Connect your GitHub repository
   - Select the main branch

2. **Build Settings**
   ```yaml
   version: 1
   frontend:
     phases:
       preBuild:
         commands:
           - npm install
       build:
         commands:
           - npm run build
     artifacts:
       baseDirectory: .next
       files:
         - '**/*'
     cache:
       paths:
         - node_modules/**/*
   ```

3. **Environment Variables**
   - Add all required environment variables in Amplify console

### **AWS EC2**

```bash
# Connect to EC2 instance
ssh -i your-key.pem ubuntu@your-ec2-ip

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Clone repository
git clone https://github.com/koja-pay/latestgreenbetterinterestapp.git
cd latestgreenbetterinterestapp

# Install dependencies
npm install

# Build application
npm run build

# Install PM2 for process management
sudo npm install -g pm2

# Start application
pm2 start npm --name "savings-app" -- start

# Save PM2 configuration
pm2 save
pm2 startup
```

## 🌊 **DigitalOcean Deployment**

### **App Platform**

1. **Create App**
   - Go to DigitalOcean App Platform
   - Connect your GitHub repository
   - Select Node.js environment

2. **Configure Build**
   ```yaml
   name: latestgreenbetterinterestapp
   services:
   - name: web
     source_dir: /
     github:
       repo: koja-pay/latestgreenbetterinterestapp
       branch: main
     run_command: npm start
     build_command: npm run build
     environment_slug: node-js
     instance_count: 1
     instance_size_slug: basic-xxs
   ```

### **Droplet Deployment**

```bash
# Create and configure droplet
# Install Node.js, nginx, and PM2
# Clone repository and build
# Configure nginx reverse proxy
# Set up SSL with Let's Encrypt
```

## 🔧 **Environment Configuration**

### **Production Environment Variables**

```env
# API Configuration
NEXT_PUBLIC_API_URL=https://api.yourdomain.com/api
NEXT_PUBLIC_WS_URL=wss://api.yourdomain.com

# App Configuration
NEXT_PUBLIC_APP_URL=https://yourdomain.com
NEXT_PUBLIC_APP_NAME="Latest Green Better Interest App"

# Payment Integration
NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY=pk_live_your_live_key

# Security
NEXTAUTH_SECRET=your_production_secret_here
NEXTAUTH_URL=https://yourdomain.com

# Features
NEXT_PUBLIC_ENABLE_WEBSOCKET=true
NEXT_PUBLIC_NOTIFICATION_SOUND=true

# Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX

# Production Settings
NODE_ENV=production
NEXT_PUBLIC_DEBUG=false
```

### **Security Considerations**

1. **Environment Variables**
   - Never commit `.env` files
   - Use secure secrets for production
   - Rotate keys regularly

2. **HTTPS**
   - Always use HTTPS in production
   - Configure SSL certificates
   - Use HSTS headers

3. **CORS**
   - Configure proper CORS policies
   - Whitelist only necessary domains

## 📊 **Monitoring & Analytics**

### **Performance Monitoring**

```bash
# Install monitoring tools
npm install @vercel/analytics
npm install @sentry/nextjs
```

### **Error Tracking**

```javascript
// sentry.client.config.js
import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  environment: process.env.NODE_ENV,
});
```

### **Analytics Setup**

```javascript
// Google Analytics
import { GoogleAnalytics } from '@next/third-parties/google'

export default function RootLayout({ children }) {
  return (
    <html>
      <body>{children}</body>
      <GoogleAnalytics gaId="G-XXXXXXXXXX" />
    </html>
  )
}
```

## 🔄 **CI/CD Pipeline**

### **GitHub Actions**

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm test
      
      - name: Build application
        run: npm run build
      
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          vercel-args: '--prod'
```

## 🚨 **Troubleshooting**

### **Common Issues**

1. **Build Failures**
   ```bash
   # Clear cache and reinstall
   rm -rf .next node_modules
   npm install
   npm run build
   ```

2. **Environment Variables**
   ```bash
   # Check environment variables
   printenv | grep NEXT_PUBLIC
   ```

3. **Memory Issues**
   ```bash
   # Increase Node.js memory limit
   NODE_OPTIONS="--max-old-space-size=4096" npm run build
   ```

### **Performance Optimization**

1. **Bundle Analysis**
   ```bash
   npm install @next/bundle-analyzer
   ANALYZE=true npm run build
   ```

2. **Image Optimization**
   - Use Next.js Image component
   - Optimize images before upload
   - Use WebP format when possible

3. **Caching**
   - Configure CDN caching
   - Use service workers for offline support
   - Implement proper cache headers

## 📈 **Scaling Considerations**

### **Horizontal Scaling**
- Use load balancers
- Implement session storage (Redis)
- Use CDN for static assets

### **Database Scaling**
- Implement read replicas
- Use connection pooling
- Consider database sharding

### **Monitoring**
- Set up health checks
- Monitor response times
- Track error rates
- Monitor resource usage

## 🔐 **Security Checklist**

- [ ] HTTPS enabled
- [ ] Security headers configured
- [ ] Environment variables secured
- [ ] Dependencies updated
- [ ] CORS properly configured
- [ ] Rate limiting implemented
- [ ] Input validation in place
- [ ] Error handling doesn't expose internals

## 📞 **Support**

For deployment issues:
- Check the [troubleshooting section](#troubleshooting)
- Review environment variable configuration
- Check build logs for errors
- Contact support team if needed

---

**Happy Deploying! 🚀**
