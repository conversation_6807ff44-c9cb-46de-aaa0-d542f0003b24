import { 
  KYC, 
  KYCDocument,
  CreateKYCData,
  UpdateKYCData,
  UploadKYCDocumentData,
  ReviewKYCData,
  KYCStats,
  KYCSearchFilters,
  PaginatedKYCResponse,
  KYCRequirements,
  ApiResponse
} from '../types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

class KYCService {
  private getAuthHeaders() {
    const token = localStorage.getItem('auth_token');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` })
    };
  }

  private getFileUploadHeaders() {
    const token = localStorage.getItem('auth_token');
    return {
      ...(token && { Authorization: `Bearer ${token}` })
    };
  }

  // User KYC Management
  async getUserKYC(): Promise<KYC> {
    const response = await fetch(`${API_BASE_URL}/kyc/user`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch user KYC');
    }

    return response.json();
  }

  async createKYC(data: CreateKYCData): Promise<KYC> {
    const response = await fetch(`${API_BASE_URL}/kyc`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to create KYC');
    }

    return response.json();
  }

  async updateKYC(kycId: string, data: UpdateKYCData): Promise<KYC> {
    const response = await fetch(`${API_BASE_URL}/kyc/${kycId}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update KYC');
    }

    return response.json();
  }

  async submitKYC(kycId: string): Promise<KYC> {
    const response = await fetch(`${API_BASE_URL}/kyc/${kycId}/submit`, {
      method: 'POST',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to submit KYC');
    }

    return response.json();
  }

  // Document Management
  async uploadDocument(data: UploadKYCDocumentData): Promise<KYCDocument> {
    const formData = new FormData();
    formData.append('file', data.file);
    formData.append('type', data.type);
    formData.append('name', data.name);
    if (data.description) formData.append('description', data.description);
    if (data.expiryDate) formData.append('expiryDate', data.expiryDate);

    const response = await fetch(`${API_BASE_URL}/kyc/${data.kycId}/documents`, {
      method: 'POST',
      headers: this.getFileUploadHeaders(),
      body: formData
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to upload document');
    }

    return response.json();
  }

  async getDocuments(kycId: string): Promise<KYCDocument[]> {
    const response = await fetch(`${API_BASE_URL}/kyc/${kycId}/documents`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch documents');
    }

    return response.json();
  }

  async deleteDocument(kycId: string, documentId: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/kyc/${kycId}/documents/${documentId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to delete document');
    }
  }

  async downloadDocument(kycId: string, documentId: string): Promise<Blob> {
    const response = await fetch(`${API_BASE_URL}/kyc/${kycId}/documents/${documentId}/download`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to download document');
    }

    return response.blob();
  }

  // Admin KYC Management
  async getAllKYC(filters?: KYCSearchFilters): Promise<PaginatedKYCResponse> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/kyc/all?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch all KYC records');
    }

    return response.json();
  }

  async getKYCById(kycId: string): Promise<KYC> {
    const response = await fetch(`${API_BASE_URL}/kyc/${kycId}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch KYC');
    }

    return response.json();
  }

  async reviewKYC(data: ReviewKYCData): Promise<KYC> {
    const response = await fetch(`${API_BASE_URL}/kyc/${data.kycId}`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({
        status: data.status,
        reviewNotes: data.reviewNotes,
        rejectionReason: data.rejectionReason,
        documentReviews: data.documentReviews
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to review KYC');
    }

    return response.json();
  }

  async approveKYC(kycId: string, reviewNotes?: string): Promise<KYC> {
    return this.reviewKYC({
      kycId,
      status: 'APPROVED',
      reviewNotes
    });
  }

  async rejectKYC(kycId: string, rejectionReason: string, reviewNotes?: string): Promise<KYC> {
    return this.reviewKYC({
      kycId,
      status: 'REJECTED',
      rejectionReason,
      reviewNotes
    });
  }

  async assignReviewer(kycId: string, reviewerId: string): Promise<KYC> {
    const response = await fetch(`${API_BASE_URL}/kyc/${kycId}/assign`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ reviewerId })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to assign reviewer');
    }

    return response.json();
  }

  // Statistics and Analytics
  async getKYCStats(filters?: {
    dateFrom?: string;
    dateTo?: string;
    level?: string;
    status?: string;
  }): Promise<KYCStats> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/kyc/stats?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch KYC statistics');
    }

    return response.json();
  }

  async getKYCAnalytics(period: 'daily' | 'weekly' | 'monthly' = 'monthly'): Promise<any> {
    const response = await fetch(`${API_BASE_URL}/kyc/analytics?period=${period}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch KYC analytics');
    }

    return response.json();
  }

  // KYC Requirements and Configuration
  async getKYCRequirements(level: 'BASIC' | 'INTERMEDIATE' | 'ADVANCED'): Promise<KYCRequirements> {
    const response = await fetch(`${API_BASE_URL}/kyc/requirements/${level}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch KYC requirements');
    }

    return response.json();
  }

  async getAllKYCRequirements(): Promise<KYCRequirements[]> {
    const response = await fetch(`${API_BASE_URL}/kyc/requirements`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch all KYC requirements');
    }

    return response.json();
  }

  // Utility Methods
  getStatusColor(status: string): string {
    const colors: Record<string, string> = {
      PENDING: '#F59E0B',
      UNDER_REVIEW: '#3B82F6',
      APPROVED: '#10B981',
      REJECTED: '#EF4444',
      EXPIRED: '#6B7280'
    };
    
    return colors[status] || '#6B7280';
  }

  getLevelColor(level: string): string {
    const colors: Record<string, string> = {
      BASIC: '#10B981',
      INTERMEDIATE: '#F59E0B',
      ADVANCED: '#8B5CF6'
    };
    
    return colors[level] || '#6B7280';
  }

  getDocumentTypeIcon(type: string): string {
    const icons: Record<string, string> = {
      NATIONAL_ID: '🆔',
      PASSPORT: '📘',
      DRIVERS_LICENSE: '🚗',
      VOTERS_CARD: '🗳️',
      UTILITY_BILL: '📄',
      BANK_STATEMENT: '🏦',
      SELFIE: '🤳',
      SIGNATURE: '✍️',
      OTHER: '📎'
    };
    
    return icons[type] || '📎';
  }

  validateFileSize(file: File, maxSizeMB: number = 5): { isValid: boolean; error?: string } {
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    
    if (file.size > maxSizeBytes) {
      return {
        isValid: false,
        error: `File size must be less than ${maxSizeMB}MB`
      };
    }
    
    return { isValid: true };
  }

  validateFileType(file: File, allowedTypes: string[]): { isValid: boolean; error?: string } {
    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: `File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(', ')}`
      };
    }
    
    return { isValid: true };
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  calculateKYCProgress(kyc: KYC, requirements: KYCRequirements): number {
    const requiredDocuments = requirements.requiredDocuments.filter(doc => doc.isRequired);
    const uploadedDocuments = kyc.documents.filter(doc => doc.status !== 'REJECTED');
    
    const personalInfoComplete = !!(
      kyc.personalInfo.firstName &&
      kyc.personalInfo.lastName &&
      kyc.personalInfo.dateOfBirth &&
      kyc.personalInfo.nationality
    );
    
    const addressInfoComplete = !!(
      kyc.addressInfo.street &&
      kyc.addressInfo.city &&
      kyc.addressInfo.state &&
      kyc.addressInfo.country
    );
    
    const contactInfoComplete = !!(
      kyc.contactInfo.phoneNumber &&
      kyc.contactInfo.email
    );
    
    const documentsComplete = uploadedDocuments.length >= requiredDocuments.length;
    
    const completedSections = [
      personalInfoComplete,
      addressInfoComplete,
      contactInfoComplete,
      documentsComplete
    ].filter(Boolean).length;
    
    return (completedSections / 4) * 100;
  }

  getKYCLevelBenefits(level: string): string[] {
    const benefits: Record<string, string[]> = {
      BASIC: [
        'Basic account access',
        'Limited transaction amounts',
        'Basic savings plans'
      ],
      INTERMEDIATE: [
        'Increased transaction limits',
        'Access to group savings',
        'Target savings features',
        'Basic withdrawal options'
      ],
      ADVANCED: [
        'Full platform access',
        'Unlimited transactions',
        'Premium features',
        'Priority support',
        'Advanced analytics'
      ]
    };
    
    return benefits[level] || [];
  }
}

export const kycService = new KYCService();
