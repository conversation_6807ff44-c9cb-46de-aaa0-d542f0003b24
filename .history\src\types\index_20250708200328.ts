// Authentication & User Types
export * from './auth';
export * from './user';

// Savings Types
export * from './savings';
export * from './groupSavings';
export * from './targetSavings';
export * from './rotationalGroupSavings';

// Financial Types
export * from './deposits';
export * from './withdrawals';
export * from './transactions';

// System Types
export * from './notifications';
export * from './kyc';
export * from './admin';
export * from './settings';

// Common Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  errors?: Record<string, string[]>;
  meta?: {
    page?: number;
    limit?: number;
    total?: number;
    totalPages?: number;
  };
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface DateRange {
  from: string;
  to: string;
}

export interface FileUpload {
  file: File;
  name: string;
  type: string;
  size: number;
}

export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface Currency {
  code: string;
  name: string;
  symbol: string;
  decimals: number;
}

export interface Country {
  code: string;
  name: string;
  currency: string;
  phoneCode: string;
}

export interface Bank {
  code: string;
  name: string;
  country: string;
  active: boolean;
}

export interface PaymentMethod {
  id: string;
  type: 'CARD' | 'BANK_TRANSFER' | 'USSD' | 'QR_CODE' | 'MOBILE_MONEY';
  name: string;
  description: string;
  isActive: boolean;
  fees: {
    percentage: number;
    fixed: number;
    minimum: number;
    maximum: number;
  };
  limits: {
    minimum: number;
    maximum: number;
    daily: number;
    monthly: number;
  };
}

export interface ErrorResponse {
  error: string;
  message: string;
  details?: Record<string, any>;
  code?: string;
  timestamp: string;
}

export interface SuccessResponse<T = any> {
  success: true;
  data: T;
  message?: string;
  meta?: Record<string, any>;
}

// Form Types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'select' | 'textarea' | 'checkbox' | 'radio' | 'date' | 'file';
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  options?: SelectOption[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    custom?: (value: any) => string | null;
  };
}

export interface FormState {
  values: Record<string, any>;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  isSubmitting: boolean;
  isValid: boolean;
}

// Chart Types
export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string | string[];
    borderWidth?: number;
  }[];
}

export interface ChartOptions {
  responsive: boolean;
  maintainAspectRatio: boolean;
  plugins?: {
    legend?: {
      display: boolean;
      position?: 'top' | 'bottom' | 'left' | 'right';
    };
    tooltip?: {
      enabled: boolean;
      mode?: 'index' | 'dataset' | 'point' | 'nearest';
    };
  };
  scales?: {
    x?: {
      display: boolean;
      title?: {
        display: boolean;
        text: string;
      };
    };
    y?: {
      display: boolean;
      title?: {
        display: boolean;
        text: string;
      };
      beginAtZero?: boolean;
    };
  };
}

// Theme Types
export interface Theme {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    success: string;
    warning: string;
    error: string;
    info: string;
  };
  fonts: {
    primary: string;
    secondary: string;
    mono: string;
  };
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  borderRadius: {
    sm: string;
    md: string;
    lg: string;
    full: string;
  };
}

// Navigation Types
export interface NavItem {
  id: string;
  label: string;
  icon?: string;
  href?: string;
  children?: NavItem[];
  badge?: string | number;
  isActive?: boolean;
  isDisabled?: boolean;
  permissions?: string[];
}

export interface Breadcrumb {
  label: string;
  href?: string;
  isActive?: boolean;
}

// Modal Types
export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  closeOnOverlayClick?: boolean;
  closeOnEscape?: boolean;
  showCloseButton?: boolean;
}

// Toast Types
export interface ToastMessage {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  message: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}
