export interface Withdrawal {
  id: string;
  userId: string;
  type: 'PLAN_CLOSURE' | 'EXTERNAL_PAYOUT';
  amount: number;
  currency: string;
  status: 'PENDING' | 'PROCESSING' | 'APPROVED' | 'COMPLETED' | 'FAILED' | 'CANCELLED' | 'REJECTED';
  reference: string;
  paystackReference?: string;
  planId?: string;
  plan?: {
    id: string;
    name: string;
    planType: string;
  };
  bankAccount?: {
    id: string;
    bankName: string;
    accountNumber: string;
    accountName: string;
  };
  penaltyAmount: number;
  penaltyReason?: string;
  netAmount: number;
  fees: number;
  description?: string;
  adminNotes?: string;
  approvedBy?: string;
  approvedAt?: string;
  rejectedBy?: string;
  rejectedAt?: string;
  rejectionReason?: string;
  processedAt?: string;
  completedAt?: string;
  failureReason?: string;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface InitiateWithdrawalData {
  type: 'PLAN_CLOSURE' | 'EXTERNAL_PAYOUT';
  amount: number;
  planId?: string;
  bankAccountId?: string;
  description?: string;
  metadata?: Record<string, any>;
}

export interface InitiateWithdrawalResponse {
  withdrawal: Withdrawal;
  requiresApproval: boolean;
  estimatedProcessingTime: string;
  penaltyInfo?: {
    penaltyAmount: number;
    penaltyRate: number;
    reason: string;
  };
}

export interface PlanClosureData {
  planId: string;
  amount: number;
  reason?: string;
  forceClose?: boolean;
}

export interface PlanClosureResponse {
  withdrawal: Withdrawal;
  planClosed: boolean;
  balanceCredited: number;
  penaltyApplied: number;
  message: string;
}

export interface ApproveWithdrawalData {
  withdrawalId: string;
  adminNotes?: string;
  adjustedAmount?: number;
}

export interface RejectWithdrawalData {
  withdrawalId: string;
  rejectionReason: string;
  adminNotes?: string;
}

export interface WithdrawalStats {
  totalWithdrawals: number;
  totalAmount: number;
  pendingWithdrawals: number;
  pendingAmount: number;
  approvedWithdrawals: number;
  approvedAmount: number;
  completedWithdrawals: number;
  completedAmount: number;
  rejectedWithdrawals: number;
  rejectedAmount: number;
  totalPenalties: number;
  averageWithdrawalAmount: number;
  withdrawalsThisMonth: number;
  amountThisMonth: number;
  averageProcessingTime: number;
}

export interface PenaltySummary {
  totalPenalties: number;
  penaltiesThisMonth: number;
  averagePenaltyRate: number;
  penaltyBreakdown: {
    earlyWithdrawal: number;
    planClosure: number;
    other: number;
  };
  topPenaltyReasons: {
    reason: string;
    count: number;
    amount: number;
  }[];
}

export interface WithdrawalSearchFilters {
  search?: string;
  type?: 'PLAN_CLOSURE' | 'EXTERNAL_PAYOUT';
  status?: 'PENDING' | 'PROCESSING' | 'APPROVED' | 'COMPLETED' | 'FAILED' | 'CANCELLED' | 'REJECTED';
  minAmount?: number;
  maxAmount?: number;
  dateFrom?: string;
  dateTo?: string;
  userId?: string;
  planId?: string;
  hasPenalty?: boolean;
  sortBy?: 'createdAt' | 'amount' | 'status' | 'approvedAt';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface PaginatedWithdrawalResponse {
  withdrawals: Withdrawal[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  stats: {
    totalAmount: number;
    pendingCount: number;
    approvedCount: number;
    completedCount: number;
    rejectedCount: number;
    totalPenalties: number;
  };
}

export interface WithdrawalFormData {
  type: 'PLAN_CLOSURE' | 'EXTERNAL_PAYOUT';
  amount: string;
  planId?: string;
  bankAccountId?: string;
  description: string;
}

export interface PaystackPayoutData {
  source: string;
  amount: number;
  recipient: string;
  reason?: string;
  currency?: string;
  reference?: string;
}
