// Migration script to set lastDepositDate for all target savings plans missing it
// Usage: node fixTargetSavingsLastDepositDate.js

const mongoose = require('mongoose');
const TargetSavings = require('../models/targetSavings');

// TODO: Update with your actual MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/kojapay';

async function run() {
  await mongoose.connect(MONGODB_URI, { useNewUrlParser: true, useUnifiedTopology: true });
  console.log('Connected to MongoDB');

  // Find all target savings plans missing lastDepositDate
  const missing = await TargetSavings.find({ $or: [ { lastDepositDate: { $exists: false } }, { lastDepositDate: null } ] });
  console.log(`Found ${missing.length} target savings plans missing lastDepositDate`);

  let updated = 0;
  for (const plan of missing) {
    // Use createdAt as fallback, else now
    const fallbackDate = plan.createdAt || new Date();
    plan.lastDepositDate = fallbackDate;
    await plan.save();
    updated++;
    console.log(`Updated plan ${plan._id}: set lastDepositDate to ${fallbackDate}`);
  }

  console.log(`Migration complete. Updated ${updated} plans.`);
  await mongoose.disconnect();
}

run().catch(err => {
  console.error('Migration failed:', err);
  process.exit(1);
});
