"use client";

import React, { useState } from 'react';
import { useKYCEnforcement } from '../../../src/hooks/use-kyc-enforcement';
import { KYCIncompleteBanner } from '../../../src/components/KYCIncompleteBanner';
import { motion } from 'framer-motion';
import DashboardLayout from '../../../src/components/dashboard/DashboardLayout';
import { Button } from '../../../src/components/ui/Button';
import { StatCard } from '../../../src/components/dashboard/DashboardCard';
import { Card3D } from '../../../src/components/ui/Card3D';
import { 
  FiZap, 
  FiDollarSign, 
  FiCalendar, 
  FiPlus, 
  FiTrendingUp,
  FiClock,
  FiRepeat,
  FiPause,
  FiPlay,
  FiSettings,
  FiToggleLeft,
  FiToggleRight
} from 'react-icons/fi';

interface AutoSavingRule {
  id: string;
  name: string;
  description: string;
  amount: number;
  frequency: 'daily' | 'weekly' | 'monthly';
  trigger: 'fixed' | 'roundup' | 'percentage';
  isActive: boolean;
  totalSaved: number;
  nextExecution: Date;
  createdDate: Date;
}

const mockAutoSavings: AutoSavingRule[] = [
  {
    id: '1',
    name: 'Daily Coffee Fund',
    description: 'Save ₦500 every day instead of buying coffee',
    amount: 500,
    frequency: 'daily',
    trigger: 'fixed',
    isActive: true,
    totalSaved: 45000,
    nextExecution: new Date(Date.now() + 24 * 60 * 60 * 1000),
    createdDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)
  },
  {
    id: '2',
    name: 'Round-up Savings',
    description: 'Round up purchases to nearest ₦100 and save the difference',
    amount: 0, // Variable amount
    frequency: 'daily',
    trigger: 'roundup',
    isActive: true,
    totalSaved: 12750,
    nextExecution: new Date(Date.now() + 12 * 60 * 60 * 1000),
    createdDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000)
  },
  {
    id: '3',
    name: 'Weekly Emergency Fund',
    description: 'Save 10% of weekly income automatically',
    amount: 15000,
    frequency: 'weekly',
    trigger: 'percentage',
    isActive: true,
    totalSaved: 180000,
    nextExecution: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
    createdDate: new Date(Date.now() - 120 * 24 * 60 * 60 * 1000)
  },
  {
    id: '4',
    name: 'Monthly Investment',
    description: 'Automatically invest ₦50,000 every month',
    amount: 50000,
    frequency: 'monthly',
    trigger: 'fixed',
    isActive: false,
    totalSaved: 200000,
    nextExecution: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000),
    createdDate: new Date(Date.now() - 150 * 24 * 60 * 60 * 1000)
  }
];

  const [selectedTab, setSelectedTab] = useState<'active' | 'paused' | 'all'>('active');
  const kycApproved = useKYCEnforcement({ redirect: false });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const getFrequencyColor = (frequency: string) => {
    switch (frequency) {
      case 'daily': return 'text-green-600 bg-green-600/10';
      case 'weekly': return 'text-blue-600 bg-blue-600/10';
      case 'monthly': return 'text-purple-600 bg-purple-600/10';
      default: return 'text-gray-600 bg-gray-600/10';
    }
  };

  const getTriggerIcon = (trigger: string) => {
    switch (trigger) {
      case 'fixed': return FiCalendar;
      case 'roundup': return FiRepeat;
      case 'percentage': return FiTrendingUp;
      default: return FiZap;
    }
  };

  const getTimeUntilNext = (nextExecution: Date) => {
    const now = new Date();
    const diff = nextExecution.getTime() - now.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(hours / 24);
    
    if (days > 0) return `${days} day${days > 1 ? 's' : ''}`;
    if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''}`;
    return 'Soon';
  };

  const toggleRule = (id: string) => {
    // In a real app, this would update the backend
    console.log(`Toggle rule ${id}`);
  };

  const filteredRules = mockAutoSavings.filter(rule => {
    if (selectedTab === 'active') return rule.isActive;
    if (selectedTab === 'paused') return !rule.isActive;
    return true;
  });

  const totalAutoSaved = mockAutoSavings.reduce((sum, rule) => sum + rule.totalSaved, 0);
  const activeRules = mockAutoSavings.filter(r => r.isActive).length;
  const totalRules = mockAutoSavings.length;
  const avgMonthlySavings = totalAutoSaved / 4; // Assuming 4 months of data

  return (
    <DashboardLayout title="Auto Savings">
      <div className="space-y-8">
        {/* KYC Banner for incomplete KYC */}
        {!kycApproved && <KYCIncompleteBanner />}
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-theme font-inter">Auto Savings</h1>
            <p className="text-theme-secondary mt-2 font-inter">
              Set up automatic savings rules to build wealth effortlessly
            </p>
          </div>
          <Button
            leftIcon={FiPlus}
            className="font-inter"
          >
            Create Rule
          </Button>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="Total Auto Saved"
            value={formatCurrency(totalAutoSaved)}
            subtitle="All time"
            icon={FiZap}
            color="green"
            trend={{ value: 25.3, isPositive: true }}
          />
          <StatCard
            title="Active Rules"
            value={`${activeRules}/${totalRules}`}
            subtitle="Currently running"
            icon={FiPlay}
            color="blue"
          />
          <StatCard
            title="Monthly Average"
            value={formatCurrency(avgMonthlySavings)}
            subtitle="Auto savings"
            icon={FiTrendingUp}
            color="purple"
            trend={{ value: 18.7, isPositive: true }}
          />
          <StatCard
            title="Next Execution"
            value="2 hours"
            subtitle="Round-up savings"
            icon={FiClock}
            color="orange"
          />
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 bg-theme-secondary p-1 rounded-lg w-fit">
          {[
            { key: 'active', label: 'Active Rules' },
            { key: 'paused', label: 'Paused Rules' },
            { key: 'all', label: 'All Rules' }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setSelectedTab(tab.key as any)}
              className={`px-4 py-2 rounded-md text-sm font-medium font-inter transition-all duration-200 ${
                selectedTab === tab.key
                  ? 'bg-brand text-white shadow-md'
                  : 'text-theme-secondary hover:text-theme'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* Auto Savings Rules */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredRules.map((rule) => {
            const TriggerIcon = getTriggerIcon(rule.trigger);
            
            return (
              <Card3D key={rule.id} elevation={2}>
                <div className="p-6">
                  {/* Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 rounded-lg bg-brand/10">
                        <TriggerIcon className="w-5 h-5 text-brand" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-theme font-inter">
                          {rule.name}
                        </h3>
                      </div>
                    </div>
                    <button
                      onClick={() => toggleRule(rule.id)}
                      className="p-1 rounded-lg hover:bg-theme-secondary transition-colors"
                    >
                      {rule.isActive ? (
                        <FiToggleRight className="w-6 h-6 text-brand" />
                      ) : (
                        <FiToggleLeft className="w-6 h-6 text-theme-secondary" />
                      )}
                    </button>
                  </div>

                  <p className="text-sm text-theme-secondary font-inter mb-4">
                    {rule.description}
                  </p>

                  {/* Amount and Frequency */}
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <p className="text-2xl font-bold text-theme font-inter">
                        {rule.trigger === 'roundup' ? 'Variable' : formatCurrency(rule.amount)}
                      </p>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getFrequencyColor(rule.frequency)}`}>
                        {rule.frequency}
                      </span>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-theme-secondary font-inter">Total Saved</p>
                      <p className="text-lg font-semibold text-brand">
                        {formatCurrency(rule.totalSaved)}
                      </p>
                    </div>
                  </div>

                  {/* Details */}
                  <div className="space-y-3 mb-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <FiClock className="w-4 h-4 text-brand" />
                        <span className="text-sm text-theme-secondary font-inter">Next Execution</span>
                      </div>
                      <span className="text-sm font-medium text-theme">
                        {rule.isActive ? getTimeUntilNext(rule.nextExecution) : 'Paused'}
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <FiCalendar className="w-4 h-4 text-brand" />
                        <span className="text-sm text-theme-secondary font-inter">Created</span>
                      </div>
                      <span className="text-sm font-medium text-theme">
                        {rule.createdDate.toLocaleDateString()}
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <FiRepeat className="w-4 h-4 text-brand" />
                        <span className="text-sm text-theme-secondary font-inter">Trigger Type</span>
                      </div>
                      <span className="text-sm font-medium text-theme capitalize">
                        {rule.trigger}
                      </span>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex space-x-2">
                    <Button
                      fullWidth
                      variant={rule.isActive ? 'outline' : 'primary'}
                      size="sm"
                      leftIcon={rule.isActive ? FiPause : FiPlay}
                      onClick={() => toggleRule(rule.id)}
                      className="font-inter"
                    >
                      {rule.isActive ? 'Pause' : 'Resume'}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      leftIcon={FiSettings}
                      className="font-inter"
                    >
                      Edit
                    </Button>
                  </div>
                </div>
              </Card3D>
            );
          })}
        </div>

        {/* Empty State */}
        {filteredRules.length === 0 && (
          <div className="text-center py-12">
            <FiZap className="w-16 h-16 text-theme-secondary mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-theme font-inter mb-2">
              No {selectedTab} rules found
            </h3>
            <p className="text-theme-secondary font-inter mb-6">
              {selectedTab === 'active' 
                ? 'Create your first auto-savings rule to start building wealth automatically'
                : selectedTab === 'paused'
                ? 'No paused rules. All your auto-savings are currently active!'
                : 'Set up automatic savings rules to make saving effortless'
              }
            </p>
            {selectedTab !== 'paused' && (
              <Button
                leftIcon={FiPlus}
                className="font-inter"
              >
                Create Auto-Savings Rule
              </Button>
            )}
          </div>
        )}

        {/* Quick Setup Section */}
        {selectedTab === 'active' && filteredRules.length > 0 && (
          <Card3D elevation={1}>
            <div className="p-6">
              <h3 className="text-lg font-semibold text-theme font-inter mb-4">
                Quick Setup Suggestions
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 rounded-lg bg-theme-secondary">
                  <FiRepeat className="w-8 h-8 text-brand mb-2" />
                  <h4 className="font-medium text-theme font-inter mb-1">Round-up Savings</h4>
                  <p className="text-sm text-theme-secondary font-inter">Save spare change from every purchase</p>
                </div>
                <div className="p-4 rounded-lg bg-theme-secondary">
                  <FiCalendar className="w-8 h-8 text-brand mb-2" />
                  <h4 className="font-medium text-theme font-inter mb-1">Fixed Amount</h4>
                  <p className="text-sm text-theme-secondary font-inter">Save a fixed amount regularly</p>
                </div>
                <div className="p-4 rounded-lg bg-theme-secondary">
                  <FiTrendingUp className="w-8 h-8 text-brand mb-2" />
                  <h4 className="font-medium text-theme font-inter mb-1">Percentage Based</h4>
                  <p className="text-sm text-theme-secondary font-inter">Save a percentage of your income</p>
                </div>
              </div>
            </div>
          </Card3D>
        )}
      </div>
    </DashboardLayout>
  );
}
