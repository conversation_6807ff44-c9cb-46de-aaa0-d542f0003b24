export interface GroupSavings {
  id: string;
  name: string;
  description: string;
  creatorId: string;
  creator: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  targetAmount: number;
  currentAmount: number;
  contributionAmount: number;
  frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY';
  duration: number; // in months
  maxMembers: number;
  currentMembers: number;
  startDate: string;
  endDate: string;
  status: 'PENDING' | 'ACTIVE' | 'COMPLETED' | 'CANCELLED';
  isPublic: boolean;
  inviteCode?: string;
  rules?: string;
  category: 'GENERAL' | 'BUSINESS' | 'EDUCATION' | 'EMERGENCY' | 'VACATION' | 'OTHER';
  interestRate: number;
  penaltyRate: number;
  members: GroupMember[];
  contributions: GroupContribution[];
  payoutSchedule: PayoutSchedule[];
  createdAt: string;
  updatedAt: string;
}

export interface GroupMember {
  id: string;
  groupId: string;
  userId: string;
  user: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    profileImage?: string;
  };
  role: 'CREATOR' | 'ADMIN' | 'MEMBER';
  status: 'PENDING' | 'ACTIVE' | 'SUSPENDED' | 'LEFT';
  totalContributions: number;
  missedContributions: number;
  payoutPosition?: number;
  joinedAt: string;
  lastContributionAt?: string;
}

export interface GroupContribution {
  id: string;
  groupId: string;
  userId: string;
  amount: number;
  type: 'REGULAR' | 'PENALTY' | 'BONUS';
  status: 'PENDING' | 'COMPLETED' | 'FAILED';
  reference: string;
  paymentMethod: string;
  description?: string;
  dueDate: string;
  paidAt?: string;
  createdAt: string;
}

export interface PayoutSchedule {
  id: string;
  groupId: string;
  userId: string;
  amount: number;
  position: number;
  scheduledDate: string;
  status: 'PENDING' | 'PAID' | 'SKIPPED';
  paidAt?: string;
  reference?: string;
}

export interface CreateGroupSavingsData {
  name: string;
  description: string;
  targetAmount: number;
  contributionAmount: number;
  frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY';
  duration: number;
  maxMembers: number;
  startDate: string;
  isPublic: boolean;
  rules?: string;
  category: 'GENERAL' | 'BUSINESS' | 'EDUCATION' | 'EMERGENCY' | 'VACATION' | 'OTHER';
  interestRate?: number;
  penaltyRate?: number;
}

// Allow joining by groupId OR inviteCode (at least one required)
export interface JoinGroupData {
  groupId?: string;
  inviteCode?: string;
}

export interface InviteGroupMemberData {
  groupId: string;
  email: string;
  message?: string;
}

export interface GroupContributionData {
  groupId: string;
  amount: number;
  paymentMethod: string;
}

export interface GroupSearchFilters {
  search?: string;
  category?: string;
  status?: 'PENDING' | 'ACTIVE' | 'COMPLETED' | 'CANCELLED';
  isPublic?: boolean;
  minAmount?: number;
  maxAmount?: number;
  frequency?: 'DAILY' | 'WEEKLY' | 'MONTHLY';
  sortBy?: 'createdAt' | 'startDate' | 'targetAmount' | 'currentMembers';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface GroupStats {
  totalGroups: number;
  activeGroups: number;
  completedGroups: number;
  totalMembers: number;
  totalContributions: number;
  averageGroupSize: number;
  completionRate: number;
}
