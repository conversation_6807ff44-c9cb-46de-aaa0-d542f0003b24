"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Fi<PERSON><PERSON>s, FiCalendar, FiDollarSign, FiClock, FiTrendingUp, FiPlus, FiCheck } from 'react-icons/fi';

interface GroupSavingsData {
  id: string;
  name: string;
  totalMembers: number;
  currentMembers: number;
  contributionAmount: number;
  frequency: 'weekly' | 'monthly';
  currentRound: number;
  totalRounds: number;
  nextPayoutDate: string;
  nextContributionDate: string;
  status: 'active' | 'pending' | 'completed';
  userPosition: number;
  totalContributed: number;
  expectedPayout: number;
}

const mockGroupData: GroupSavingsData[] = [
  {
    id: '1',
    name: 'Tech Professionals Circle',
    totalMembers: 10,
    currentMembers: 8,
    contributionAmount: 50000,
    frequency: 'monthly',
    currentRound: 3,
    totalRounds: 10,
    nextPayoutDate: '2024-02-15',
    nextContributionDate: '2024-02-01',
    status: 'active',
    userPosition: 5,
    totalContributed: 150000,
    expectedPayout: 500000
  },
  {
    id: '2',
    name: 'Young Entrepreneurs Fund',
    totalMembers: 20,
    currentMembers: 20,
    contributionAmount: 25000,
    frequency: 'weekly',
    currentRound: 8,
    totalRounds: 20,
    nextPayoutDate: '2024-03-22',
    nextContributionDate: '2024-01-29',
    status: 'active',
    userPosition: 12,
    totalContributed: 200000,
    expectedPayout: 500000
  },
  {
    id: '3',
    name: 'Family Savings Group',
    totalMembers: 6,
    currentMembers: 4,
    contributionAmount: 100000,
    frequency: 'monthly',
    currentRound: 1,
    totalRounds: 6,
    nextPayoutDate: '2024-04-01',
    nextContributionDate: '2024-02-01',
    status: 'pending',
    userPosition: 3,
    totalContributed: 100000,
    expectedPayout: 600000
  }
];

export default function RotationalGroupSavings() {
  const [selectedGroup, setSelectedGroup] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400 bg-green-400/10 border-green-400/20';
      case 'pending': return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/20';
      case 'completed': return 'text-blue-400 bg-blue-400/10 border-blue-400/20';
      default: return 'text-gray-400 bg-gray-400/10 border-gray-400/20';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-white mb-2">Rotational Group Savings</h2>
          <p className="text-gray-400">Join or create savings groups with friends and family</p>
        </div>
        <motion.button
          onClick={() => setShowCreateForm(true)}
          className="px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-green-500/25 flex items-center space-x-2"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <FiPlus className="w-5 h-5" />
          <span>Create Group</span>
        </motion.button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-900/50 border border-gray-800 rounded-xl p-6 transform hover:scale-105 transition-all duration-300"
          style={{ perspective: '1000px' }}
        >
          <div className="flex items-center justify-between mb-4">
            <FiUsers className="w-8 h-8 text-green-400" />
            <span className="text-2xl font-bold text-white">3</span>
          </div>
          <p className="text-gray-400 text-sm">Active Groups</p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-900/50 border border-gray-800 rounded-xl p-6 transform hover:scale-105 transition-all duration-300"
          style={{ perspective: '1000px' }}
        >
          <div className="flex items-center justify-between mb-4">
            <FiDollarSign className="w-8 h-8 text-blue-400" />
            <span className="text-2xl font-bold text-white">₦450K</span>
          </div>
          <p className="text-gray-400 text-sm">Total Contributed</p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-900/50 border border-gray-800 rounded-xl p-6 transform hover:scale-105 transition-all duration-300"
          style={{ perspective: '1000px' }}
        >
          <div className="flex items-center justify-between mb-4">
            <FiTrendingUp className="w-8 h-8 text-purple-400" />
            <span className="text-2xl font-bold text-white">₦1.6M</span>
          </div>
          <p className="text-gray-400 text-sm">Expected Payouts</p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-900/50 border border-gray-800 rounded-xl p-6 transform hover:scale-105 transition-all duration-300"
          style={{ perspective: '1000px' }}
        >
          <div className="flex items-center justify-between mb-4">
            <FiClock className="w-8 h-8 text-yellow-400" />
            <span className="text-2xl font-bold text-white">Feb 15</span>
          </div>
          <p className="text-gray-400 text-sm">Next Payout</p>
        </motion.div>
      </div>

      {/* Groups List */}
      <div className="space-y-4">
        <h3 className="text-xl font-semibold text-white">Your Groups</h3>
        {mockGroupData.map((group, index) => (
          <motion.div
            key={group.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-gray-900/50 border border-gray-800 rounded-xl p-6 hover:border-gray-700 transition-all duration-300 transform hover:scale-[1.02] cursor-pointer"
            style={{ 
              perspective: '1000px',
              transformStyle: 'preserve-3d'
            }}
            onClick={() => setSelectedGroup(selectedGroup === group.id ? null : group.id)}
            whileHover={{ 
              rotateX: 2,
              rotateY: 2,
              scale: 1.02
            }}
          >
            <div className="flex justify-between items-start mb-4">
              <div>
                <h4 className="text-lg font-semibold text-white mb-2">{group.name}</h4>
                <div className="flex items-center space-x-4 text-sm text-gray-400">
                  <span className="flex items-center">
                    <FiUsers className="w-4 h-4 mr-1" />
                    {group.currentMembers}/{group.totalMembers} members
                  </span>
                  <span className="flex items-center">
                    <FiCalendar className="w-4 h-4 mr-1" />
                    {group.frequency}
                  </span>
                </div>
              </div>
              <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(group.status)}`}>
                {group.status.charAt(0).toUpperCase() + group.status.slice(1)}
              </span>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              <div>
                <p className="text-xs text-gray-500 mb-1">Contribution</p>
                <p className="text-sm font-semibold text-white">{formatCurrency(group.contributionAmount)}</p>
              </div>
              <div>
                <p className="text-xs text-gray-500 mb-1">Round</p>
                <p className="text-sm font-semibold text-white">{group.currentRound}/{group.totalRounds}</p>
              </div>
              <div>
                <p className="text-xs text-gray-500 mb-1">Your Position</p>
                <p className="text-sm font-semibold text-white">#{group.userPosition}</p>
              </div>
              <div>
                <p className="text-xs text-gray-500 mb-1">Expected Payout</p>
                <p className="text-sm font-semibold text-green-400">{formatCurrency(group.expectedPayout)}</p>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mb-4">
              <div className="flex justify-between text-xs text-gray-400 mb-2">
                <span>Progress</span>
                <span>{Math.round((group.currentRound / group.totalRounds) * 100)}%</span>
              </div>
              <div className="w-full bg-gray-800 rounded-full h-2">
                <div 
                  className="bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(group.currentRound / group.totalRounds) * 100}%` }}
                ></div>
              </div>
            </div>

            {/* Expanded Details */}
            {selectedGroup === group.id && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="border-t border-gray-800 pt-4 mt-4"
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h5 className="text-sm font-semibold text-white mb-2">Next Dates</h5>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Next Contribution:</span>
                        <span className="text-white">{group.nextContributionDate}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Next Payout:</span>
                        <span className="text-green-400">{group.nextPayoutDate}</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h5 className="text-sm font-semibold text-white mb-2">Your Summary</h5>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Total Contributed:</span>
                        <span className="text-white">{formatCurrency(group.totalContributed)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Expected Return:</span>
                        <span className="text-green-400">{formatCurrency(group.expectedPayout)}</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="flex space-x-3 mt-4">
                  <button className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg text-sm font-medium transition-colors">
                    Make Contribution
                  </button>
                  <button className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm font-medium transition-colors">
                    View Details
                  </button>
                </div>
              </motion.div>
            )}
          </motion.div>
        ))}
      </div>
    </div>
  );
}
