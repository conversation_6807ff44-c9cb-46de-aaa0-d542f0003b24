"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '../../contexts/ThemeContext';

interface Text3DProps {
  children: React.ReactNode;
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span';
  variant?: 'hero' | 'heading' | 'subheading' | 'body';
  intensity?: 'light' | 'medium' | 'strong';
  color?: 'default' | 'green' | 'white' | 'gradient';
  className?: string;
  animate?: boolean;
}

export function Text3D({
  children,
  as: Component = 'h1',
  variant = 'hero',
  intensity = 'medium',
  color = 'default',
  className = '',
  animate = true
}: Text3DProps) {
  const { theme } = useTheme();

  const getVariantClasses = () => {
    switch (variant) {
      case 'hero':
        return 'text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold font-inter leading-tight';
      case 'heading':
        return 'text-2xl md:text-3xl lg:text-4xl font-bold font-inter leading-tight';
      case 'subheading':
        return 'text-xl md:text-2xl font-semibold font-inter leading-relaxed';
      case 'body':
        return 'text-base md:text-lg font-medium font-inter leading-relaxed';
      default:
        return 'text-4xl font-bold font-inter';
    }
  };

  const getColorClasses = () => {
    if (color === 'gradient') {
      return 'bg-gradient-to-r from-green-400 via-green-500 to-green-600 bg-clip-text text-transparent';
    }
    
    switch (color) {
      case 'green':
        return theme === 'light' ? 'text-green-600' : 'text-green-400';
      case 'white':
        return 'text-white';
      case 'default':
      default:
        return theme === 'light' ? 'text-gray-900' : 'text-white';
    }
  };

  const getShadowClasses = () => {
    if (color === 'gradient') {
      return ''; // No shadow for gradient text as it interferes with bg-clip-text
    }

    if (theme === 'light') {
      switch (intensity) {
        case 'light':
          return 'text-shadow-sm';
        case 'medium':
          return 'text-shadow-md';
        case 'strong':
          return 'text-shadow-3d-light';
        default:
          return 'text-shadow-md';
      }
    } else {
      switch (intensity) {
        case 'light':
          return 'text-shadow-sm';
        case 'medium':
          return color === 'green' ? 'text-shadow-3d-green' : 'text-shadow-lg';
        case 'strong':
          return color === 'green' ? 'text-shadow-3d-green' : 'text-shadow-3d';
        default:
          return 'text-shadow-lg';
      }
    }
  };

  const classes = `
    ${getVariantClasses()}
    ${getColorClasses()}
    ${getShadowClasses()}
    ${className}
  `.trim();

  const animationProps = animate ? {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6, ease: "easeOut" }
  } : {};

  return (
    <motion.div {...animationProps}>
      <Component className={classes}>
        {children}
      </Component>
    </motion.div>
  );
}

// Specialized text components
export function HeroText3D(props: Omit<Text3DProps, 'variant'>) {
  return <Text3D {...props} variant="hero" />;
}

export function Heading3D(props: Omit<Text3DProps, 'variant'>) {
  return <Text3D {...props} variant="heading" />;
}

export function Subheading3D(props: Omit<Text3DProps, 'variant'>) {
  return <Text3D {...props} variant="subheading" />;
}

// Gradient text component
export function GradientText3D({
  children,
  className = '',
  animate = true,
  ...props
}: Omit<Text3DProps, 'color'>) {
  const animationProps = animate ? {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6, ease: "easeOut" }
  } : {};

  return (
    <motion.div {...animationProps}>
      <Text3D {...props} color="gradient" className={className}>
        {children}
      </Text3D>
    </motion.div>
  );
}

// Animated typing effect
export function TypewriterText3D({
  text,
  delay = 0,
  speed = 0.05,
  ...props
}: Omit<Text3DProps, 'children'> & {
  text: string;
  delay?: number;
  speed?: number;
}) {
  const [displayText, setDisplayText] = React.useState('');

  React.useEffect(() => {
    const timer = setTimeout(() => {
      let i = 0;
      const typeTimer = setInterval(() => {
        if (i < text.length) {
          setDisplayText(text.slice(0, i + 1));
          i++;
        } else {
          clearInterval(typeTimer);
        }
      }, speed * 1000);

      return () => clearInterval(typeTimer);
    }, delay * 1000);

    return () => clearTimeout(timer);
  }, [text, delay, speed]);

  return (
    <Text3D {...props} animate={false}>
      {displayText}
      <motion.span
        animate={{ opacity: [1, 0] }}
        transition={{ duration: 0.8, repeat: Infinity, repeatType: "reverse" }}
        className="inline-block w-1 h-[1em] bg-current ml-1"
      />
    </Text3D>
  );
}

export default Text3D;
