"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FiPlus, 
  FiEdit3, 
  FiTrash2, 
  FiCheck, 
  FiX, 
  FiCreditCard,
  FiSmartphone,
  FiDollarSign,
  FiStar,
  FiShield
} from 'react-icons/fi';
import { Button } from '../ui/Button';
import { Card3D } from '../ui/Card3D';
import { WithdrawalAccount } from '../../types/automaticWithdrawal';
import { automaticWithdrawalService } from '../../services/automaticWithdrawalService';
import AddWithdrawalAccountForm from './AddWithdrawalAccountForm';

interface WithdrawalAccountManagerProps {
  onAccountSelect?: (account: WithdrawalAccount) => void;
  selectedAccountId?: string;
  showAddButton?: boolean;
}

export default function WithdrawalAccountManager({
  onAccountSelect,
  selectedAccountId,
  showAddButton = true
}: WithdrawalAccountManagerProps) {
  const [accounts, setAccounts] = useState<WithdrawalAccount[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingAccount, setEditingAccount] = useState<WithdrawalAccount | null>(null);
  const [deletingAccountId, setDeletingAccountId] = useState<string | null>(null);

  useEffect(() => {
    loadAccounts();
  }, []);

  const loadAccounts = async () => {
    try {
      setLoading(true);
      const accountsData = await automaticWithdrawalService.getWithdrawalAccounts();
      setAccounts(accountsData);
    } catch (error) {
      console.error('Failed to load withdrawal accounts:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAccountCreated = (newAccount: WithdrawalAccount) => {
    setAccounts(prev => [...prev, newAccount]);
    setShowAddForm(false);
  };

  const handleSetDefault = async (accountId: string) => {
    try {
      await automaticWithdrawalService.setDefaultAccount(accountId);
      setAccounts(prev => prev.map(account => ({
        ...account,
        isDefault: account.id === accountId
      })));
    } catch (error) {
      console.error('Failed to set default account:', error);
    }
  };

  const handleDeleteAccount = async (accountId: string) => {
    try {
      setDeletingAccountId(accountId);
      await automaticWithdrawalService.deleteWithdrawalAccount(accountId);
      setAccounts(prev => prev.filter(account => account.id !== accountId));
    } catch (error) {
      console.error('Failed to delete account:', error);
    } finally {
      setDeletingAccountId(null);
    }
  };

  const getAccountIcon = (type: string) => {
    switch (type) {
      case 'bank_account':
        return FiCreditCard;
      case 'mobile_money':
        return FiSmartphone;
      case 'crypto_wallet':
        return FiDollarSign;
      default:
        return FiCreditCard;
    }
  };

  const getAccountDisplayName = (account: WithdrawalAccount) => {
    if (account.type === 'bank_account' && account.bankDetails) {
      return `${account.bankDetails.bankName} - ${account.bankDetails.accountNumber.slice(-4)}`;
    }
    if (account.type === 'mobile_money' && account.mobileMoneyDetails) {
      return `${account.mobileMoneyDetails.provider.toUpperCase()} - ${account.mobileMoneyDetails.phoneNumber}`;
    }
    if (account.type === 'crypto_wallet' && account.cryptoDetails) {
      return `${account.cryptoDetails.currency.toUpperCase()} Wallet`;
    }
    return 'Unknown Account';
  };

  const getAccountSubtitle = (account: WithdrawalAccount) => {
    if (account.type === 'bank_account' && account.bankDetails) {
      return account.bankDetails.accountName;
    }
    if (account.type === 'mobile_money' && account.mobileMoneyDetails) {
      return account.mobileMoneyDetails.accountName;
    }
    if (account.type === 'crypto_wallet' && account.cryptoDetails) {
      return `${account.cryptoDetails.walletAddress.slice(0, 8)}...${account.cryptoDetails.walletAddress.slice(-8)}`;
    }
    return '';
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map(i => (
          <Card3D key={i} className="p-6 animate-pulse">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-theme-secondary rounded-lg"></div>
              <div className="flex-1">
                <div className="h-4 bg-theme-secondary rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-theme-secondary rounded w-1/2"></div>
              </div>
            </div>
          </Card3D>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-bold text-theme font-inter">Withdrawal Accounts</h3>
          <p className="text-theme-secondary font-inter">Manage your withdrawal destinations</p>
        </div>
        {showAddButton && (
          <Button
            onClick={() => setShowAddForm(true)}
            leftIcon={FiPlus}
            className="font-inter"
          >
            Add Account
          </Button>
        )}
      </div>

      {/* Accounts List */}
      <div className="space-y-4">
        <AnimatePresence>
          {accounts.map((account) => {
            const IconComponent = getAccountIcon(account.type);
            const isSelected = selectedAccountId === account.id;
            
            return (
              <motion.div
                key={account.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
              >
                <Card3D 
                  className={`p-6 cursor-pointer transition-all duration-300 ${
                    isSelected 
                      ? 'ring-2 ring-brand bg-brand/5' 
                      : 'hover:bg-theme-secondary/50'
                  }`}
                  onClick={() => onAccountSelect?.(account)}
                  elevation={isSelected ? 3 : 1}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      {/* Account Icon */}
                      <div className={`p-3 rounded-lg ${
                        account.isVerified 
                          ? 'bg-green-500/20 text-green-400' 
                          : 'bg-yellow-500/20 text-yellow-400'
                      }`}>
                        <IconComponent className="w-6 h-6" />
                      </div>

                      {/* Account Details */}
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <h4 className="font-semibold text-theme font-inter">
                            {getAccountDisplayName(account)}
                          </h4>
                          {account.isDefault && (
                            <div className="flex items-center space-x-1 px-2 py-1 bg-brand/20 rounded-full">
                              <FiStar className="w-3 h-3 text-brand" />
                              <span className="text-xs text-brand font-medium font-inter">Default</span>
                            </div>
                          )}
                          {account.isVerified ? (
                            <div className="flex items-center space-x-1 px-2 py-1 bg-green-500/20 rounded-full">
                              <FiShield className="w-3 h-3 text-green-400" />
                              <span className="text-xs text-green-400 font-medium font-inter">Verified</span>
                            </div>
                          ) : (
                            <div className="flex items-center space-x-1 px-2 py-1 bg-yellow-500/20 rounded-full">
                              <FiX className="w-3 h-3 text-yellow-400" />
                              <span className="text-xs text-yellow-400 font-medium font-inter">Pending</span>
                            </div>
                          )}
                        </div>
                        <p className="text-theme-secondary text-sm font-inter mt-1">
                          {getAccountSubtitle(account)}
                        </p>
                        <p className="text-theme-secondary text-xs font-inter mt-1">
                          Added {new Date(account.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center space-x-2">
                      {!account.isDefault && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleSetDefault(account.id);
                          }}
                          className="font-inter"
                        >
                          Set Default
                        </Button>
                      )}
                      
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={(e) => {
                          e.stopPropagation();
                          setEditingAccount(account);
                        }}
                        leftIcon={FiEdit3}
                        className="font-inter"
                      >
                        Edit
                      </Button>
                      
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteAccount(account.id);
                        }}
                        leftIcon={FiTrash2}
                        loading={deletingAccountId === account.id}
                        className="font-inter text-red-400 border-red-400 hover:bg-red-400/10"
                      >
                        Delete
                      </Button>
                    </div>
                  </div>
                </Card3D>
              </motion.div>
            );
          })}
        </AnimatePresence>

        {accounts.length === 0 && (
          <Card3D className="p-12 text-center">
            <FiCreditCard className="w-16 h-16 text-theme-secondary mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-theme font-inter mb-2">No Withdrawal Accounts</h3>
            <p className="text-theme-secondary font-inter mb-6">
              Add a withdrawal account to start receiving automatic transfers
            </p>
            <Button
              onClick={() => setShowAddForm(true)}
              leftIcon={FiPlus}
              className="font-inter"
            >
              Add Your First Account
            </Button>
          </Card3D>
        )}
      </div>

      {/* Add Account Modal */}
      <AnimatePresence>
        {showAddForm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowAddForm(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-theme rounded-2xl p-6 w-full max-w-md max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-theme font-inter">Add Withdrawal Account</h3>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setShowAddForm(false)}
                  leftIcon={FiX}
                  className="font-inter"
                >
                  Close
                </Button>
              </div>
              
              <AddWithdrawalAccountForm
                onAccountCreated={handleAccountCreated}
                onCancel={() => setShowAddForm(false)}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Edit Account Modal */}
      <AnimatePresence>
        {editingAccount && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setEditingAccount(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-theme rounded-2xl p-6 w-full max-w-md max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-theme font-inter">Edit Account</h3>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setEditingAccount(null)}
                  leftIcon={FiX}
                  className="font-inter"
                >
                  Close
                </Button>
              </div>
              
              <AddWithdrawalAccountForm
                editingAccount={editingAccount}
                onAccountCreated={(updatedAccount) => {
                  setAccounts(prev => prev.map(account => 
                    account.id === updatedAccount.id ? updatedAccount : account
                  ));
                  setEditingAccount(null);
                }}
                onCancel={() => setEditingAccount(null)}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
