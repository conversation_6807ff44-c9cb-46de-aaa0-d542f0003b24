export interface Deposit {
  id: string;
  userId: string;
  amount: number;
  currency: string;
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  paymentMethod: 'CARD' | 'BANK_TRANSFER' | 'USSD' | 'QR_CODE' | 'MOBILE_MONEY';
  reference: string;
  paystackReference?: string;
  description?: string;
  metadata?: Record<string, any>;
  fees: number;
  netAmount: number;
  initiatedAt: string;
  processedAt?: string;
  completedAt?: string;
  failureReason?: string;
  createdAt: string;
  updatedAt: string;
}

export interface InitiateDepositData {
  amount: number;
  currency?: string;
  paymentMethod: 'CARD' | 'BANK_TRANSFER' | 'USSD' | 'QR_CODE' | 'MOBILE_MONEY';
  description?: string;
  metadata?: Record<string, any>;
  callbackUrl?: string;
}

export interface InitiateDepositResponse {
  deposit: Deposit;
  paymentData: {
    authorizationUrl?: string;
    accessCode?: string;
    reference: string;
    qrCodeUrl?: string;
    ussdCode?: string;
    bankDetails?: {
      accountNumber: string;
      bankName: string;
      accountName: string;
    };
  };
}

export interface VerifyDepositData {
  reference: string;
  paystackReference?: string;
}

export interface VerifyDepositResponse {
  deposit: Deposit;
  isSuccessful: boolean;
  message: string;
}

export interface DepositStats {
  totalDeposits: number;
  totalAmount: number;
  successfulDeposits: number;
  failedDeposits: number;
  pendingDeposits: number;
  averageDepositAmount: number;
  depositsThisMonth: number;
  amountThisMonth: number;
  topPaymentMethods: PaymentMethodStats[];
}

export interface PaymentMethodStats {
  method: string;
  count: number;
  amount: number;
  percentage: number;
}

export interface DepositSearchFilters {
  search?: string;
  status?: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  paymentMethod?: 'CARD' | 'BANK_TRANSFER' | 'USSD' | 'QR_CODE' | 'MOBILE_MONEY';
  minAmount?: number;
  maxAmount?: number;
  dateFrom?: string;
  dateTo?: string;
  userId?: string;
  sortBy?: 'createdAt' | 'amount' | 'status';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface PaginatedDepositResponse {
  deposits: Deposit[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  stats: {
    totalAmount: number;
    successfulCount: number;
    failedCount: number;
    pendingCount: number;
  };
}

export interface PaystackWebhookData {
  event: string;
  data: {
    id: number;
    domain: string;
    status: string;
    reference: string;
    amount: number;
    message: string;
    gateway_response: string;
    paid_at: string;
    created_at: string;
    channel: string;
    currency: string;
    ip_address: string;
    metadata: Record<string, any>;
    fees: number;
    customer: {
      id: number;
      first_name: string;
      last_name: string;
      email: string;
      phone: string;
    };
    authorization: {
      authorization_code: string;
      bin: string;
      last4: string;
      exp_month: string;
      exp_year: string;
      channel: string;
      card_type: string;
      bank: string;
      country_code: string;
      brand: string;
    };
  };
}

export interface DepositFormData {
  amount: string;
  paymentMethod: 'CARD' | 'BANK_TRANSFER' | 'USSD' | 'QR_CODE' | 'MOBILE_MONEY';
  description: string;
}
