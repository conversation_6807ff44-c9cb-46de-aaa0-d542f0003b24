import { 
  TargetSavings, 
  TargetMilestone,
  TargetReminder,
  CreateTargetSavingsData, 
  UpdateTargetSavingsData,
  TargetContributionData,
  TargetSearchFilters,
  TargetStats,
  TargetProgress,
  ApiResponse
} from '../types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

class TargetSavingsService {
  private getAuthHeaders() {
    const token = localStorage.getItem('auth_token');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` })
    };
  }

  // Target Savings CRUD
  async getTargetSavings(): Promise<{ targets: TargetSavings[] }> {
    // Backend route: /api/target-savings (GET)
    const response = await fetch(`${API_BASE_URL}/api/target-savings`, {
      headers: this.getAuthHeaders()
    });
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to fetch target savings');
    }
    // The backend returns an array, wrap in { targets }
    const data = await response.json();
    return { targets: Array.isArray(data) ? data : [] };
  }

  async getTargetSavingsById(targetId: string): Promise<TargetSavings> {
    const response = await fetch(`${API_BASE_URL}/target-savings/${targetId}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch target savings');
    }

    return response.json();
  }

  async createTargetSavings(data: CreateTargetSavingsData): Promise<TargetSavings> {
    // Backend expects: goalName, targetAmount, timelineMonths, frequency
    // Map frontend fields to backend fields
    const mapped = {
      goalName: data.title || '',
      targetAmount: data.targetAmount,
      timelineMonths: (data.targetDate && data.targetAmount && data.contributionAmount)
        ? Math.max(1, Math.round((Number(data.targetAmount) / Number(data.contributionAmount))))
        : 1,
      frequency: (data.contributionFrequency || 'MONTHLY').toLowerCase(),
    };
    const response = await fetch(`${API_BASE_URL}/api/target-savings`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(mapped)
    });
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to create target savings');
    }
    return response.json();
  }

  async updateTargetSavings(targetId: string, data: UpdateTargetSavingsData): Promise<TargetSavings> {
    const response = await fetch(`${API_BASE_URL}/target-savings/${targetId}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update target savings');
    }

    return response.json();
  }

  async deleteTargetSavings(targetId: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/target-savings/${targetId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to delete target savings');
    }
  }

  async pauseTargetSavings(targetId: string): Promise<TargetSavings> {
    const response = await fetch(`${API_BASE_URL}/target-savings/${targetId}/pause`, {
      method: 'POST',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to pause target savings');
    }

    return response.json();
  }

  async resumeTargetSavings(targetId: string): Promise<TargetSavings> {
    const response = await fetch(`${API_BASE_URL}/target-savings/${targetId}/resume`, {
      method: 'POST',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to resume target savings');
    }

    return response.json();
  }

  // Contributions
  async makeContribution(data: TargetContributionData): Promise<any> {
    const response = await fetch(`${API_BASE_URL}/target-savings/${data.targetId}/contribute`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({
        amount: data.amount,
        paymentMethod: data.paymentMethod,
        description: data.description
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to make contribution');
    }

    return response.json();
  }

  async getContributionHistory(targetId: string, filters?: {
    dateFrom?: string;
    dateTo?: string;
    page?: number;
    limit?: number;
  }): Promise<any> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/target-savings/${targetId}/contributions?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch contribution history');
    }

    return response.json();
  }

  // Milestones
  async addMilestone(targetId: string, milestone: Omit<TargetMilestone, 'id' | 'targetId' | 'isCompleted' | 'completedAt'>): Promise<TargetMilestone> {
    const response = await fetch(`${API_BASE_URL}/target-savings/${targetId}/milestones`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(milestone)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to add milestone');
    }

    return response.json();
  }

  async updateMilestone(targetId: string, milestoneId: string, data: Partial<TargetMilestone>): Promise<TargetMilestone> {
    const response = await fetch(`${API_BASE_URL}/target-savings/${targetId}/milestones/${milestoneId}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update milestone');
    }

    return response.json();
  }

  async deleteMilestone(targetId: string, milestoneId: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/target-savings/${targetId}/milestones/${milestoneId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to delete milestone');
    }
  }

  async completeMilestone(targetId: string, milestoneId: string): Promise<TargetMilestone> {
    const response = await fetch(`${API_BASE_URL}/target-savings/${targetId}/milestones/${milestoneId}/complete`, {
      method: 'POST',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to complete milestone');
    }

    return response.json();
  }

  // Reminders
  async addReminder(targetId: string, reminder: Omit<TargetReminder, 'id' | 'targetId' | 'isSent' | 'sentAt'>): Promise<TargetReminder> {
    const response = await fetch(`${API_BASE_URL}/target-savings/${targetId}/reminders`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(reminder)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to add reminder');
    }

    return response.json();
  }

  async updateReminder(targetId: string, reminderId: string, data: Partial<TargetReminder>): Promise<TargetReminder> {
    const response = await fetch(`${API_BASE_URL}/target-savings/${targetId}/reminders/${reminderId}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update reminder');
    }

    return response.json();
  }

  async deleteReminder(targetId: string, reminderId: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/target-savings/${targetId}/reminders/${reminderId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to delete reminder');
    }
  }

  // Statistics and Analytics
  async getTargetStats(): Promise<TargetStats> {
    const response = await fetch(`${API_BASE_URL}/target-savings/stats`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch target statistics');
    }

    return response.json();
  }

  async getTargetProgress(targetId: string): Promise<TargetProgress> {
    const response = await fetch(`${API_BASE_URL}/target-savings/${targetId}/progress`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch target progress');
    }

    return response.json();
  }

  // Utility Methods
  calculateProgress(current: number, target: number): number {
    return Math.min((current / target) * 100, 100);
  }

  calculateDaysRemaining(targetDate: string): number {
    const target = new Date(targetDate);
    const now = new Date();
    const diffTime = target.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  calculateRecommendedMonthlyContribution(
    targetAmount: number,
    currentAmount: number,
    targetDate: string
  ): number {
    const remaining = targetAmount - currentAmount;
    const daysRemaining = this.calculateDaysRemaining(targetDate);
    const monthsRemaining = Math.max(daysRemaining / 30, 1);
    
    return remaining / monthsRemaining;
  }

  isOnTrack(
    targetAmount: number,
    currentAmount: number,
    targetDate: string,
    startDate: string
  ): boolean {
    const totalDays = new Date(targetDate).getTime() - new Date(startDate).getTime();
    const daysPassed = Date.now() - new Date(startDate).getTime();
    const expectedProgress = (daysPassed / totalDays) * targetAmount;
    
    return currentAmount >= expectedProgress * 0.9; // 90% tolerance
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN'
    }).format(amount);
  }

  getCategoryIcon(category: string): string {
    const icons: Record<string, string> = {
      EMERGENCY: '🚨',
      VACATION: '✈️',
      EDUCATION: '🎓',
      HOUSE: '🏠',
      CAR: '🚗',
      BUSINESS: '💼',
      WEDDING: '💒',
      HEALTH: '🏥',
      OTHER: '🎯'
    };
    
    return icons[category] || '🎯';
  }

  getPriorityColor(priority: string): string {
    const colors: Record<string, string> = {
      LOW: '#10B981',
      MEDIUM: '#F59E0B',
      HIGH: '#EF4444'
    };
    
    return colors[priority] || '#6B7280';
  }
}

export const targetSavingsService = new TargetSavingsService();
