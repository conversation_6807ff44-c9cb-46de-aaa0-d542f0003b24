const express = require('express');
const router = express.Router();
const fetch = require('node-fetch');

// GET /api/paystack/account-verify?account_number=xxxx&bank_code=yyyy
router.get('/account-verify', async (req, res) => {
  const { account_number, bank_code } = req.query;
  if (!account_number || !bank_code) {
    return res.status(400).json({ status: false, message: 'Missing account_number or bank_code' });
  }
  try {
    const PAYSTACK_SECRET_KEY = process.env.PAYSTACK_SECRET_KEY;
    const PAYSTACK_BASE_URL = process.env.PAYSTACK_BASE_URL || 'https://api.paystack.co';
    const url = `${PAYSTACK_BASE_URL}/bank/resolve?account_number=${account_number}&bank_code=${bank_code}`;
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${PAYSTACK_SECRET_KEY}`,
        'Content-Type': 'application/json',
      },
    });
    const result = await response.json();
    if (!response.ok) {
      return res.status(response.status).json({ status: false, message: result.message || 'Paystack error', data: result });
    }
    return res.json(result);
  } catch (err) {
    return res.status(500).json({ status: false, message: 'Server error', error: err.message });
  }
});

module.exports = router;
