"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FiSave,
  FiRefreshCw,
  FiTrendingUp,
  FiPercent,
  FiCalendar,
  FiDollarSign,
  FiEdit,
  FiPlus,
  FiTrash2,
} from 'react-icons/fi';
import AdminLayout from '../../../src/components/admin/AdminLayout';
import { Card3D } from '../../../src/components/ui/Card3D';
import { PrimaryButton, OutlineButton } from '../../../src/components/ui/AnimatedButton';
import { showToast } from '../../../src/components/ui/Toast';

interface InterestRate {
  id: string;
  planType: string;
  duration: number; // in months
  minAmount: number;
  maxAmount: number;
  rate: number; // percentage
  compoundingFrequency: 'daily' | 'monthly' | 'quarterly' | 'annually';
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

const mockInterestRates: InterestRate[] = [
  {
    id: '1',
    planType: 'Basic Savings',
    duration: 6,
    minAmount: 10000,
    maxAmount: 100000,
    rate: 8.5,
    compoundingFrequency: 'monthly',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '2',
    planType: 'Premium Savings',
    duration: 12,
    minAmount: 100000,
    maxAmount: 1000000,
    rate: 12.0,
    compoundingFrequency: 'monthly',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '3',
    planType: 'Elite Savings',
    duration: 24,
    minAmount: 1000000,
    maxAmount: 10000000,
    rate: 15.0,
    compoundingFrequency: 'quarterly',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }
];

export default function InterestRateSettingsPage() {
  const [interestRates, setInterestRates] = useState<InterestRate[]>(mockInterestRates);
  const [editingRate, setEditingRate] = useState<InterestRate | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);

  const [formData, setFormData] = useState({
    planType: '',
    duration: 6,
    minAmount: 10000,
    maxAmount: 100000,
    rate: 8.0,
    compoundingFrequency: 'monthly' as const,
    isActive: true
  });

  const handleSaveRate = async () => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (editingRate) {
        // Update existing rate
        setInterestRates(prev =>
          prev.map(rate =>
            rate.id === editingRate.id
              ? { ...rate, ...formData, updatedAt: new Date().toISOString() }
              : rate
          )
        );
        showToast.success('Interest rate updated successfully');
        setEditingRate(null);
      } else {
        // Add new rate
        const newRate: InterestRate = {
          id: Date.now().toString(),
          ...formData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        setInterestRates(prev => [...prev, newRate]);
        showToast.success('Interest rate added successfully');
        setShowAddForm(false);
      }

      // Reset form
      setFormData({
        planType: '',
        duration: 6,
        minAmount: 10000,
        maxAmount: 100000,
        rate: 8.0,
        compoundingFrequency: 'monthly',
        isActive: true
      });
    } catch (error) {
      showToast.error('Failed to save interest rate');
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditRate = (rate: InterestRate) => {
    setEditingRate(rate);
    setFormData({
      planType: rate.planType,
      duration: rate.duration,
      minAmount: rate.minAmount,
      maxAmount: rate.maxAmount,
      rate: rate.rate,
      compoundingFrequency: rate.compoundingFrequency,
      isActive: rate.isActive
    });
    setShowAddForm(true);
  };

  const handleDeleteRate = async (rateId: string) => {
    if (!confirm('Are you sure you want to delete this interest rate?')) return;

    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setInterestRates(prev => prev.filter(rate => rate.id !== rateId));
      showToast.success('Interest rate deleted successfully');
    } catch (error) {
      showToast.error('Failed to delete interest rate');
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleActive = async (rateId: string) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setInterestRates(prev =>
        prev.map(rate =>
          rate.id === rateId
            ? { ...rate, isActive: !rate.isActive, updatedAt: new Date().toISOString() }
            : rate
        )
      );
      showToast.success('Interest rate status updated');
    } catch (error) {
      showToast.error('Failed to update interest rate status');
    } finally {
      setIsLoading(false);
    }
  };

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-NG', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Calculate summary stats
  const activeRates = interestRates.filter(rate => rate.isActive).length;
  const averageRate = interestRates.length > 0 
    ? interestRates.reduce((sum, rate) => sum + rate.rate, 0) / interestRates.length 
    : 0;
  const highestRate = Math.max(...interestRates.map(rate => rate.rate));

  return (
    <AdminLayout title="Interest Rate Settings">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white">Interest Rate Settings</h1>
            <p className="text-gray-400 mt-2">Manage interest rates for different savings plans</p>
          </div>
          <div className="flex space-x-2">
            <OutlineButton onClick={() => setShowAddForm(!showAddForm)}>
              <FiPlus className="mr-2" />
              Add Rate
            </OutlineButton>
            <PrimaryButton>
              <FiRefreshCw className="mr-2" />
              Refresh
            </PrimaryButton>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card3D className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Active Rates</p>
                <p className="text-2xl font-bold text-white">{activeRates}</p>
              </div>
              <FiTrendingUp className="text-green-500 text-3xl" />
            </div>
          </Card3D>

          <Card3D className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Average Rate</p>
                <p className="text-2xl font-bold text-white">{averageRate.toFixed(1)}%</p>
              </div>
              <FiPercent className="text-blue-500 text-3xl" />
            </div>
          </Card3D>

          <Card3D className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Highest Rate</p>
                <p className="text-2xl font-bold text-white">{highestRate.toFixed(1)}%</p>
              </div>
              <FiDollarSign className="text-green-500 text-3xl" />
            </div>
          </Card3D>
        </div>

        {/* Add/Edit Form */}
        {showAddForm && (
          <Card3D className="bg-gray-800 border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-white mb-4">
              {editingRate ? 'Edit Interest Rate' : 'Add New Interest Rate'}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Plan Type
                </label>
                <input
                  type="text"
                  value={formData.planType}
                  onChange={(e) => setFormData({ ...formData, planType: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-green-500 focus:outline-none text-white"
                  placeholder="e.g., Basic Savings"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Duration (months)
                </label>
                <input
                  type="number"
                  value={formData.duration}
                  onChange={(e) => setFormData({ ...formData, duration: parseInt(e.target.value) })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-green-500 focus:outline-none text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Minimum Amount (₦)
                </label>
                <input
                  type="number"
                  value={formData.minAmount}
                  onChange={(e) => setFormData({ ...formData, minAmount: parseInt(e.target.value) })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-green-500 focus:outline-none text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Maximum Amount (₦)
                </label>
                <input
                  type="number"
                  value={formData.maxAmount}
                  onChange={(e) => setFormData({ ...formData, maxAmount: parseInt(e.target.value) })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-green-500 focus:outline-none text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Interest Rate (%)
                </label>
                <input
                  type="number"
                  step="0.1"
                  value={formData.rate}
                  onChange={(e) => setFormData({ ...formData, rate: parseFloat(e.target.value) })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-green-500 focus:outline-none text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Compounding Frequency
                </label>
                <select
                  value={formData.compoundingFrequency}
                  onChange={(e) => setFormData({ ...formData, compoundingFrequency: e.target.value as any })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-green-500 focus:outline-none text-white"
                >
                  <option value="daily">Daily</option>
                  <option value="monthly">Monthly</option>
                  <option value="quarterly">Quarterly</option>
                  <option value="annually">Annually</option>
                </select>
              </div>
            </div>

            <div className="flex items-center mt-4">
              <input
                type="checkbox"
                id="isActive"
                checked={formData.isActive}
                onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                className="mr-2"
              />
              <label htmlFor="isActive" className="text-sm text-gray-300">
                Active
              </label>
            </div>

            <div className="flex space-x-2 mt-6">
              <PrimaryButton
                onClick={handleSaveRate}
                disabled={isLoading || !formData.planType}
              >
                <FiSave className="mr-2" />
                {editingRate ? 'Update' : 'Save'} Rate
              </PrimaryButton>
              <OutlineButton
                onClick={() => {
                  setShowAddForm(false);
                  setEditingRate(null);
                  setFormData({
                    planType: '',
                    duration: 6,
                    minAmount: 10000,
                    maxAmount: 100000,
                    rate: 8.0,
                    compoundingFrequency: 'monthly',
                    isActive: true
                  });
                }}
              >
                Cancel
              </OutlineButton>
            </div>
          </Card3D>
        )}

        {/* Interest Rates Table */}
        <Card3D className="bg-gray-800 border-gray-700 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Plan Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Duration
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Amount Range
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Interest Rate
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Compounding
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-700">
                {interestRates.map((rate) => (
                  <motion.tr
                    key={rate.id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="hover:bg-gray-700/50"
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-white">{rate.planType}</div>
                      <div className="text-sm text-gray-400">
                        Updated {formatDate(rate.updatedAt)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-white">
                        <FiCalendar className="mr-2 text-gray-400" />
                        {rate.duration} months
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-white">
                        {formatAmount(rate.minAmount)} - {formatAmount(rate.maxAmount)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm font-medium text-green-400">
                        <FiPercent className="mr-1" />
                        {rate.rate}%
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-300 capitalize">
                        {rate.compoundingFrequency}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <button
                        onClick={() => handleToggleActive(rate.id)}
                        disabled={isLoading}
                        className={`px-2 py-1 text-xs rounded-full ${
                          rate.isActive
                            ? 'bg-green-600 text-white'
                            : 'bg-gray-600 text-gray-300'
                        }`}
                      >
                        {rate.isActive ? 'Active' : 'Inactive'}
                      </button>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleEditRate(rate)}
                          className="text-blue-400 hover:text-blue-300"
                        >
                          <FiEdit />
                        </button>
                        <button
                          onClick={() => handleDeleteRate(rate.id)}
                          disabled={isLoading}
                          className="text-red-400 hover:text-red-300 disabled:opacity-50"
                        >
                          <FiTrash2 />
                        </button>
                      </div>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        </Card3D>
      </div>
    </AdminLayout>
  );
}
