"use client";

import { motion } from 'framer-motion';
import Image from 'next/image';

interface BetterInterestLogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'light' | 'dark' | 'gradient';
  showIcon?: boolean;
  className?: string;
}

const sizeClasses = {
  sm: {
    container: 'h-8',
    text: 'text-lg',
    icon: 'w-6 h-6'
  },
  md: {
    container: 'h-10',
    text: 'text-xl',
    icon: 'w-8 h-8'
  },
  lg: {
    container: 'h-12',
    text: 'text-2xl',
    icon: 'w-10 h-10'
  },
  xl: {
    container: 'h-16',
    text: 'text-3xl',
    icon: 'w-12 h-12'
  }
};

const variantClasses = {
  light: 'text-white',
  dark: 'text-gray-900',
  gradient: 'bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent'
};

export function BetterInterestLogo({ 
  size = 'md', 
  variant = 'gradient', 
  showIcon = true,
  className = '' 
}: BetterInterestLogoProps) {
  const sizeConfig = sizeClasses[size];
  const textColor = variantClasses[variant];

  return (
    <motion.div 
      className={`flex items-center space-x-2 ${sizeConfig.container} ${className}`}
      whileHover={{ scale: 1.05 }}
      transition={{ type: "spring", stiffness: 400, damping: 10 }}
    >
      {showIcon && (
        <motion.div
          className={`${sizeConfig.icon} flex items-center justify-center relative`}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.95 }}
          transition={{ type: "spring", stiffness: 400, damping: 10 }}
        >
          <Image
            src="/images/logo-text.png"
            alt="Better Interest Logo"
            width={parseInt(sizeConfig.icon.split(' ')[0].replace('w-', '')) * 4}
            height={parseInt(sizeConfig.icon.split(' ')[1].replace('h-', '')) * 4}
            className={`${sizeConfig.icon} object-contain`}
            priority
          />
        </motion.div>
      )}
      
      <div className="flex flex-col">
        <motion.h1
          className={`font-inter font-bold leading-tight ${sizeConfig.text} ${textColor} relative`}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
          style={{
            letterSpacing: '0.02em',
            textShadow: '0 0 20px rgba(255, 255, 255, 0.1)'
          }}
        >
          <span
            className="text-white relative"
            style={{
              textShadow: '0 0 15px rgba(255, 255, 255, 0.2)',
              fontWeight: '700'
            }}
          >
            Better
          </span>
          <span
            className="text-green-400 ml-1 relative"
            style={{
              textShadow: '0 0 20px rgba(34, 197, 94, 0.4), 0 0 40px rgba(34, 197, 94, 0.2)',
              fontWeight: '800',
              letterSpacing: '0.05em'
            }}
          >
            Interest
          </span>
        </motion.h1>
        
        {size === 'lg' || size === 'xl' ? (
          <motion.p 
            className="text-xs text-gray-400 -mt-1"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            Smart Savings Platform
          </motion.p>
        ) : null}
      </div>
    </motion.div>
  );
}

// Simplified text-only logo
export function BetterInterestTextLogo({ 
  size = 'md', 
  variant = 'gradient',
  className = '' 
}: Omit<BetterInterestLogoProps, 'showIcon'>) {
  const sizeConfig = sizeClasses[size];
  const textColor = variantClasses[variant];

  return (
    <motion.div 
      className={`${className}`}
      whileHover={{ scale: 1.05 }}
      transition={{ type: "spring", stiffness: 400, damping: 10 }}
    >
      <motion.h1 
        className={`font-bold ${sizeConfig.text} ${textColor}`}
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5 }}
      >
        Better<span className="text-green-500">Interest</span>
      </motion.h1>
    </motion.div>
  );
}

// Icon-only logo
export function BetterInterestIcon({ 
  size = 'md',
  className = '' 
}: Pick<BetterInterestLogoProps, 'size' | 'className'>) {
  const sizeConfig = sizeClasses[size];

  return (
    <motion.div
      className={`${sizeConfig.icon} flex items-center justify-center ${className}`}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.95 }}
      transition={{ type: "spring", stiffness: 400, damping: 10 }}
    >
      <Image
        src="/images/logo-text.png"
        alt="Better Interest Logo"
        width={parseInt(sizeConfig.icon.split(' ')[0].replace('w-', '')) * 4}
        height={parseInt(sizeConfig.icon.split(' ')[1].replace('h-', '')) * 4}
        className={`${sizeConfig.icon} object-contain`}
        priority
      />
    </motion.div>
  );
}
