"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { useTheme, getThemeClasses } from '../../contexts/ThemeContext';

interface ThemedButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  href?: string;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
  icon?: React.ReactNode;
  fullWidth?: boolean;
}

const ArrowIcon = () => (
  <svg width={34} height={34} viewBox="0 0 74 74" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx={37} cy={37} r="35.5" stroke="currentColor" strokeWidth={3} />
    <path d="M25 35.5C24.1716 35.5 23.5 36.1716 23.5 37C23.5 37.8284 24.1716 38.5 25 38.5V35.5ZM49.0607 38.0607C49.6464 37.4749 49.6464 36.5251 49.0607 35.9393L39.5147 26.3934C38.9289 25.8076 37.9792 25.8076 37.3934 26.3934C36.8076 26.9792 36.8076 27.9289 37.3934 28.5147L45.8787 37L37.3934 45.4853C36.8076 46.0711 36.8076 47.0208 37.3934 47.6066C37.9792 48.1924 38.9289 48.1924 39.5147 47.6066L49.0607 38.0607ZM25 38.5L48 38.5V35.5L25 35.5V38.5Z" fill="currentColor" />
  </svg>
);

export function ThemedButton({
  children,
  onClick,
  href,
  variant = 'primary',
  size = 'md',
  disabled = false,
  className = '',
  type = 'button',
  icon,
  fullWidth = false
}: ThemedButtonProps) {
  const { theme } = useTheme();
  const themeClasses = getThemeClasses(theme);

  const baseClasses = `
    cursor-pointer font-semibold font-inter transition-all duration-300 transform hover:scale-105
    border border-transparent flex items-center justify-center
    rounded-xl relative overflow-hidden group shadow-lg
    ${fullWidth ? 'w-full' : ''}
    ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
  `;

  const variantClasses = {
    primary: themeClasses.button.primary,
    secondary: themeClasses.button.secondary,
    outline: themeClasses.button.outline
  };

  const sizeClasses = {
    sm: 'px-4 py-[7px] text-sm',     // Reduced from py-2 (8px) to 7px (-12.5% ≈ 3% height reduction)
    md: 'px-6 py-[11px] text-base',  // Reduced from py-3 (12px) to 11px (-8.3% ≈ 3% height reduction)
    lg: 'px-8 py-[15px] text-lg'     // Reduced from py-4 (16px) to 15px (-6.25% ≈ 3% height reduction)
  };

  const buttonClasses = `
    ${baseClasses}
    ${variantClasses[variant]}
    ${sizeClasses[size]}
    ${className}
  `;

  const ButtonContent = () => (
    <>
      {/* Background glow effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-green-400/20 to-green-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full blur-xl"></div>
      
      {/* Button content */}
      <span className="relative z-10 flex items-center">
        <span className="text">{children}</span>
        {(icon || variant === 'primary') && (
          <motion.div
            className="ml-2 text-current"
            whileHover={{ x: 5 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          >
            {icon || <ArrowIcon />}
          </motion.div>
        )}
      </span>
    </>
  );

  if (href) {
    return (
      <motion.a
        href={href}
        className={buttonClasses}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.95 }}
        transition={{ type: "spring", stiffness: 400, damping: 10 }}
      >
        <ButtonContent />
      </motion.a>
    );
  }

  return (
    <motion.button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={buttonClasses}
      whileHover={{ scale: disabled ? 1 : 1.02 }}
      whileTap={{ scale: disabled ? 1 : 0.95 }}
      transition={{ type: "spring", stiffness: 400, damping: 10 }}
    >
      <ButtonContent />
    </motion.button>
  );
}

// Specialized button variants
export function PrimaryButton(props: Omit<ThemedButtonProps, 'variant'>) {
  return <ThemedButton {...props} variant="primary" />;
}

export function SecondaryButton(props: Omit<ThemedButtonProps, 'variant'>) {
  return <ThemedButton {...props} variant="secondary" />;
}

export function OutlineButton(props: Omit<ThemedButtonProps, 'variant'>) {
  return <ThemedButton {...props} variant="outline" />;
}

// Enhanced Material Design Theme Toggle Button
export function ThemeToggleButton({ className = '', variant = 'icon' }: {
  className?: string;
  variant?: 'icon' | 'switch' | 'fab';
}) {
  const { theme, toggleTheme } = useTheme();
  const themeClasses = getThemeClasses(theme);

  if (variant === 'switch') {
    return (
      <div className={`flex items-center space-x-3 ${className}`}>
        <span className={`text-sm font-medium ${themeClasses.text.secondary}`}>
          {theme === 'light' ? 'Light' : 'Dark'}
        </span>
        <motion.button
          onClick={toggleTheme}
          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 ${
            theme === 'light'
              ? 'bg-gray-200 focus:ring-offset-white'
              : 'bg-green-600 focus:ring-offset-gray-900'
          }`}
          whileTap={{ scale: 0.95 }}
          title={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
        >
          <motion.span
            className={`inline-block h-4 w-4 transform rounded-full bg-white shadow-lg transition-transform ${
              theme === 'light' ? 'translate-x-1' : 'translate-x-6'
            }`}
            layout
            transition={{ type: "spring", stiffness: 500, damping: 30 }}
          />
        </motion.button>
      </div>
    );
  }

  if (variant === 'fab') {
    return (
      <motion.button
        onClick={toggleTheme}
        className={`fixed bottom-6 right-6 w-14 h-14 rounded-full shadow-lg transition-all duration-300 z-50 ${
          theme === 'light'
            ? 'bg-white text-gray-800 shadow-gray-300 hover:shadow-xl'
            : 'bg-gray-800 text-yellow-400 shadow-gray-900 hover:shadow-2xl'
        }`}
        whileHover={{ scale: 1.1, rotate: 180 }}
        whileTap={{ scale: 0.9 }}
        title={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
        style={{
          boxShadow: theme === 'light'
            ? '0 4px 20px rgba(0, 0, 0, 0.1), 0 8px 40px rgba(0, 0, 0, 0.05)'
            : '0 4px 20px rgba(0, 0, 0, 0.3), 0 8px 40px rgba(34, 197, 94, 0.1)'
        }}
      >
        <motion.div
          initial={false}
          animate={{ rotate: theme === 'light' ? 0 : 180 }}
          transition={{ duration: 0.3 }}
        >
          {theme === 'light' ? (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
            </svg>
          ) : (
            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
          )}
        </motion.div>
      </motion.button>
    );
  }

  // Default icon variant with Material Design ripple effect
  return (
    <motion.button
      onClick={toggleTheme}
      className={`relative p-3 rounded-full transition-all duration-300 overflow-hidden group ${
        theme === 'light'
          ? 'bg-gray-100 hover:bg-gray-200 text-gray-700 hover:text-gray-900'
          : 'bg-gray-800 hover:bg-gray-700 text-gray-300 hover:text-white'
      } ${className}`}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      title={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
      style={{
        boxShadow: theme === 'light'
          ? '0 2px 8px rgba(0, 0, 0, 0.1)'
          : '0 2px 8px rgba(0, 0, 0, 0.3)'
      }}
    >
      {/* Ripple effect */}
      <motion.div
        className="absolute inset-0 bg-green-400 opacity-0 group-hover:opacity-20 rounded-full"
        initial={{ scale: 0 }}
        whileHover={{ scale: 1 }}
        transition={{ duration: 0.3 }}
      />

      <motion.div
        initial={false}
        animate={{ rotate: theme === 'light' ? 0 : 180 }}
        transition={{ duration: 0.5, type: "spring", stiffness: 200 }}
        className="relative z-10"
      >
        {theme === 'light' ? (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
          </svg>
        ) : (
          <svg className="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
          </svg>
        )}
      </motion.div>
    </motion.button>
  );
}

export default ThemedButton;
