import React from "react";
import { cn } from "@/lib/utils";

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "primary" | "secondary" | "outline";
  size?: "sm" | "md" | "lg";
  children: React.ReactNode;
}

export const Button: React.FC<ButtonProps> = ({
  variant = "primary",
  size = "md",
  className,
  children,
  ...props
}) => {
  const baseClasses =
    "font-semibold rounded-xl transition-all duration-300 transform hover:scale-105 btn-hover-effect";

  const variantClasses = {
    primary:
      "bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white hover:shadow-2xl hover:shadow-green-500/25",
    secondary:
      "bg-gray-800 hover:bg-gray-700 text-white border border-gray-600",
    outline:
      "border-2 border-green-500 text-green-400 hover:bg-green-500 hover:text-white",
  };

  const sizeClasses = {
    sm: "px-4 py-2 text-sm",
    md: "px-6 py-3 text-base",
    lg: "px-8 py-4 text-lg",
  };

  return (
    <button
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        className,
      )}
      {...props}
      data-oid="zy-2d1-"
    >
      {children}
    </button>
  );
};
