import { Request, Response, NextFunction } from 'express';
import { config } from '../config/env';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: any;
    }
  }
}

export const authenticateToken = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        success: false,
        error: 'Access token required',
        message: 'Please provide a valid access token'
      });
    }

    // For demo mode, just check if token starts with demo_token_
    if (token.startsWith('demo_token_')) {
      const mockUser = {
        id: 'demo-user-123',
        email: '<EMAIL>',
        role: 'USER',
        isActive: true
      };

      req.user = mockUser;
      return next();
    }

    return res.status(401).json({
      success: false,
      error: 'Invalid token',
      message: 'Invalid access token'
    });
  } catch (error) {
    console.error('Auth middleware error:', error);
    return res.status(500).json({
      success: false,
      error: 'Authentication error',
      message: 'Internal server error during authentication'
    });
  }
};

export const requireAdmin = (req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required',
      message: 'Please authenticate first'
    });
  }

  if (req.user.role !== 'ADMIN') {
    return res.status(403).json({
      success: false,
      error: 'Admin access required',
      message: 'You do not have permission to access this resource'
    });
  }

  return next();
};

export const optionalAuth = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (token && token.startsWith('demo_token_')) {
      const mockUser = {
        id: 'demo-user-123',
        email: '<EMAIL>',
        role: 'USER',
        isActive: true
      };

      req.user = mockUser;
    }

    next();
  } catch (error) {
    // Continue without authentication for optional auth
    next();
  }
};
