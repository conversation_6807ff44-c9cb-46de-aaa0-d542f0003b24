"use client";

import { useState } from 'react';
import {
    FiArrowDownRight,
    FiArrowUpRight,
    FiBarChart,
    FiDollarSign,
    FiPieChart,
    FiPlus,
    FiShield,
    FiTarget,
    FiTrendingUp
} from 'react-icons/fi';
import { StatCard } from '../../../src/components/dashboard/DashboardCard';
import DashboardLayout from '../../../src/components/dashboard/DashboardLayout';
import { Button } from '../../../src/components/ui/Button';
import { Card3D } from '../../../src/components/ui/Card3D';

interface InvestmentOption {
  id: string;
  name: string;
  description: string;
  minimumAmount: number;
  expectedReturn: number;
  riskLevel: 'low' | 'medium' | 'high';
  duration: string;
  category: 'bonds' | 'stocks' | 'mutual_funds' | 'real_estate';
  currentValue?: number;
  invested?: number;
  performance?: number;
}

const mockInvestments: InvestmentOption[] = [
  {
    id: '1',
    name: 'Government Bonds',
    description: 'Secure government-backed bonds with guaranteed returns',
    minimumAmount: 100000,
    expectedReturn: 12.5,
    riskLevel: 'low',
    duration: '1-5 years',
    category: 'bonds',
    invested: 500000,
    currentValue: 562500,
    performance: 12.5
  },
  {
    id: '2',
    name: 'Blue Chip Stocks',
    description: 'Invest in established companies with strong track records',
    minimumAmount: 50000,
    expectedReturn: 18.0,
    riskLevel: 'medium',
    duration: '3-10 years',
    category: 'stocks',
    invested: 300000,
    currentValue: 354000,
    performance: 18.0
  },
  {
    id: '3',
    name: 'Real Estate Fund',
    description: 'Diversified real estate investment trust portfolio',
    minimumAmount: 250000,
    expectedReturn: 15.5,
    riskLevel: 'medium',
    duration: '5-15 years',
    category: 'real_estate',
    invested: 750000,
    currentValue: 866250,
    performance: 15.5
  },
  {
    id: '4',
    name: 'Tech Growth Fund',
    description: 'High-growth technology companies and startups',
    minimumAmount: 75000,
    expectedReturn: 25.0,
    riskLevel: 'high',
    duration: '5-10 years',
    category: 'mutual_funds'
  }
];

export default function InvestmentPage() {
  const [selectedTab, setSelectedTab] = useState<'portfolio' | 'opportunities'>('portfolio');

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-600 bg-green-600/10';
      case 'medium': return 'text-yellow-600 bg-yellow-600/10';
      case 'high': return 'text-red-600 bg-red-600/10';
      default: return 'text-gray-600 bg-gray-600/10';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'bonds': return FiShield;
      case 'stocks': return FiTrendingUp;
      case 'mutual_funds': return FiPieChart;
      case 'real_estate': return FiTarget;
      default: return FiDollarSign;
    }
  };

  const myInvestments = mockInvestments.filter(inv => inv.invested);
  const availableInvestments = mockInvestments.filter(inv => !inv.invested);

  const totalInvested = myInvestments.reduce((sum, inv) => sum + (inv.invested || 0), 0);
  const totalValue = myInvestments.reduce((sum, inv) => sum + (inv.currentValue || 0), 0);
  const totalGains = totalValue - totalInvested;
  const totalReturn = totalInvested > 0 ? ((totalGains / totalInvested) * 100) : 0;

  return (
    <DashboardLayout title="Investment">
      <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-theme font-inter">Investment Portfolio</h1>
            <p className="text-theme-secondary mt-2 font-inter">
              Grow your wealth with smart investment choices
            </p>
          </div>
          <Button
            leftIcon={FiPlus}
            className="font-inter"
          >
            New Investment
          </Button>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="Total Invested"
            value={formatCurrency(totalInvested)}
            subtitle="Principal amount"
            icon={FiDollarSign}
            color="blue"
          />
          <StatCard
            title="Current Value"
            value={formatCurrency(totalValue)}
            subtitle="Market value"
            icon={FiTrendingUp}
            color="green"
            trend={{ value: totalReturn, isPositive: totalReturn >= 0 }}
          />
          <StatCard
            title="Total Gains"
            value={formatCurrency(totalGains)}
            subtitle="Profit/Loss"
            icon={totalGains >= 0 ? FiArrowUpRight : FiArrowDownRight}
            color={totalGains >= 0 ? "green" : "red"}
            trend={{ value: totalReturn, isPositive: totalReturn >= 0 }}
          />
          <StatCard
            title="Active Investments"
            value={myInvestments.length.toString()}
            subtitle="Portfolio items"
            icon={FiPieChart}
            color="purple"
          />
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 bg-theme-secondary p-1 rounded-lg w-fit">
          {[
            { key: 'portfolio', label: 'My Portfolio' },
            { key: 'opportunities', label: 'Investment Opportunities' }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setSelectedTab(tab.key as any)}
              className={`px-4 py-2 rounded-md text-sm font-medium font-inter transition-all duration-200 ${
                selectedTab === tab.key
                  ? 'bg-brand text-white shadow-md'
                  : 'text-theme-secondary hover:text-theme'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* Investment Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {(selectedTab === 'portfolio' ? myInvestments : availableInvestments).map((investment) => {
            const IconComponent = getCategoryIcon(investment.category);
            const isPortfolio = selectedTab === 'portfolio';
            
            return (
              <Card3D key={investment.id} elevation={2}>
                <div className="p-6">
                  {/* Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 rounded-lg bg-brand/10">
                        <IconComponent className="w-5 h-5 text-brand" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-theme font-inter">
                          {investment.name}
                        </h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskColor(investment.riskLevel)}`}>
                          {investment.riskLevel} risk
                        </span>
                      </div>
                    </div>
                  </div>

                  <p className="text-sm text-theme-secondary font-inter mb-4">
                    {investment.description}
                  </p>

                  {/* Investment Details */}
                  <div className="space-y-3 mb-4">
                    {isPortfolio && investment.invested && (
                      <>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-theme-secondary font-inter">Invested</span>
                          <span className="text-sm font-medium text-theme">
                            {formatCurrency(investment.invested)}
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-theme-secondary font-inter">Current Value</span>
                          <span className="text-sm font-medium text-brand">
                            {formatCurrency(investment.currentValue || 0)}
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-theme-secondary font-inter">Performance</span>
                          <span className={`text-sm font-medium ${
                            (investment.performance || 0) >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {investment.performance >= 0 ? '+' : ''}{investment.performance?.toFixed(1)}%
                          </span>
                        </div>
                      </>
                    )}
                    
                    {!isPortfolio && (
                      <>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-theme-secondary font-inter">Minimum</span>
                          <span className="text-sm font-medium text-theme">
                            {formatCurrency(investment.minimumAmount)}
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-theme-secondary font-inter">Expected Return</span>
                          <span className="text-sm font-medium text-brand">
                            {investment.expectedReturn}% p.a.
                          </span>
                        </div>
                      </>
                    )}
                    
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-theme-secondary font-inter">Duration</span>
                      <span className="text-sm font-medium text-theme">{investment.duration}</span>
                    </div>
                  </div>

                  {/* Action Button */}
                  <Button
                    fullWidth
                    variant={isPortfolio ? 'outline' : 'primary'}
                    size="sm"
                    leftIcon={isPortfolio ? FiBarChart : FiPlus}
                    className="font-inter"
                  >
                    {isPortfolio ? 'View Details' : 'Invest Now'}
                  </Button>
                </div>
              </Card3D>
            );
          })}
        </div>

        {/* Empty State */}
        {(selectedTab === 'portfolio' ? myInvestments : availableInvestments).length === 0 && (
          <div className="text-center py-12">
            <FiPieChart className="w-16 h-16 text-theme-secondary mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-theme font-inter mb-2">
              {selectedTab === 'portfolio' ? 'No investments yet' : 'No opportunities available'}
            </h3>
            <p className="text-theme-secondary font-inter mb-6">
              {selectedTab === 'portfolio' 
                ? 'Start building your investment portfolio today'
                : 'Check back later for new investment opportunities'
              }
            </p>
            {selectedTab === 'portfolio' && (
              <Button
                leftIcon={FiPlus}
                onClick={() => setSelectedTab('opportunities')}
                className="font-inter"
              >
                Explore Opportunities
              </Button>
            )}
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
