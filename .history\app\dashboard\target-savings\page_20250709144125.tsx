"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  FiPlus, 
  FiTarget, 
  FiTrendingUp, 
  FiCalendar, 
  FiDollarSign,
  FiEdit3,
  FiTrash2,
  FiPause,
  FiPlay,
  FiStar,
  FiClock,
  FiCheckCircle
} from 'react-icons/fi';
import DashboardLayout from '../../../src/components/dashboard/DashboardLayout';
import { targetSavingsService } from '../../../src/services';
import { TargetSavings, CreateTargetSavingsData } from '../../../src/types';
import Button from '../../../src/components/ui/Button';
import Card from '../../../src/components/ui/Card';
import Badge from '../../../src/components/ui/Badge';
import Modal from '../../../src/components/ui/Modal';
import Input from '../../../src/components/ui/Input';
import Select from '../../../src/components/ui/Select';
import Textarea from '../../../src/components/ui/Textarea';
import { showToast as toast } from '../../../src/components/ui/Toast';

const categoryIcons = {
  EMERGENCY: '🚨',
  VACATION: '✈️',
  EDUCATION: '🎓',
  HOUSE: '🏠',
  CAR: '🚗',
  BUSINESS: '💼',
  WEDDING: '💒',
  HEALTH: '🏥',
  OTHER: '🎯'
};

const priorityColors = {
  LOW: 'bg-green-500',
  MEDIUM: 'bg-yellow-500',
  HIGH: 'bg-red-500'
};

export default function TargetSavingsPage() {
  const [targets, setTargets] = useState<TargetSavings[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedTarget, setSelectedTarget] = useState<TargetSavings | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  const [createForm, setCreateForm] = useState<Partial<CreateTargetSavingsData>>({
    title: '',
    description: '',
    targetAmount: 0,
    targetDate: '',
    category: 'OTHER',
    priority: 'MEDIUM',
    autoContribution: false,
    contributionAmount: 0,
    contributionFrequency: 'MONTHLY'
  });

  useEffect(() => {
    loadTargets();
  }, []);

  const loadTargets = async () => {
    try {
      setLoading(true);
      const response = await targetSavingsService.getTargetSavings();
      setTargets(response.targets);
    } catch (error) {
      toast.error('Failed to load target savings');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTarget = async () => {
    try {
      if (!createForm.title || !createForm.targetAmount || !createForm.targetDate) {
        toast.error('Please fill in all required fields');
        return;
      }

      await targetSavingsService.createTargetSavings(createForm as CreateTargetSavingsData);
      toast.success('Target savings created successfully');
      setShowCreateModal(false);
      setCreateForm({
        title: '',
        description: '',
        targetAmount: 0,
        targetDate: '',
        category: 'OTHER',
        priority: 'MEDIUM',
        autoContribution: false,
        contributionAmount: 0,
        contributionFrequency: 'MONTHLY'
      });
      loadTargets();
    } catch (error) {
      toast.error('Failed to create target savings');
    }
  };

  const calculateProgress = (current: number, target: number) => {
    return Math.min((current / target) * 100, 100);
  };

  const calculateDaysRemaining = (targetDate: string) => {
    const target = new Date(targetDate);
    const now = new Date();
    const diffTime = target.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN'
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'bg-green-500';
      case 'COMPLETED': return 'bg-blue-500';
      case 'PAUSED': return 'bg-yellow-500';
      case 'CANCELLED': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  if (loading) {
    return (
      <DashboardLayout title="Target Savings">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title="Target Savings">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white">Target Savings</h1>
            <p className="text-gray-400 mt-2">Set and achieve your financial goals</p>
          </div>
          <Button
            onClick={() => setShowCreateModal(true)}
            className="bg-green-600 hover:bg-green-700"
          >
            <FiPlus className="mr-2" />
            Create Target
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Targets</p>
                <p className="text-2xl font-bold text-white">{targets.length}</p>
              </div>
              <FiTarget className="text-green-500 text-2xl" />
            </div>
          </Card>

          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Active Targets</p>
                <p className="text-2xl font-bold text-white">
                  {targets.filter(t => t.status === 'ACTIVE').length}
                </p>
              </div>
              <FiTrendingUp className="text-blue-500 text-2xl" />
            </div>
          </Card>

          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Saved</p>
                <p className="text-2xl font-bold text-white">
                  {formatCurrency(targets.reduce((sum, t) => sum + t.currentAmount, 0))}
                </p>
              </div>
              <FiDollarSign className="text-yellow-500 text-2xl" />
            </div>
          </Card>

          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Completed</p>
                <p className="text-2xl font-bold text-white">
                  {targets.filter(t => t.status === 'COMPLETED').length}
                </p>
              </div>
              <FiCheckCircle className="text-green-500 text-2xl" />
            </div>
          </Card>
        </div>

        {/* Targets Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {targets.map((target) => {
            // Map backend fields to frontend display
            const title = target.goalName || target.title || '';
            const description = target.description || '';
            const currentAmount = typeof target.currentAmount === 'number' ? target.currentAmount : (target.savedAmount || 0);
            const targetAmount = typeof target.targetAmount === 'number' ? target.targetAmount : 0;
            const progress = target.progress !== undefined ? target.progress : calculateProgress(currentAmount, targetAmount);
            const interestAccrued = target.interestAccrued || 0;
            const interestRate = target.interestRate || 0;
            const frequency = (target.frequency || target.contributionFrequency || '').toUpperCase();
            const timelineMonths = target.timelineMonths || 1;
            const startDate = target.startDate || target.createdAt || '';
            const status = target.status || (progress >= 100 ? 'COMPLETED' : 'ACTIVE');
            // Calculate end date if possible
            let endDate = '';
            if (startDate && timelineMonths) {
              const d = new Date(startDate);
              d.setMonth(d.getMonth() + timelineMonths);
              endDate = d.toISOString().split('T')[0];
            }
            // Days remaining
            let daysRemaining = 0;
            if (endDate) {
              const now = new Date();
              const end = new Date(endDate);
              daysRemaining = Math.ceil((end.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
            }
            return (
              <motion.div
                key={target._id || target.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="cursor-pointer"
                onClick={() => {
                  setSelectedTarget(target);
                  setShowDetailsModal(true);
                }}
              >
                <Card className="bg-gray-800 border-gray-700 p-6 hover:border-green-500 transition-colors">
                  <div className="space-y-4">
                    {/* Header */}
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">{categoryIcons[target.category] || '🎯'}</span>
                        <div>
                          <h3 className="font-semibold text-white">{title}</h3>
                          <p className="text-sm text-gray-400">{description}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className={`w-3 h-3 rounded-full ${priorityColors[target.priority] || 'bg-gray-500'}`}></div>
                        <Badge variant={status === 'ACTIVE' ? 'success' : 'default'}>
                          {status}
                        </Badge>
                      </div>
                    </div>

                    {/* Progress */}
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-400">Progress</span>
                        <span className="text-white">{progress.toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div 
                          className="bg-green-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${progress}%` }}
                        ></div>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-400">
                          {formatCurrency(currentAmount)}
                        </span>
                        <span className="text-white">
                          {formatCurrency(targetAmount)}
                        </span>
                      </div>
                      <div className="flex justify-between text-xs text-gray-400 mt-1">
                        <span>Interest: {formatCurrency(interestAccrued)} ({interestRate}% p.a.)</span>
                        <span>Freq: {frequency}</span>
                      </div>
                    </div>

                    {/* Footer */}
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center text-gray-400">
                        <FiCalendar className="mr-1" />
                        {daysRemaining > 0 ? `${daysRemaining} days left` : 'Overdue'}
                      </div>
                      <div className="flex items-center text-gray-400">
                        <span>Start: {startDate ? new Date(startDate).toLocaleDateString() : '-'}</span>
                        {endDate && <span className="ml-2">End: {new Date(endDate).toLocaleDateString()}</span>}
                      </div>
                    </div>
                  </div>
                </Card>
              </motion.div>
            );
          })}
        </div>

        {targets.length === 0 && (
          <div className="text-center py-12">
            <FiTarget className="mx-auto text-6xl text-gray-600 mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">No Target Savings Yet</h3>
            <p className="text-gray-400 mb-6">Create your first savings target to get started</p>
            <Button
              onClick={() => setShowCreateModal(true)}
              className="bg-green-600 hover:bg-green-700"
            >
              <FiPlus className="mr-2" />
              Create Your First Target
            </Button>
          </div>
        )}
      </div>

      {/* Create Target Modal */}
      <Modal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        title="Create Target Savings"
        size="lg"
      >
        <div className="space-y-4">
          <Input
            label="Target Title"
            value={createForm.title}
            onChange={(e) => setCreateForm({ ...createForm, title: e.target.value })}
            placeholder="e.g., Emergency Fund"
            required
          />

          <Textarea
            label="Description"
            value={createForm.description}
            onChange={(e) => setCreateForm({ ...createForm, description: e.target.value })}
            placeholder="Describe your savings goal..."
            rows={3}
          />

          <div className="grid grid-cols-2 gap-4">
            <Input
              label="Target Amount"
              type="number"
              value={createForm.targetAmount}
              onChange={(e) => setCreateForm({ ...createForm, targetAmount: Number(e.target.value) })}
              placeholder="0"
              required
            />

            <Input
              label="Target Date"
              type="date"
              value={createForm.targetDate}
              onChange={(e) => setCreateForm({ ...createForm, targetDate: e.target.value })}
              required
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <Select
              label="Category"
              value={createForm.category}
              onChange={(value) => setCreateForm({ ...createForm, category: value as any })}
              options={[
                { value: 'EMERGENCY', label: '🚨 Emergency' },
                { value: 'VACATION', label: '✈️ Vacation' },
                { value: 'EDUCATION', label: '🎓 Education' },
                { value: 'HOUSE', label: '🏠 House' },
                { value: 'CAR', label: '🚗 Car' },
                { value: 'BUSINESS', label: '💼 Business' },
                { value: 'WEDDING', label: '💒 Wedding' },
                { value: 'HEALTH', label: '🏥 Health' },
                { value: 'OTHER', label: '🎯 Other' }
              ]}
            />

            <Select
              label="Priority"
              value={createForm.priority}
              onChange={(value) => setCreateForm({ ...createForm, priority: value as any })}
              options={[
                { value: 'LOW', label: 'Low Priority' },
                { value: 'MEDIUM', label: 'Medium Priority' },
                { value: 'HIGH', label: 'High Priority' }
              ]}
            />
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="autoContribution"
              checked={createForm.autoContribution}
              onChange={(e) => setCreateForm({ ...createForm, autoContribution: e.target.checked })}
              className="rounded border-gray-600 bg-gray-700 text-green-600"
            />
            <label htmlFor="autoContribution" className="text-white">
              Enable automatic contributions
            </label>
          </div>

          {createForm.autoContribution && (
            <div className="grid grid-cols-2 gap-4">
              <Input
                label="Contribution Amount"
                type="number"
                value={createForm.contributionAmount}
                onChange={(e) => setCreateForm({ ...createForm, contributionAmount: Number(e.target.value) })}
                placeholder="0"
              />

              <Select
                label="Frequency"
                value={createForm.contributionFrequency}
                onChange={(value) => setCreateForm({ ...createForm, contributionFrequency: value as any })}
                options={[
                  { value: 'DAILY', label: 'Daily' },
                  { value: 'WEEKLY', label: 'Weekly' },
                  { value: 'MONTHLY', label: 'Monthly' }
                ]}
              />
            </div>
          )}

          <div className="flex justify-end space-x-3 pt-4">
            <Button
              variant="outline"
              onClick={() => setShowCreateModal(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateTarget}
              className="bg-green-600 hover:bg-green-700"
            >
              Create Target
            </Button>
          </div>
        </div>
      </Modal>
    </DashboardLayout>
  );
}
