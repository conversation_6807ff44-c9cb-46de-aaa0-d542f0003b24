import { useAuth } from '../hooks/use-auth';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

/**
 * KYC enforcement hook for dashboard pages.
 * Redirects to /dashboard/kyc if KYC is not APPROVED.
 * Returns true if KYC is approved, false otherwise.
 */
export function useKYCEnforcement({ redirect = true } = {}) {
  const { user, isLoading, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && isAuthenticated && user?.kycStatus !== 'APPROVED' && redirect) {
      router.push('/dashboard/kyc');
    }
  }, [user, isLoading, isAuthenticated, redirect, router]);

  return user?.kycStatus === 'APPROVED';
}
