import {
  AutomaticWithdrawalRule,
  AutomaticWithdrawalRequest,
  ProcessingEngine,
  ProcessingResult,
  WithdrawalAccount
} from '../types/automaticWithdrawal';
import { automaticWithdrawalService } from './automaticWithdrawalService';
import { paystackService } from './paystackService';

interface UserBalance {
  userId: string;
  totalBalance: number;
  availableBalance: number;
  interestEarned: number;
  lastInterestDate: string;
}

interface ProcessingQueue {
  ruleId: string;
  userId: string;
  priority: 'low' | 'medium' | 'high';
  scheduledTime: Date;
  retryCount: number;
}

class AutomaticWithdrawalProcessor {
  private isProcessing = false;
  private processingQueue: ProcessingQueue[] = [];
  private processingInterval: NodeJS.Timeout | null = null;
  private statistics = {
    processedRulesCount: 0,
    failedRulesCount: 0,
    lastProcessingTime: null as string | null
  };

  constructor() {
    this.startProcessingEngine();
  }

  // Main Processing Engine
  startProcessingEngine() {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
    }

    // Process every 5 minutes
    this.processingInterval = setInterval(() => {
      this.processAutomaticWithdrawals();
    }, 5 * 60 * 1000);

    console.log('Automatic withdrawal processing engine started');
  }

  stopProcessingEngine() {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
    }
    console.log('Automatic withdrawal processing engine stopped');
  }

  async processAutomaticWithdrawals(): Promise<ProcessingResult[]> {
    if (this.isProcessing) {
      console.log('Processing already in progress, skipping...');
      return [];
    }

    this.isProcessing = true;
    this.statistics.lastProcessingTime = new Date().toISOString();
    const results: ProcessingResult[] = [];

    try {
      console.log('Starting automatic withdrawal processing...');

      // Get all active rules
      const activeRules = await automaticWithdrawalService.getAutomaticRules();
      const eligibleRules = activeRules.filter(rule => rule.isActive);

      console.log(`Found ${eligibleRules.length} active rules to process`);

      // Process each rule
      for (const rule of eligibleRules) {
        try {
          const result = await this.processRule(rule);
          results.push(result);

          if (result.success) {
            this.statistics.processedRulesCount++;
          } else {
            this.statistics.failedRulesCount++;
          }
        } catch (error) {
          console.error(`Error processing rule ${rule.id}:`, error);
          results.push({
            ruleId: rule.id,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            executionTime: 0
          });
          this.statistics.failedRulesCount++;
        }
      }

      // Process queued items
      await this.processQueue();

      console.log(`Processing completed. Processed: ${results.filter(r => r.success).length}, Failed: ${results.filter(r => !r.success).length}`);

    } catch (error) {
      console.error('Error in automatic withdrawal processing:', error);
    } finally {
      this.isProcessing = false;
    }

    return results;
  }

  async processRule(rule: AutomaticWithdrawalRule): Promise<ProcessingResult> {
    const startTime = Date.now();

    try {
      // Check if rule should trigger
      const shouldTrigger = await this.shouldRuleTrigger(rule);
      
      if (!shouldTrigger) {
        return {
          ruleId: rule.id,
          success: true,
          executionTime: Date.now() - startTime
        };
      }

      // Get user balance
      const userBalance = await this.getUserBalance(rule.userId);
      
      // Calculate withdrawal amount
      const withdrawalAmount = this.calculateWithdrawalAmount(rule, userBalance);
      
      if (withdrawalAmount <= 0) {
        return {
          ruleId: rule.id,
          success: true,
          executionTime: Date.now() - startTime
        };
      }

      // Validate withdrawal
      const validation = await this.validateWithdrawal(rule, withdrawalAmount, userBalance);
      if (!validation.isValid) {
        throw new Error(validation.error);
      }

      // Create withdrawal request
      const withdrawalRequest = await this.createWithdrawalRequest(rule, withdrawalAmount);

      // Process withdrawal if no approval required
      if (!rule.executionSettings.requireApproval) {
        await this.processWithdrawal(withdrawalRequest);
      }

      // Update rule statistics
      await this.updateRuleStatistics(rule.id, true, withdrawalAmount);

      return {
        ruleId: rule.id,
        success: true,
        withdrawalRequestId: withdrawalRequest.id,
        amount: withdrawalAmount,
        executionTime: Date.now() - startTime
      };

    } catch (error) {
      console.error(`Error processing rule ${rule.id}:`, error);
      
      // Update rule statistics
      await this.updateRuleStatistics(rule.id, false);

      // Add to retry queue if retries are configured
      if (rule.executionSettings.retryAttempts > 0) {
        this.addToRetryQueue(rule);
      }

      return {
        ruleId: rule.id,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime
      };
    }
  }

  private async shouldRuleTrigger(rule: AutomaticWithdrawalRule): Promise<boolean> {
    const now = new Date();
    const conditions = rule.triggerConditions;

    switch (rule.triggerType) {
      case 'balance_threshold':
        if (conditions.balanceThreshold) {
          const userBalance = await this.getUserBalance(rule.userId);
          return userBalance.availableBalance >= conditions.balanceThreshold;
        }
        return false;

      case 'date_based':
        return this.checkDateBasedTrigger(rule, now);

      case 'interest_earned':
        if (conditions.interestThreshold) {
          const userBalance = await this.getUserBalance(rule.userId);
          return userBalance.interestEarned >= conditions.interestThreshold;
        }
        return false;

      case 'goal_reached':
        // This would require integration with savings goals
        return false;

      default:
        return false;
    }
  }

  private checkDateBasedTrigger(rule: AutomaticWithdrawalRule, now: Date): boolean {
    const conditions = rule.triggerConditions;
    const lastExecuted = rule.lastExecuted ? new Date(rule.lastExecuted) : null;

    // Check execution window
    if (conditions.executionWindow) {
      const currentTime = now.getHours() * 60 + now.getMinutes();
      const [startHour, startMin] = conditions.executionWindow.startTime.split(':').map(Number);
      const [endHour, endMin] = conditions.executionWindow.endTime.split(':').map(Number);
      const startTime = startHour * 60 + startMin;
      const endTime = endHour * 60 + endMin;

      if (currentTime < startTime || currentTime > endTime) {
        return false;
      }
    }

    switch (conditions.frequency) {
      case 'daily':
        if (!lastExecuted) return true;
        const daysSinceLastExecution = Math.floor((now.getTime() - lastExecuted.getTime()) / (1000 * 60 * 60 * 24));
        return daysSinceLastExecution >= 1;

      case 'weekly':
        if (!lastExecuted) return true;
        const weeksSinceLastExecution = Math.floor((now.getTime() - lastExecuted.getTime()) / (1000 * 60 * 60 * 24 * 7));
        return weeksSinceLastExecution >= 1 && (conditions.dayOfWeek ? now.getDay() === conditions.dayOfWeek : true);

      case 'monthly':
        if (!lastExecuted) return true;
        const monthsSinceLastExecution = (now.getFullYear() - lastExecuted.getFullYear()) * 12 + (now.getMonth() - lastExecuted.getMonth());
        return monthsSinceLastExecution >= 1 && (conditions.dayOfMonth ? now.getDate() === conditions.dayOfMonth : true);

      case 'quarterly':
        if (!lastExecuted) return true;
        const quartersSinceLastExecution = Math.floor(((now.getFullYear() - lastExecuted.getFullYear()) * 12 + (now.getMonth() - lastExecuted.getMonth())) / 3);
        return quartersSinceLastExecution >= 1;

      default:
        return false;
    }
  }

  private calculateWithdrawalAmount(rule: AutomaticWithdrawalRule, userBalance: UserBalance): number {
    const config = rule.withdrawalConfig;
    const availableBalance = userBalance.availableBalance;

    switch (config.amountType) {
      case 'fixed':
        return config.amount || 0;

      case 'percentage':
        if (!config.percentage) return 0;
        const percentageAmount = (availableBalance * config.percentage) / 100;
        return config.keepMinimumBalance 
          ? Math.max(0, percentageAmount - config.keepMinimumBalance)
          : percentageAmount;

      case 'excess':
        const threshold = rule.triggerConditions.balanceThreshold || 0;
        return Math.max(0, availableBalance - threshold);

      case 'all':
        return config.keepMinimumBalance 
          ? Math.max(0, availableBalance - config.keepMinimumBalance)
          : availableBalance;

      default:
        return 0;
    }
  }

  private async validateWithdrawal(
    rule: AutomaticWithdrawalRule, 
    amount: number, 
    userBalance: UserBalance
  ): Promise<{ isValid: boolean; error?: string }> {
    // Check minimum amount
    if (amount < 1000) {
      return { isValid: false, error: 'Withdrawal amount is below minimum (₦1,000)' };
    }

    // Check maximum amount
    if (rule.withdrawalConfig.maxAmount && amount > rule.withdrawalConfig.maxAmount) {
      return { isValid: false, error: 'Withdrawal amount exceeds maximum limit' };
    }

    // Check available balance
    if (amount > userBalance.availableBalance) {
      return { isValid: false, error: 'Insufficient balance for withdrawal' };
    }

    // Check minimum balance requirement
    const remainingBalance = userBalance.availableBalance - amount;
    const minimumBalance = rule.withdrawalConfig.keepMinimumBalance || 0;
    
    if (remainingBalance < minimumBalance) {
      return { isValid: false, error: 'Withdrawal would violate minimum balance requirement' };
    }

    return { isValid: true };
  }

  private async createWithdrawalRequest(
    rule: AutomaticWithdrawalRule, 
    amount: number
  ): Promise<AutomaticWithdrawalRequest> {
    const withdrawalData = {
      withdrawalAccountId: rule.withdrawalConfig.withdrawalAccountId,
      amount,
      reason: `Automatic withdrawal: ${rule.name}`,
      sourceType: 'balance' as const,
      type: 'automatic' as const
    };

    return automaticWithdrawalService.createInstantWithdrawal(withdrawalData);
  }

  private async processWithdrawal(withdrawalRequest: AutomaticWithdrawalRequest): Promise<void> {
    // This would integrate with the actual payment processing
    // For now, we'll simulate the process
    console.log(`Processing withdrawal ${withdrawalRequest.id} for amount ₦${withdrawalRequest.amount}`);
    
    // In a real implementation, this would:
    // 1. Get withdrawal account details
    // 2. Initiate Paystack transfer
    // 3. Update withdrawal status
    // 4. Send notifications
  }

  private async updateRuleStatistics(
    ruleId: string, 
    success: boolean, 
    amount?: number
  ): Promise<void> {
    // This would update the rule statistics in the database
    console.log(`Updating statistics for rule ${ruleId}: success=${success}, amount=${amount}`);
  }

  private addToRetryQueue(rule: AutomaticWithdrawalRule): void {
    const retryTime = new Date();
    retryTime.setMinutes(retryTime.getMinutes() + rule.executionSettings.retryDelay);

    this.processingQueue.push({
      ruleId: rule.id,
      userId: rule.userId,
      priority: rule.executionSettings.priority,
      scheduledTime: retryTime,
      retryCount: 0
    });

    // Sort queue by priority and scheduled time
    this.processingQueue.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      if (priorityDiff !== 0) return priorityDiff;
      return a.scheduledTime.getTime() - b.scheduledTime.getTime();
    });
  }

  private async processQueue(): Promise<void> {
    const now = new Date();
    const readyItems = this.processingQueue.filter(item => item.scheduledTime <= now);

    for (const item of readyItems) {
      try {
        // Remove from queue
        this.processingQueue = this.processingQueue.filter(q => q.ruleId !== item.ruleId);
        
        // Get rule and process
        const rules = await automaticWithdrawalService.getAutomaticRules();
        const rule = rules.find(r => r.id === item.ruleId);
        
        if (rule) {
          await this.processRule(rule);
        }
      } catch (error) {
        console.error(`Error processing queued item ${item.ruleId}:`, error);
      }
    }
  }

  private async getUserBalance(userId: string): Promise<UserBalance> {
    // This would fetch actual user balance from the database
    // For now, return mock data
    return {
      userId,
      totalBalance: 150000,
      availableBalance: 140000,
      interestEarned: 5000,
      lastInterestDate: new Date().toISOString()
    };
  }

  // Public API
  async triggerRule(ruleId: string): Promise<ProcessingResult> {
    const rules = await automaticWithdrawalService.getAutomaticRules();
    const rule = rules.find(r => r.id === ruleId);
    
    if (!rule) {
      throw new Error('Rule not found');
    }

    return this.processRule(rule);
  }

  getProcessingStatus(): ProcessingEngine {
    return {
      isProcessing: this.isProcessing,
      queueLength: this.processingQueue.length,
      lastProcessingTime: this.statistics.lastProcessingTime,
      processedRulesCount: this.statistics.processedRulesCount,
      failedRulesCount: this.statistics.failedRulesCount
    };
  }

  getQueueStatus(): ProcessingQueue[] {
    return [...this.processingQueue];
  }

  async forceProcessing(): Promise<ProcessingResult[]> {
    return this.processAutomaticWithdrawals();
  }
}

// Export singleton instance
export const automaticWithdrawalProcessor = new AutomaticWithdrawalProcessor();
export default automaticWithdrawalProcessor;
