"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON><PERSON>ser, 
  FiMail, 
  FiPhone, 
  FiMapPin, 
  FiCalendar,
  FiEdit3,
  FiSave,
  FiX,
  FiCamera,
  FiShield,
  FiCreditCard,
  FiPlus,
  FiTrash2
} from 'react-icons/fi';
import DashboardLayout from '../../../src/components/dashboard/DashboardLayout';
import { userService } from '../../../src/services';
import { UserProfile, UpdateUserProfileData } from '../../../src/types';
import { PrimaryButton, SecondaryButton, OutlineButton } from '../../../src/components/ui/ThemedButton';
import { Card } from '../../../src/components/ui/Card';
import { Input } from '../../../src/components/ui/Input';
import { Badge } from '../../../src/components/ui/Badge';
import { Modal } from '../../../src/components/ui/Modal';
import { toast } from '../../../src/components/ui/Toast';

export default function ProfilePage() {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [bankAccounts, setBankAccounts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);
  const [showBankModal, setShowBankModal] = useState(false);
  
  const [editForm, setEditForm] = useState<UpdateUserProfileData>({});
  const [bankForm, setBankForm] = useState({
    bankName: '',
    accountNumber: '',
    accountName: '',
    bankCode: ''
  });

  useEffect(() => {
    loadProfile();
    loadBankAccounts();
  }, []);

  const loadProfile = async () => {
    try {
      const data = await userService.getCurrentUserProfile();
      setProfile(data);
      setEditForm({
        firstName: data.firstName,
        lastName: data.lastName,
        phoneNumber: data.phoneNumber,
        dateOfBirth: data.dateOfBirth,
        address: data.address,
        city: data.city,
        state: data.state,
        country: data.country
      });
    } catch (error) {
      toast.error('Failed to load profile');
    } finally {
      setLoading(false);
    }
  };

  const loadBankAccounts = async () => {
    try {
      const accounts = await userService.getBankAccounts();
      setBankAccounts(accounts);
    } catch (error) {
      console.error('Failed to load bank accounts:', error);
    }
  };

  const handleSaveProfile = async () => {
    try {
      const updatedProfile = await userService.updateCurrentUserProfile(editForm);
      setProfile(updatedProfile);
      setEditing(false);
      toast.success('Profile updated successfully');
    } catch (error) {
      toast.error('Failed to update profile');
    }
  };

  const handleAddBankAccount = async () => {
    try {
      await userService.addBankAccount(bankForm);
      setBankForm({ bankName: '', accountNumber: '', accountName: '', bankCode: '' });
      setShowBankModal(false);
      loadBankAccounts();
      toast.success('Bank account added successfully');
    } catch (error) {
      toast.error('Failed to add bank account');
    }
  };

  const handleDeleteBankAccount = async (accountId: string) => {
    try {
      await userService.deleteBankAccount(accountId);
      loadBankAccounts();
      toast.success('Bank account removed successfully');
    } catch (error) {
      toast.error('Failed to remove bank account');
    }
  };

  const handleSetDefaultAccount = async (accountId: string) => {
    try {
      await userService.setDefaultBankAccount(accountId);
      loadBankAccounts();
      toast.success('Default bank account updated');
    } catch (error) {
      toast.error('Failed to update default account');
    }
  };

  const getKYCStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED': return 'success';
      case 'PENDING': return 'warning';
      case 'REJECTED': return 'error';
      default: return 'secondary';
    }
  };

  if (loading) {
    return (
      <DashboardLayout title="Profile">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (!profile) {
    return (
      <DashboardLayout title="Profile">
        <div className="text-center py-12">
          <p className="text-gray-400">Failed to load profile</p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title="Profile">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white">Profile</h1>
            <p className="text-gray-400 mt-2">Manage your account information</p>
          </div>
          {!editing ? (
            <PrimaryButton
              onClick={() => setEditing(true)}
              className="bg-green-600 hover:bg-green-700"
            >
              <FiEdit3 className="mr-2" />
              Edit Profile
            </PrimaryButton>
          ) : (
            <div className="flex space-x-2">
              <OutlineButton
                onClick={() => {
                  setEditing(false);
                  setEditForm({
                    firstName: profile.firstName,
                    lastName: profile.lastName,
                    phoneNumber: profile.phoneNumber,
                    dateOfBirth: profile.dateOfBirth,
                    address: profile.address,
                    city: profile.city,
                    state: profile.state,
                    country: profile.country
                  });
                }}
              >
                <FiX className="mr-2" />
                Cancel
              </OutlineButton>
              <PrimaryButton
                onClick={handleSaveProfile}
                className="bg-green-600 hover:bg-green-700"
              >
                <FiSave className="mr-2" />
                Save Changes
              </PrimaryButton>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Profile Card */}
          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="text-center space-y-4">
              <div className="relative inline-block">
                <div className="w-24 h-24 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center text-white text-2xl font-bold">
                  {profile.profileImage ? (
                    <img 
                      src={profile.profileImage} 
                      alt="Profile" 
                      className="w-24 h-24 rounded-full object-cover"
                    />
                  ) : (
                    userService.getInitials(profile.firstName, profile.lastName)
                  )}
                </div>
                <button className="absolute bottom-0 right-0 bg-green-600 hover:bg-green-700 rounded-full p-2 text-white">
                  <FiCamera className="w-4 h-4" />
                </button>
              </div>
              
              <div>
                <h3 className="text-xl font-semibold text-white">
                  {profile.firstName} {profile.lastName}
                </h3>
                <p className="text-gray-400">{profile.email}</p>
              </div>

              <div className="flex justify-center space-x-2">
                <Badge variant={profile.isVerified ? 'success' : 'warning'}>
                  {profile.isVerified ? 'Verified' : 'Unverified'}
                </Badge>
                <Badge variant={getKYCStatusColor(profile.kycStatus)}>
                  KYC {profile.kycStatus}
                </Badge>
              </div>

              <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-700">
                <div>
                  <p className="text-gray-400 text-sm">Balance</p>
                  <p className="text-lg font-semibold text-white">
                    {userService.formatCurrency(profile.balance)}
                  </p>
                </div>
                <div>
                  <p className="text-gray-400 text-sm">Total Savings</p>
                  <p className="text-lg font-semibold text-white">
                    {userService.formatCurrency(profile.totalSavings)}
                  </p>
                </div>
              </div>
            </div>
          </Card>

          {/* Personal Information */}
          <Card className="bg-gray-800 border-gray-700 p-6 lg:col-span-2">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <FiUser className="mr-2" />
              Personal Information
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  First Name
                </label>
                {editing ? (
                  <Input
                    value={editForm.firstName || ''}
                    onChange={(e) => setEditForm({ ...editForm, firstName: e.target.value })}
                  />
                ) : (
                  <p className="text-white">{profile.firstName}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  Last Name
                </label>
                {editing ? (
                  <Input
                    value={editForm.lastName || ''}
                    onChange={(e) => setEditForm({ ...editForm, lastName: e.target.value })}
                  />
                ) : (
                  <p className="text-white">{profile.lastName}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  Email
                </label>
                <p className="text-white flex items-center">
                  <FiMail className="mr-2 text-gray-400" />
                  {profile.email}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  Phone Number
                </label>
                {editing ? (
                  <Input
                    value={editForm.phoneNumber || ''}
                    onChange={(e) => setEditForm({ ...editForm, phoneNumber: e.target.value })}
                  />
                ) : (
                  <p className="text-white flex items-center">
                    <FiPhone className="mr-2 text-gray-400" />
                    {profile.phoneNumber || 'Not provided'}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  Date of Birth
                </label>
                {editing ? (
                  <Input
                    type="date"
                    value={editForm.dateOfBirth || ''}
                    onChange={(e) => setEditForm({ ...editForm, dateOfBirth: e.target.value })}
                  />
                ) : (
                  <p className="text-white flex items-center">
                    <FiCalendar className="mr-2 text-gray-400" />
                    {profile.dateOfBirth || 'Not provided'}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  Address
                </label>
                {editing ? (
                  <Input
                    value={editForm.address || ''}
                    onChange={(e) => setEditForm({ ...editForm, address: e.target.value })}
                  />
                ) : (
                  <p className="text-white flex items-center">
                    <FiMapPin className="mr-2 text-gray-400" />
                    {profile.address || 'Not provided'}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  City
                </label>
                {editing ? (
                  <Input
                    value={editForm.city || ''}
                    onChange={(e) => setEditForm({ ...editForm, city: e.target.value })}
                  />
                ) : (
                  <p className="text-white">{profile.city || 'Not provided'}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  State
                </label>
                {editing ? (
                  <Input
                    value={editForm.state || ''}
                    onChange={(e) => setEditForm({ ...editForm, state: e.target.value })}
                  />
                ) : (
                  <p className="text-white">{profile.state || 'Not provided'}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  Country
                </label>
                {editing ? (
                  <Input
                    value={editForm.country || ''}
                    onChange={(e) => setEditForm({ ...editForm, country: e.target.value })}
                  />
                ) : (
                  <p className="text-white">{profile.country || 'Not provided'}</p>
                )}
              </div>
            </div>
          </Card>
        </div>

        {/* Bank Accounts */}
        <Card className="bg-gray-800 border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-white flex items-center">
              <FiCreditCard className="mr-2" />
              Bank Accounts
            </h3>
            <PrimaryButton
              onClick={() => setShowBankModal(true)}
              className="bg-green-600 hover:bg-green-700"
            >
              <FiPlus className="mr-2" />
              Add Account
            </PrimaryButton>
          </div>

          <div className="space-y-4">
            {bankAccounts.map((account) => (
              <div
                key={account.id}
                className="flex items-center justify-between p-4 bg-gray-700 rounded-lg"
              >
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center">
                    <FiCreditCard className="text-white" />
                  </div>
                  <div>
                    <p className="font-semibold text-white">{account.bankName}</p>
                    <p className="text-gray-400">{account.accountNumber}</p>
                    <p className="text-sm text-gray-400">{account.accountName}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  {account.isDefault && (
                    <Badge variant="success">Default</Badge>
                  )}
                  {account.isVerified ? (
                    <Badge variant="success">Verified</Badge>
                  ) : (
                    <Badge variant="warning">Pending</Badge>
                  )}
                  
                  <div className="flex space-x-1">
                    {!account.isDefault && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleSetDefaultAccount(account.id)}
                      >
                        Set Default
                      </Button>
                    )}
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDeleteBankAccount(account.id)}
                      className="text-red-400 hover:text-red-300"
                    >
                      <FiTrash2 />
                    </Button>
                  </div>
                </div>
              </div>
            ))}

            {bankAccounts.length === 0 && (
              <div className="text-center py-8">
                <FiCreditCard className="mx-auto text-4xl text-gray-600 mb-2" />
                <p className="text-gray-400">No bank accounts added yet</p>
              </div>
            )}
          </div>
        </Card>
      </div>

      {/* Add Bank Account Modal */}
      <Modal
        isOpen={showBankModal}
        onClose={() => setShowBankModal(false)}
        title="Add Bank Account"
      >
        <div className="space-y-4">
          <Input
            label="Bank Name"
            value={bankForm.bankName}
            onChange={(e) => setBankForm({ ...bankForm, bankName: e.target.value })}
            placeholder="e.g., First Bank"
          />

          <Input
            label="Account Number"
            value={bankForm.accountNumber}
            onChange={(e) => setBankForm({ ...bankForm, accountNumber: e.target.value })}
            placeholder="**********"
          />

          <Input
            label="Account Name"
            value={bankForm.accountName}
            onChange={(e) => setBankForm({ ...bankForm, accountName: e.target.value })}
            placeholder="John Doe"
          />

          <Input
            label="Bank Code"
            value={bankForm.bankCode}
            onChange={(e) => setBankForm({ ...bankForm, bankCode: e.target.value })}
            placeholder="011"
          />

          <div className="flex justify-end space-x-3 pt-4">
            <OutlineButton
              onClick={() => setShowBankModal(false)}
            >
              Cancel
            </OutlineButton>
            <PrimaryButton
              onClick={handleAddBankAccount}
              className="bg-green-600 hover:bg-green-700"
            >
              Add Account
            </PrimaryButton>
          </div>
        </div>
      </Modal>
    </DashboardLayout>
  );
}
