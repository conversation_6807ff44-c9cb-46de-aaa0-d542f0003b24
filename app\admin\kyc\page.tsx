"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  FiShield, 
  FiSearch, 
  FiFilter,
  FiEye,
  FiCheck,
  FiX,
  FiDownload,
  FiRefreshCw,
  FiUser,
  FiFileText,
  FiCalendar,
  FiClock,
  FiAlertCircle
} from 'react-icons/fi';
import AdminLayout from '../../../src/components/admin/AdminLayout';
import { kycService } from '../../../src/services';
import { KYC, KYCSearchFilters } from '../../../src/types';
import { PrimaryButton, OutlineButton } from '../../../src/components/ui/AnimatedButton';
import { Modal } from '../../../src/components/ui/Modal';
import { Table } from '../../../src/components/ui/Table';
import { Pagination } from '../../../src/components/ui/Pagination';
import { Textarea } from '../../../src/components/ui/Textarea';
import { toast } from '../../../src/components/ui/Toast';

const documentTypeIcons = {
  NATIONAL_ID: '🆔',
  PASSPORT: '📘',
  DRIVERS_LICENSE: '🚗',
  VOTERS_CARD: '🗳️',
  UTILITY_BILL: '📄',
  BANK_STATEMENT: '🏦',
  SELFIE: '🤳',
  SIGNATURE: '✍️',
  OTHER: '📎'
};

export default function AdminKYCPage() {
  // Temporary placeholder - UI components need to be implemented
  return (
    <AdminLayout>
      <div className="p-6">
        <h1 className="text-2xl font-bold text-white mb-4">KYC Management</h1>
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <p className="text-gray-400">KYC management interface coming soon...</p>
        </div>
      </div>
    </AdminLayout>
  );
}

function AdminKYCPageOriginal() {
  const [kycRecords, setKycRecords] = useState<KYC[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedKYC, setSelectedKYC] = useState<KYC | null>(null);
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [reviewAction, setReviewAction] = useState<'APPROVED' | 'REJECTED'>('APPROVED');
  const [reviewNotes, setReviewNotes] = useState('');
  const [rejectionReason, setRejectionReason] = useState('');
  
  const [filters, setFilters] = useState<KYCSearchFilters>({
    search: '',
    status: undefined,
    level: undefined,
    dateFrom: '',
    dateTo: '',
    sortBy: 'submittedAt',
    sortOrder: 'desc',
    page: 1,
    limit: 20
  });

  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 20,
    totalPages: 0
  });

  useEffect(() => {
    loadKYCRecords();
  }, [filters]);

  const loadKYCRecords = async () => {
    try {
      setLoading(true);
      const response = await kycService.getAllKYC(filters);
      setKycRecords(response.kyc);
      setPagination({
        total: response.total,
        page: response.page,
        limit: response.limit,
        totalPages: response.totalPages
      });
    } catch (error) {
      toast.error('Failed to load KYC records');
    } finally {
      setLoading(false);
    }
  };

  const handleReviewKYC = async () => {
    if (!selectedKYC) return;

    try {
      await kycService.reviewKYC({
        kycId: selectedKYC.id,
        status: reviewAction,
        reviewNotes,
        rejectionReason: reviewAction === 'REJECTED' ? rejectionReason : undefined
      });

      setKycRecords(kycRecords.map(kyc => 
        kyc.id === selectedKYC.id 
          ? { ...kyc, status: reviewAction, reviewNotes, rejectionReason }
          : kyc
      ));

      toast.success(`KYC ${reviewAction.toLowerCase()} successfully`);
      setShowReviewModal(false);
      setReviewNotes('');
      setRejectionReason('');
    } catch (error) {
      toast.error('Failed to review KYC');
    }
  };

  const handleDownloadDocument = async (kycId: string, documentId: string) => {
    try {
      const blob = await kycService.downloadDocument(kycId, documentId);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `document-${documentId}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      toast.error('Failed to download document');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-NG');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED': return 'success';
      case 'PENDING': return 'warning';
      case 'UNDER_REVIEW': return 'info';
      case 'REJECTED': return 'error';
      default: return 'secondary';
    }
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'BASIC': return 'success';
      case 'INTERMEDIATE': return 'warning';
      case 'ADVANCED': return 'info';
      default: return 'secondary';
    }
  };

  const columns = [
    {
      key: 'user',
      label: 'User',
      render: (kyc: KYC) => (
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center text-white font-semibold">
            {kyc.personalInfo.firstName.charAt(0)}{kyc.personalInfo.lastName.charAt(0)}
          </div>
          <div>
            <p className="font-medium text-white">
              {kyc.personalInfo.firstName} {kyc.personalInfo.lastName}
            </p>
            <p className="text-sm text-gray-400">{kyc.contactInfo.email}</p>
          </div>
        </div>
      )
    },
    {
      key: 'level',
      label: 'Level',
      render: (kyc: KYC) => (
        <Badge variant={getLevelColor(kyc.level)}>
          {kyc.level}
        </Badge>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (kyc: KYC) => (
        <Badge variant={getStatusColor(kyc.status)}>
          {kyc.status.replace('_', ' ')}
        </Badge>
      )
    },
    {
      key: 'documents',
      label: 'Documents',
      render: (kyc: KYC) => (
        <div className="text-sm">
          <p className="text-white">{kyc.documents.length} uploaded</p>
          <p className="text-gray-400">
            {kyc.documents.filter(d => d.status === 'APPROVED').length} approved
          </p>
        </div>
      )
    },
    {
      key: 'submitted',
      label: 'Submitted',
      render: (kyc: KYC) => (
        <div className="text-sm text-gray-400">
          <div className="flex items-center">
            <FiCalendar className="mr-1" />
            {kyc.submittedAt ? formatDate(kyc.submittedAt) : 'Not submitted'}
          </div>
          {kyc.reviewedAt && (
            <p className="mt-1">Reviewed: {formatDate(kyc.reviewedAt)}</p>
          )}
        </div>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (kyc: KYC) => (
        <div className="flex items-center space-x-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => {
              setSelectedKYC(kyc);
              setShowReviewModal(true);
            }}
          >
            <FiEye />
          </Button>
          {kyc.status === 'PENDING' && (
            <>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setSelectedKYC(kyc);
                  setReviewAction('APPROVED');
                  setShowReviewModal(true);
                }}
                className="text-green-400 hover:text-green-300"
              >
                <FiCheck />
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setSelectedKYC(kyc);
                  setReviewAction('REJECTED');
                  setShowReviewModal(true);
                }}
                className="text-red-400 hover:text-red-300"
              >
                <FiX />
              </Button>
            </>
          )}
        </div>
      )
    }
  ];

  return (
    <AdminLayout title="KYC Management">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white">KYC Management</h1>
            <p className="text-gray-400 mt-2">Review and manage user verification documents</p>
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={loadKYCRecords}
            >
              <FiRefreshCw className="mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Pending Review</p>
                <p className="text-2xl font-bold text-yellow-400">
                  {kycRecords.filter(k => k.status === 'PENDING').length}
                </p>
              </div>
              <FiClock className="text-yellow-500 text-2xl" />
            </div>
          </Card>

          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Approved</p>
                <p className="text-2xl font-bold text-green-400">
                  {kycRecords.filter(k => k.status === 'APPROVED').length}
                </p>
              </div>
              <FiCheck className="text-green-500 text-2xl" />
            </div>
          </Card>

          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Rejected</p>
                <p className="text-2xl font-bold text-red-400">
                  {kycRecords.filter(k => k.status === 'REJECTED').length}
                </p>
              </div>
              <FiX className="text-red-500 text-2xl" />
            </div>
          </Card>

          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Under Review</p>
                <p className="text-2xl font-bold text-blue-400">
                  {kycRecords.filter(k => k.status === 'UNDER_REVIEW').length}
                </p>
              </div>
              <FiShield className="text-blue-500 text-2xl" />
            </div>
          </Card>
        </div>

        {/* Filters */}
        <Card className="bg-gray-800 border-gray-700 p-6">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <Input
              placeholder="Search by name or email..."
              value={filters.search}
              onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              icon={<FiSearch />}
            />

            <Select
              value={filters.status || ''}
              onChange={(value) => setFilters({ ...filters, status: value || undefined })}
              options={[
                { value: '', label: 'All Status' },
                { value: 'PENDING', label: 'Pending' },
                { value: 'UNDER_REVIEW', label: 'Under Review' },
                { value: 'APPROVED', label: 'Approved' },
                { value: 'REJECTED', label: 'Rejected' }
              ]}
            />

            <Select
              value={filters.level || ''}
              onChange={(value) => setFilters({ ...filters, level: value || undefined })}
              options={[
                { value: '', label: 'All Levels' },
                { value: 'BASIC', label: 'Basic' },
                { value: 'INTERMEDIATE', label: 'Intermediate' },
                { value: 'ADVANCED', label: 'Advanced' }
              ]}
            />

            <Input
              type="date"
              value={filters.dateFrom}
              onChange={(e) => setFilters({ ...filters, dateFrom: e.target.value })}
              placeholder="From Date"
            />

            <Input
              type="date"
              value={filters.dateTo}
              onChange={(e) => setFilters({ ...filters, dateTo: e.target.value })}
              placeholder="To Date"
            />
          </div>
        </Card>

        {/* KYC Table */}
        <Card className="bg-gray-800 border-gray-700">
          <div className="p-6 border-b border-gray-700">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold text-white">
                KYC Records ({pagination.total.toLocaleString()})
              </h3>
              <div className="flex items-center space-x-2">
                <Select
                  value={filters.sortBy}
                  onChange={(value) => setFilters({ ...filters, sortBy: value as any })}
                  options={[
                    { value: 'submittedAt', label: 'Date Submitted' },
                    { value: 'status', label: 'Status' },
                    { value: 'level', label: 'Level' }
                  ]}
                />
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setFilters({ 
                    ...filters, 
                    sortOrder: filters.sortOrder === 'asc' ? 'desc' : 'asc' 
                  })}
                >
                  {filters.sortOrder === 'asc' ? '↑' : '↓'}
                </Button>
              </div>
            </div>
          </div>

          <Table
            columns={columns}
            data={kycRecords}
            loading={loading}
            emptyMessage="No KYC records found"
          />

          <div className="p-6 border-t border-gray-700">
            <Pagination
              currentPage={pagination.page}
              totalPages={pagination.totalPages}
              onPageChange={(page) => setFilters({ ...filters, page })}
              showInfo={true}
              totalItems={pagination.total}
              itemsPerPage={pagination.limit}
            />
          </div>
        </Card>
      </div>

      {/* Review Modal */}
      <Modal
        isOpen={showReviewModal}
        onClose={() => setShowReviewModal(false)}
        title="Review KYC Application"
        size="xl"
      >
        {selectedKYC && (
          <div className="space-y-6">
            {/* User Info */}
            <div className="flex items-center space-x-4 p-4 bg-gray-700 rounded-lg">
              <div className="w-16 h-16 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center text-white text-xl font-bold">
                {selectedKYC.personalInfo.firstName.charAt(0)}{selectedKYC.personalInfo.lastName.charAt(0)}
              </div>
              <div>
                <h3 className="text-xl font-semibold text-white">
                  {selectedKYC.personalInfo.firstName} {selectedKYC.personalInfo.lastName}
                </h3>
                <p className="text-gray-400">{selectedKYC.contactInfo.email}</p>
                <div className="flex space-x-2 mt-2">
                  <Badge variant={getLevelColor(selectedKYC.level)}>
                    {selectedKYC.level}
                  </Badge>
                  <Badge variant={getStatusColor(selectedKYC.status)}>
                    {selectedKYC.status}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Personal Information */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold text-white mb-2">Personal Information</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Date of Birth:</span>
                    <span className="text-white">{selectedKYC.personalInfo.dateOfBirth}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Nationality:</span>
                    <span className="text-white">{selectedKYC.personalInfo.nationality}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Gender:</span>
                    <span className="text-white">{selectedKYC.personalInfo.gender}</span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-semibold text-white mb-2">Contact Information</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Phone:</span>
                    <span className="text-white">{selectedKYC.contactInfo.phoneNumber}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Address:</span>
                    <span className="text-white">{selectedKYC.addressInfo.street}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">City:</span>
                    <span className="text-white">{selectedKYC.addressInfo.city}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Documents */}
            <div>
              <h4 className="font-semibold text-white mb-4">Uploaded Documents</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {selectedKYC.documents.map((doc) => (
                  <div key={doc.id} className="p-4 bg-gray-700 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">{documentTypeIcons[doc.type]}</span>
                        <span className="font-medium text-white">{doc.name}</span>
                      </div>
                      <Badge variant={getStatusColor(doc.status)}>
                        {doc.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-400 mb-2">{doc.type.replace('_', ' ')}</p>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDownloadDocument(selectedKYC.id, doc.id)}
                    >
                      <FiDownload className="mr-1" />
                      Download
                    </Button>
                  </div>
                ))}
              </div>
            </div>

            {/* Review Action */}
            <div className="space-y-4 p-4 bg-gray-700 rounded-lg">
              <div className="flex space-x-4">
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    name="reviewAction"
                    value="APPROVED"
                    checked={reviewAction === 'APPROVED'}
                    onChange={(e) => setReviewAction(e.target.value as any)}
                    className="text-green-600"
                  />
                  <span className="text-white">Approve</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    name="reviewAction"
                    value="REJECTED"
                    checked={reviewAction === 'REJECTED'}
                    onChange={(e) => setReviewAction(e.target.value as any)}
                    className="text-red-600"
                  />
                  <span className="text-white">Reject</span>
                </label>
              </div>

              <Textarea
                label="Review Notes"
                value={reviewNotes}
                onChange={(e) => setReviewNotes(e.target.value)}
                placeholder="Add your review notes..."
                rows={3}
              />

              {reviewAction === 'REJECTED' && (
                <Textarea
                  label="Rejection Reason"
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  placeholder="Specify the reason for rejection..."
                  rows={2}
                  required
                />
              )}
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-700">
              <Button
                variant="outline"
                onClick={() => setShowReviewModal(false)}
              >
                Cancel
              </Button>
              <Button
                variant={reviewAction === 'APPROVED' ? 'success' : 'error'}
                onClick={handleReviewKYC}
                disabled={reviewAction === 'REJECTED' && !rejectionReason}
              >
                {reviewAction === 'APPROVED' ? 'Approve KYC' : 'Reject KYC'}
              </Button>
            </div>
          </div>
        )}
      </Modal>
    </AdminLayout>
  );
}
