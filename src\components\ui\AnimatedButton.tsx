"use client";

import React from 'react';
import { motion } from 'framer-motion';

interface AnimatedButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  href?: string;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
  icon?: React.ReactNode;
}

const ArrowIcon = () => (
  <svg width={34} height={34} viewBox="0 0 74 74" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx={37} cy={37} r="35.5" stroke="currentColor" strokeWidth={3} />
    <path d="M25 35.5C24.1716 35.5 23.5 36.1716 23.5 37C23.5 37.8284 24.1716 38.5 25 38.5V35.5ZM49.0607 38.0607C49.6464 37.4749 49.6464 36.5251 49.0607 35.9393L39.5147 26.3934C38.9289 25.8076 37.9792 25.8076 37.3934 26.3934C36.8076 26.9792 36.8076 27.9289 37.3934 28.5147L45.8787 37L37.3934 45.4853C36.8076 46.0711 36.8076 47.0208 37.3934 47.6066C37.9792 48.1924 38.9289 48.1924 39.5147 47.6066L49.0607 38.0607ZM25 38.5L48 38.5V35.5L25 35.5V38.5Z" fill="currentColor" />
  </svg>
);

export function AnimatedButton({
  children,
  onClick,
  href,
  variant = 'primary',
  size = 'md',
  disabled = false,
  className = '',
  type = 'button',
  icon
}: AnimatedButtonProps) {
  const baseClasses = `
    cursor-pointer font-bold font-sans transition-all duration-200 
    border border-transparent flex items-center justify-center
    rounded-full relative overflow-hidden group
    ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
  `;

  const variantClasses = {
    primary: `
      bg-gradient-to-r from-green-400 to-green-600 
      hover:from-green-500 hover:to-green-700
      text-white border-green-500
    `,
    secondary: `
      bg-gradient-to-r from-blue-400 to-blue-600 
      hover:from-blue-500 hover:to-blue-700
      text-white border-blue-500
    `,
    outline: `
      bg-transparent border-green-400 text-green-400
      hover:bg-green-400 hover:text-black
    `
  };

  const sizeClasses = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg'
  };

  const buttonClasses = `
    ${baseClasses}
    ${variantClasses[variant]}
    ${sizeClasses[size]}
    ${className}
  `;

  const ButtonContent = () => (
    <>
      {/* Background glow effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-green-400/20 to-green-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full blur-xl"></div>
      
      {/* Button content */}
      <span className="relative z-10 flex items-center">
        <span className="text">{children}</span>
        {(icon || variant === 'primary') && (
          <motion.div
            className="ml-2 text-current"
            whileHover={{ x: 5 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          >
            {icon || <ArrowIcon />}
          </motion.div>
        )}
      </span>
    </>
  );

  if (href) {
    return (
      <motion.a
        href={href}
        className={buttonClasses}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.95 }}
        transition={{ type: "spring", stiffness: 400, damping: 10 }}
      >
        <ButtonContent />
      </motion.a>
    );
  }

  return (
    <motion.button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={buttonClasses}
      whileHover={{ scale: disabled ? 1 : 1.02 }}
      whileTap={{ scale: disabled ? 1 : 0.95 }}
      transition={{ type: "spring", stiffness: 400, damping: 10 }}
    >
      <ButtonContent />
    </motion.button>
  );
}

// Specialized button variants
export function PrimaryButton(props: Omit<AnimatedButtonProps, 'variant'>) {
  return <AnimatedButton {...props} variant="primary" />;
}

export function SecondaryButton(props: Omit<AnimatedButtonProps, 'variant'>) {
  return <AnimatedButton {...props} variant="secondary" />;
}

export function OutlineButton(props: Omit<AnimatedButtonProps, 'variant'>) {
  return <AnimatedButton {...props} variant="outline" />;
}

// Link button component
export function LinkButton({ 
  href, 
  children, 
  variant = 'primary',
  ...props 
}: AnimatedButtonProps & { href: string }) {
  return (
    <AnimatedButton 
      href={href} 
      variant={variant}
      {...props}
    >
      {children}
    </AnimatedButton>
  );
}

export default AnimatedButton;
