"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON><PERSON>ell, 
  <PERSON>Check, 
  FiCheckCircle,
  FiTrash2,
  FiFilter,
  FiSearch,
  FiArchive,
  FiMail,
  FiSmartphone,
  FiMonitor,
  FiClock,
  FiAlertCircle,
  FiInfo,
  FiX
} from 'react-icons/fi';
import DashboardLayout from '../../../src/components/dashboard/DashboardLayout';
import { notificationsService } from '../../../src/services';
import { Notification, NotificationSearchFilters } from '../../../src/types';
import { Button } from '../../../src/components/ui/Button';
import { Card } from '../../../src/components/ui/Card';
import { Badge } from '../../../src/components/ui/Badge';
import { Input } from '../../../src/components/ui/Input';
import { Select } from '../../../src/components/ui/Select';
import { toast } from '../../../src/components/ui/Toast';

const notificationIcons = {
  DEPOSIT: '💰',
  WITHDRAWAL: '🏦',
  CONTRIBUTION: '📈',
  PLAN_CREATED: '📋',
  PLAN_COMPLETED: '✅',
  PLAN_PAUSED: '⏸️',
  GOAL_ACHIEVED: '🎯',
  MILESTONE_REACHED: '🏆',
  PAYMENT_DUE: '⏰',
  PAYMENT_OVERDUE: '⚠️',
  KYC_APPROVED: '✅',
  KYC_REJECTED: '❌',
  GROUP_INVITATION: '👥',
  GROUP_PAYOUT: '💸',
  SYSTEM: '⚙️',
  PROMOTIONAL: '🎁',
  SECURITY: '🔒',
  OTHER: '📢'
};

const priorityColors = {
  LOW: 'bg-green-500',
  MEDIUM: 'bg-yellow-500',
  HIGH: 'bg-orange-500',
  URGENT: 'bg-red-500'
};

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [unreadCount, setUnreadCount] = useState(0);
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);
  const [filters, setFilters] = useState<NotificationSearchFilters>({
    status: undefined,
    type: undefined,
    priority: undefined,
    page: 1,
    limit: 20
  });

  useEffect(() => {
    loadNotifications();
    loadUnreadCount();
  }, [filters]);

  const loadNotifications = async () => {
    try {
      setLoading(true);
      const response = await notificationsService.getUserNotifications(filters);
      setNotifications(response.notifications);
    } catch (error) {
      toast.error('Failed to load notifications');
    } finally {
      setLoading(false);
    }
  };

  const loadUnreadCount = async () => {
    try {
      const response = await notificationsService.getUnreadCount();
      setUnreadCount(response.count);
    } catch (error) {
      console.error('Failed to load unread count:', error);
    }
  };

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await notificationsService.markAsRead(notificationId);
      setNotifications(notifications.map(n => 
        n.id === notificationId ? { ...n, status: 'READ', readAt: new Date().toISOString() } : n
      ));
      loadUnreadCount();
      toast.success('Notification marked as read');
    } catch (error) {
      toast.error('Failed to mark notification as read');
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await notificationsService.markAllAsRead();
      setNotifications(notifications.map(n => ({ 
        ...n, 
        status: 'read', 
        readAt: new Date().toISOString() 
      })));
      setUnreadCount(0);
      toast.success('All notifications marked as read');
    } catch (error) {
      toast.error('Failed to mark all notifications as read');
    }
  };

  const handleArchiveNotification = async (notificationId: string) => {
    try {
      await notificationsService.archiveNotification(notificationId);
      setNotifications(notifications.filter(n => n.id !== notificationId));
      toast.success('Notification archived');
    } catch (error) {
      toast.error('Failed to archive notification');
    }
  };

  const handleDeleteNotification = async (notificationId: string) => {
    try {
      await notificationsService.deleteNotification(notificationId);
      setNotifications(notifications.filter(n => n.id !== notificationId));
      toast.success('Notification deleted');
    } catch (error) {
      toast.error('Failed to delete notification');
    }
  };

  const handleBulkAction = async (action: 'read' | 'archive' | 'delete') => {
    try {
      if (selectedNotifications.length === 0) {
        toast.error('No notifications selected');
        return;
      }

      switch (action) {
        case 'read':
          for (const id of selectedNotifications) {
            await notificationsService.markAsRead(id);
          }
          setNotifications(notifications.map(n => 
            selectedNotifications.includes(n.id) 
              ? { ...n, status: 'read', readAt: new Date().toISOString() } 
              : n
          ));
          toast.success('Selected notifications marked as read');
          break;

        case 'archive':
          for (const id of selectedNotifications) {
            await notificationsService.archiveNotification(id);
          }
          setNotifications(notifications.filter(n => !selectedNotifications.includes(n.id)));
          toast.success('Selected notifications archived');
          break;

        case 'delete':
          for (const id of selectedNotifications) {
            await notificationsService.deleteNotification(id);
          }
          setNotifications(notifications.filter(n => !selectedNotifications.includes(n.id)));
          toast.success('Selected notifications deleted');
          break;
      }

      setSelectedNotifications([]);
      loadUnreadCount();
    } catch (error) {
      toast.error(`Failed to ${action} notifications`);
    }
  };

  const toggleNotificationSelection = (notificationId: string) => {
    setSelectedNotifications(prev => 
      prev.includes(notificationId)
        ? prev.filter(id => id !== notificationId)
        : [...prev, notificationId]
    );
  };

  const selectAllNotifications = () => {
    if (selectedNotifications.length === notifications.length) {
      setSelectedNotifications([]);
    } else {
      setSelectedNotifications(notifications.map(n => n.id));
    }
  };

  const formatTimeAgo = (dateString: string) => {
    return notificationsService.formatTimeAgo(dateString);
  };

  const getNotificationIcon = (type: string) => {
    return notificationIcons[type] || '📢';
  };

  const getPriorityColor = (priority: string) => {
    return priorityColors[priority] || 'bg-gray-500';
  };

  if (loading) {
    return (
      <DashboardLayout title="Notifications">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title="Notifications">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white">Notifications</h1>
            <p className="text-gray-400 mt-2">
              {unreadCount > 0 ? `${unreadCount} unread notifications` : 'All caught up!'}
            </p>
          </div>
          <div className="flex space-x-2">
            {unreadCount > 0 && (
              <Button
                onClick={handleMarkAllAsRead}
                variant="outline"
              >
                <FiCheckCircle className="mr-2" />
                Mark All Read
              </Button>
            )}
          </div>
        </div>

        {/* Filters */}
        <Card className="bg-gray-800 border-gray-700 p-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Input
              placeholder="Search notifications..."
              value={filters.search || ''}
              onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              icon={<FiSearch />}
            />

            <Select
              value={filters.status || ''}
              onChange={(value) => setFilters({ ...filters, status: value || undefined })}
              options={[
                { value: '', label: 'All Status' },
                { value: 'UNREAD', label: 'Unread' },
                { value: 'READ', label: 'Read' },
                { value: 'ARCHIVED', label: 'Archived' }
              ]}
            />

            <Select
              value={filters.type || ''}
              onChange={(value) => setFilters({ ...filters, type: value || undefined })}
              options={[
                { value: '', label: 'All Types' },
                { value: 'DEPOSIT', label: 'Deposits' },
                { value: 'WITHDRAWAL', label: 'Withdrawals' },
                { value: 'CONTRIBUTION', label: 'Contributions' },
                { value: 'SYSTEM', label: 'System' },
                { value: 'SECURITY', label: 'Security' }
              ]}
            />

            <Select
              value={filters.priority || ''}
              onChange={(value) => setFilters({ ...filters, priority: value || undefined })}
              options={[
                { value: '', label: 'All Priorities' },
                { value: 'LOW', label: 'Low' },
                { value: 'MEDIUM', label: 'Medium' },
                { value: 'HIGH', label: 'High' },
                { value: 'URGENT', label: 'Urgent' }
              ]}
            />
          </div>
        </Card>

        {/* Bulk Actions */}
        {selectedNotifications.length > 0 && (
          <Card className="bg-gray-800 border-gray-700 p-4">
            <div className="flex items-center justify-between">
              <p className="text-white">
                {selectedNotifications.length} notification{selectedNotifications.length > 1 ? 's' : ''} selected
              </p>
              <div className="flex space-x-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkAction('read')}
                >
                  <FiCheck className="mr-1" />
                  Mark Read
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkAction('archive')}
                >
                  <FiArchive className="mr-1" />
                  Archive
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkAction('delete')}
                  className="text-red-400 hover:text-red-300"
                >
                  <FiTrash2 className="mr-1" />
                  Delete
                </Button>
              </div>
            </div>
          </Card>
        )}

        {/* Notifications List */}
        <div className="space-y-3">
          {/* Select All */}
          {notifications.length > 0 && (
            <div className="flex items-center space-x-2 px-4">
              <input
                type="checkbox"
                checked={selectedNotifications.length === notifications.length}
                onChange={selectAllNotifications}
                className="rounded border-gray-600 bg-gray-700 text-green-600"
              />
              <span className="text-sm text-gray-400">Select all</span>
            </div>
          )}

          {notifications.map((notification) => (
            <motion.div
              key={notification.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className={`${
                notification.status === 'UNREAD' ? 'bg-gray-800 border-green-500' : 'bg-gray-800 border-gray-700'
              } border rounded-lg p-4 hover:border-green-500 transition-colors`}
            >
              <div className="flex items-start space-x-4">
                <input
                  type="checkbox"
                  checked={selectedNotifications.includes(notification.id)}
                  onChange={() => toggleNotificationSelection(notification.id)}
                  className="mt-1 rounded border-gray-600 bg-gray-700 text-green-600"
                />

                <div className="flex-shrink-0">
                  <div className="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center text-lg">
                    {getNotificationIcon(notification.type)}
                  </div>
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className="text-white font-medium">{notification.title}</h3>
                        <div className={`w-2 h-2 rounded-full ${getPriorityColor(notification.priority)}`}></div>
                        {notification.status === 'UNREAD' && (
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        )}
                      </div>
                      
                      <p className="text-gray-400 text-sm mb-2">
                        {notificationsService.truncateMessage(notification.message, 120)}
                      </p>

                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <span className="flex items-center">
                          <FiClock className="mr-1" />
                          {formatTimeAgo(notification.createdAt)}
                        </span>
                        
                        <div className="flex items-center space-x-1">
                          {notification.channels.includes('EMAIL') && <FiMail />}
                          {notification.channels.includes('SMS') && <FiSmartphone />}
                          {notification.channels.includes('PUSH') && <FiMonitor />}
                        </div>

                        <Badge variant={notification.priority === 'URGENT' ? 'error' : 'secondary'}>
                          {notification.priority}
                        </Badge>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 ml-4">
                      {notification.status === 'UNREAD' && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleMarkAsRead(notification.id)}
                        >
                          <FiCheck />
                        </Button>
                      )}
                      
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleArchiveNotification(notification.id)}
                      >
                        <FiArchive />
                      </Button>
                      
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDeleteNotification(notification.id)}
                        className="text-red-400 hover:text-red-300"
                      >
                        <FiTrash2 />
                      </Button>
                    </div>
                  </div>

                  {notification.actionRequired && notification.actionUrl && (
                    <div className="mt-3 pt-3 border-t border-gray-700">
                      <Button
                        size="sm"
                        className="bg-green-600 hover:bg-green-700"
                        onClick={() => window.open(notification.actionUrl, '_blank')}
                      >
                        {notification.actionText || 'Take Action'}
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          ))}

          {notifications.length === 0 && (
            <div className="text-center py-12">
              <FiBell className="mx-auto text-6xl text-gray-600 mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">No Notifications</h3>
              <p className="text-gray-400">You're all caught up! Check back later for updates.</p>
            </div>
          )}
        </div>

        {/* Load More */}
        {notifications.length >= (filters.limit || 20) && (
          <div className="text-center">
            <Button
              variant="outline"
              onClick={() => setFilters({ ...filters, limit: (filters.limit || 20) + 20 })}
            >
              Load More Notifications
            </Button>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
