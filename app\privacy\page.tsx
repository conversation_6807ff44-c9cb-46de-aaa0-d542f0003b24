"use client";

import { useEffect } from "react";
import { motion } from "framer-motion";
import AOS from "aos";
import "aos/dist/aos.css";
import Link from "next/link";
import {
  FiSave,
  FiArrowLeft,
  FiShield,
  FiLock,
  FiEye,
  FiUserCheck,
} from "react-icons/fi";

export default function PrivacyPage() {
  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
      easing: "ease-out-cubic",
    });
  }, []);

  const sections = [
    {
      title: "Information We Collect",
      icon: <FiUserCheck className="w-6 h-6" data-oid="4m2yjdd" />,
      content: [
        {
          subtitle: "Personal Information",
          text: "We collect personal information you provide when creating an account, including your name, email address, phone number, date of birth, and government-issued ID for verification purposes.",
        },
        {
          subtitle: "Financial Information",
          text: "To provide our savings and investment services, we collect bank account information, transaction history, and financial goals. This information is encrypted and stored securely.",
        },
        {
          subtitle: "Usage Data",
          text: "We automatically collect information about how you use our app, including device information, IP address, browser type, and app usage patterns to improve our services.",
        },
      ],
    },
    {
      title: "How We Use Your Information",
      icon: <FiEye className="w-6 h-6" data-oid="fwfhj6d" />,
      content: [
        {
          subtitle: "Service Provision",
          text: "We use your information to provide, maintain, and improve our savings and investment services, process transactions, and communicate with you about your account.",
        },
        {
          subtitle: "Security and Fraud Prevention",
          text: "Your information helps us verify your identity, prevent fraud, and maintain the security of our platform and your funds.",
        },
        {
          subtitle: "Personalization",
          text: "We use your data to personalize your experience, provide relevant financial insights, and recommend savings strategies tailored to your goals.",
        },
      ],
    },
    {
      title: "Information Sharing",
      icon: <FiShield className="w-6 h-6" data-oid="6a_-..:" />,
      content: [
        {
          subtitle: "Service Providers",
          text: "We may share your information with trusted third-party service providers who help us operate our platform, including payment processors, identity verification services, and cloud storage providers.",
        },
        {
          subtitle: "Legal Requirements",
          text: "We may disclose your information when required by law, court order, or government regulation, or when we believe disclosure is necessary to protect our rights or the safety of our users.",
        },
        {
          subtitle: "Business Transfers",
          text: "In the event of a merger, acquisition, or sale of assets, your information may be transferred as part of the business transaction, subject to the same privacy protections.",
        },
      ],
    },
    {
      title: "Data Security",
      icon: <FiLock className="w-6 h-6" data-oid="bmoa_u0" />,
      content: [
        {
          subtitle: "Encryption",
          text: "All sensitive data is encrypted both in transit and at rest using industry-standard encryption protocols. Your financial information is protected with bank-level security measures.",
        },
        {
          subtitle: "Access Controls",
          text: "We implement strict access controls to ensure that only authorized personnel can access your personal information, and all access is logged and monitored.",
        },
        {
          subtitle: "Regular Security Audits",
          text: "We conduct regular security audits and penetration testing to identify and address potential vulnerabilities in our systems.",
        },
      ],
    },
  ];

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black text-white overflow-hidden"
      data-oid="gk0n.8r"
    >
      {/* Animated Background */}
      <div className="fixed inset-0 z-0" data-oid="0_e5x-w">
        <div
          className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black animate-gradient"
          data-oid=".vd1yh."
        ></div>
        <div className="absolute inset-0 opacity-10" data-oid="4e.vy2s">
          {[...Array(30)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-green-400 rounded-full particle"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 4}s`,
                animationDuration: `${4 + Math.random() * 2}s`,
              }}
              data-oid="wc4cqhv"
            />
          ))}
        </div>
      </div>

      {/* Navigation */}
      <nav className="relative z-50 px-6 py-4" data-oid="4so-7vj">
        <div
          className="max-w-7xl mx-auto flex items-center justify-between"
          data-oid="2g8ivou"
        >
          <Link
            href="/"
            className="flex items-center space-x-2 hover:opacity-80 transition-opacity"
            data-oid="u5msyv_"
          >
            <FiArrowLeft className="w-5 h-5 text-white" data-oid="gzsr30h" />
            <span className="text-white" data-oid=".id24yz">
              Back to Home
            </span>
          </Link>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center space-x-2"
            data-oid="ah4okxi"
          >
            <div
              className="w-10 h-10 bg-gradient-to-r from-green-400 to-green-600 rounded-lg flex items-center justify-center"
              data-oid="d63b75m"
            >
              <FiSave className="w-6 h-6 text-white" data-oid="k:5k-0j" />
            </div>
            <span className="text-2xl font-bold text-white" data-oid="5d6f--t">
              Koja Save
            </span>
          </motion.div>
        </div>
      </nav>

      {/* Header */}
      <section className="relative z-10 px-6 py-20" data-oid="0qk-bou">
        <div className="max-w-4xl mx-auto text-center" data-oid="45ti2tr">
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-4xl md:text-6xl font-bold mb-6 text-white"
            data-oid="3x_ims."
          >
            Privacy{" "}
            <span className="text-green-400" data-oid="zop1-0r">
              Policy
            </span>
          </motion.h1>

          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-xl text-gray-300 mb-8"
            data-oid="czwhx58"
          >
            Your privacy is important to us. This policy explains how we
            collect, use, and protect your personal information.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="glass rounded-xl p-6 bg-gray-900/30 border border-gray-700"
            data-oid="9e.gd3."
          >
            <p className="text-gray-300" data-oid="e3d1kxb">
              <strong className="text-white" data-oid="kq.ainv">
                Last Updated:
              </strong>{" "}
              December 2024
            </p>
            <p className="text-gray-300 mt-2" data-oid="3zud2hq">
              <strong className="text-white" data-oid="8f-x_5i">
                Effective Date:
              </strong>{" "}
              December 1, 2024
            </p>
          </motion.div>
        </div>
      </section>

      {/* Introduction */}
      <section className="relative z-10 px-6 py-12" data-oid="aam1bix">
        <div className="max-w-4xl mx-auto" data-oid="uyfei44">
          <div
            className="glass rounded-xl p-8 bg-gray-900/30 border border-gray-700"
            data-aos="fade-up"
            data-oid="f__gtey"
          >
            <h2
              className="text-2xl font-bold mb-4 text-white"
              data-oid="9710p70"
            >
              Introduction
            </h2>
            <p className="text-gray-300 mb-4" data-oid="orm01-u">
              Koja Save ("we," "our," or "us") is committed to protecting your
              privacy and ensuring the security of your personal information.
              This Privacy Policy explains how we collect, use, disclose, and
              safeguard your information when you use our mobile application and
              related services (collectively, the "Service").
            </p>
            <p className="text-gray-300" data-oid="kff-lbh">
              By using our Service, you agree to the collection and use of
              information in accordance with this Privacy Policy. If you do not
              agree with our policies and practices, please do not use our
              Service.
            </p>
          </div>
        </div>
      </section>

      {/* Main Sections */}
      <section className="relative z-10 px-6 py-12" data-oid="x95a-zo">
        <div className="max-w-4xl mx-auto space-y-8" data-oid="emrodzq">
          {sections.map((section, index) => (
            <div
              key={index}
              data-aos="fade-up"
              data-aos-delay={index * 100}
              className="glass rounded-xl p-8 bg-gray-900/30 border border-gray-700"
              data-oid="anvh7kp"
            >
              <div className="flex items-center gap-3 mb-6" data-oid="ifwbdi.">
                <div className="text-green-400" data-oid="xyoh_mx">
                  {section.icon}
                </div>
                <h2
                  className="text-2xl font-bold text-white"
                  data-oid="7tkkkvn"
                >
                  {section.title}
                </h2>
              </div>

              <div className="space-y-6" data-oid="_h9w5-1">
                {section.content.map((item, itemIndex) => (
                  <div key={itemIndex} data-oid="1khfz7b">
                    <h3
                      className="text-lg font-semibold mb-2 text-green-400"
                      data-oid="dsqrb1q"
                    >
                      {item.subtitle}
                    </h3>
                    <p className="text-gray-300" data-oid="gsitijy">
                      {item.text}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Your Rights */}
      <section className="relative z-10 px-6 py-12" data-oid="wru22c-">
        <div className="max-w-4xl mx-auto" data-oid="t_2fxo0">
          <div
            className="glass rounded-xl p-8 bg-gray-900/30 border border-gray-700"
            data-aos="fade-up"
            data-oid="2hu1o8h"
          >
            <h2
              className="text-2xl font-bold mb-6 text-white"
              data-oid="ny8bvdu"
            >
              Your Rights and Choices
            </h2>
            <div className="space-y-4" data-oid=".xi:64.">
              <div data-oid="g.a.9os">
                <h3
                  className="text-lg font-semibold mb-2 text-green-400"
                  data-oid="0m3:n0d"
                >
                  Access and Update
                </h3>
                <p className="text-gray-300" data-oid="zrs:lfe">
                  You can access and update your personal information through
                  your account settings in the app.
                </p>
              </div>
              <div data-oid="ai:y2_1">
                <h3
                  className="text-lg font-semibold mb-2 text-green-400"
                  data-oid="_7dfzsl"
                >
                  Data Deletion
                </h3>
                <p className="text-gray-300" data-oid="3eihcnv">
                  You can request deletion of your account and personal data by
                  contacting our support team. Some information may be retained
                  for legal or regulatory requirements.
                </p>
              </div>
              <div data-oid="ts3ha2o">
                <h3
                  className="text-lg font-semibold mb-2 text-green-400"
                  data-oid="l6cq7.2"
                >
                  Marketing Communications
                </h3>
                <p className="text-gray-300" data-oid="8k:rbv8">
                  You can opt out of marketing communications at any time by
                  using the unsubscribe link in emails or adjusting your
                  notification preferences in the app.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Data Retention */}
      <section className="relative z-10 px-6 py-12" data-oid="cwcpzbt">
        <div className="max-w-4xl mx-auto" data-oid="m-bkmtq">
          <div
            className="glass rounded-xl p-8 bg-gray-900/30 border border-gray-700"
            data-aos="fade-up"
            data-oid="pq2qcuf"
          >
            <h2
              className="text-2xl font-bold mb-4 text-white"
              data-oid="6:7-wed"
            >
              Data Retention
            </h2>
            <p className="text-gray-300 mb-4" data-oid="7s0ikkr">
              We retain your personal information for as long as necessary to
              provide our services and comply with legal obligations.
              Specifically:
            </p>
            <ul className="space-y-2 text-gray-300" data-oid="dw1bz6t">
              <li className="flex items-start gap-2" data-oid="75innn5">
                <div
                  className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"
                  data-oid="h7t6s_6"
                ></div>
                Account information is retained while your account is active and
                for 7 years after closure for regulatory compliance
              </li>
              <li className="flex items-start gap-2" data-oid="vt5_.48">
                <div
                  className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"
                  data-oid="s.jye4e"
                ></div>
                Transaction records are kept for 7 years as required by
                financial regulations
              </li>
              <li className="flex items-start gap-2" data-oid="00rr4o7">
                <div
                  className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"
                  data-oid="9pimn1l"
                ></div>
                Marketing data is deleted within 30 days of opt-out requests
              </li>
            </ul>
          </div>
        </div>
      </section>

      {/* Contact Information */}
      <section className="relative z-10 px-6 py-12" data-oid="uuk_5_a">
        <div className="max-w-4xl mx-auto" data-oid="93nbqcm">
          <div
            className="glass rounded-xl p-8 bg-gray-900/30 border border-gray-700"
            data-aos="fade-up"
            data-oid="e9t64h7"
          >
            <h2
              className="text-2xl font-bold mb-4 text-white"
              data-oid="ylgpq2_"
            >
              Contact Us
            </h2>
            <p className="text-gray-300 mb-4" data-oid="s3hot14">
              If you have any questions about this Privacy Policy or our data
              practices, please contact us:
            </p>
            <div className="space-y-2 text-gray-300" data-oid="cb.pc-w">
              <p data-oid="7dcq3ui">
                <strong className="text-white" data-oid="sss4u9-">
                  Email:
                </strong>{" "}
                <EMAIL>
              </p>
              <p data-oid="-n-39yk">
                <strong className="text-white" data-oid=":p3xk.-">
                  Phone:
                </strong>{" "}
                +234 (0) ************
              </p>
              <p data-oid="ej9bi_z">
                <strong className="text-white" data-oid="tesrc41">
                  Address:
                </strong>{" "}
                123 Finance Street, Victoria Island, Lagos, Nigeria
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Updates */}
      <section className="relative z-10 px-6 py-12" data-oid="l:8yt0:">
        <div className="max-w-4xl mx-auto" data-oid="i4gjo6j">
          <div
            className="glass rounded-xl p-8 bg-gray-900/30 border border-gray-700"
            data-aos="fade-up"
            data-oid="10mg9v:"
          >
            <h2
              className="text-2xl font-bold mb-4 text-white"
              data-oid="lac15rd"
            >
              Policy Updates
            </h2>
            <p className="text-gray-300" data-oid="z60swzw">
              We may update this Privacy Policy from time to time. We will
              notify you of any material changes by posting the new Privacy
              Policy on this page and updating the "Last Updated" date. We
              encourage you to review this Privacy Policy periodically for any
              changes.
            </p>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative z-10 px-6 py-20" data-oid="j_46dr7">
        <div
          className="max-w-4xl mx-auto text-center"
          data-aos="fade-up"
          data-oid="urze0m2"
        >
          <div
            className="glass rounded-2xl p-12 bg-gray-900/30 border border-gray-700"
            data-oid="0cuhoq."
          >
            <h2
              className="text-4xl md:text-5xl font-bold mb-6 text-white"
              data-oid=":7fl6qd"
            >
              Questions About Our{" "}
              <span className="text-green-400" data-oid="rog5-k_">
                Privacy Policy?
              </span>
            </h2>
            <p className="text-xl text-gray-300 mb-8" data-oid="rfnzlrg">
              Our team is here to help you understand how we protect your data.
            </p>
            <Link
              href="/contact"
              className="px-8 py-4 bg-gradient-to-r from-green-400 to-green-600 rounded-lg text-lg font-semibold text-white hover:from-green-500 hover:to-green-700 transition-all btn-glow btn-hover-effect hover-lift"
              data-oid=".v0eyoq"
            >
              Contact Us
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer
        className="relative z-10 px-6 py-12 border-t border-gray-700"
        data-oid="c54bwyo"
      >
        <div className="max-w-7xl mx-auto text-center" data-oid="51vufbd">
          <div
            className="flex items-center justify-center space-x-2 mb-4"
            data-oid="glz2mfh"
          >
            <div
              className="w-8 h-8 bg-gradient-to-r from-green-400 to-green-600 rounded-lg flex items-center justify-center"
              data-oid="6zw:tbq"
            >
              <FiSave className="w-5 h-5 text-white" data-oid="41h0r.6" />
            </div>
            <span className="text-xl font-bold text-white" data-oid=".y1cnc:">
              Koja Save
            </span>
          </div>
          <p className="text-gray-400" data-oid="zx0ttt5">
            Building the future of personal finance, one save at a time.
          </p>
        </div>
      </footer>
    </div>
  );
}
