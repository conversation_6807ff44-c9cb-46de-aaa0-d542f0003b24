"use client";

import { useEffect } from "react";
import { motion } from "framer-motion";
import { Button } from "./ui/button";
import {
  FiArrowRight,
  FiPlay,
  FiTrendingUp,
  FiShield,
} from "react-icons/fi";
import KojaSaveIcon from "./KojaSaveIcon";
import AOS from "aos";

export default function Hero() {
  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
      easing: "ease-out-cubic",
    });
  }, []);

  return (
    <section
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-gray-900 via-black to-gray-900"
      data-oid="sjw8gmd"
    >
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden" data-oid="-oo_9yk">
        <div
          className="absolute -top-40 -right-40 w-80 h-80 bg-green-500/20 rounded-full blur-3xl animate-pulse"
          data-oid="zcoj3h_"
        ></div>
        <div
          className="absolute -bottom-40 -left-40 w-80 h-80 bg-green-400/20 rounded-full blur-3xl animate-pulse delay-1000"
          data-oid="zw:k15:"
        ></div>
        <div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-green-600/10 rounded-full blur-3xl animate-pulse delay-2000"
          data-oid="6eg55xp"
        ></div>
      </div>

      {/* Floating icons */}
      <div className="absolute inset-0 pointer-events-none" data-oid="odb46lh">
        <motion.div
          className="absolute top-20 left-20 text-green-400/30"
          animate={{ y: [0, -20, 0] }}
          transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
          data-oid="osrx7ed"
        >
          <KojaSaveIcon size={40} className="text-green-400/30" />
        </motion.div>
        <motion.div
          className="absolute top-40 right-32 text-green-500/30"
          animate={{ y: [0, -15, 0] }}
          transition={{
            duration: 2.5,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 0.5,
          }}
          data-oid="sw7fipe"
        >
          <FiTrendingUp size={35} data-oid="i5eds4n" />
        </motion.div>
        <motion.div
          className="absolute bottom-32 left-32 text-green-300/30"
          animate={{ y: [0, -25, 0] }}
          transition={{
            duration: 3.5,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1,
          }}
          data-oid="__yej1f"
        >
          <FiShield size={45} data-oid="llaaeai" />
        </motion.div>
      </div>

      <div
        className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center"
        data-oid="0by9gdq"
      >
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          data-oid="hero-content"
        >
          {/* Badge */}
          <motion.div
            className="inline-flex items-center px-4 py-2 rounded-full bg-green-500/10 border border-green-500/20 text-green-400 text-sm font-medium mb-8"
            whileHover={{ scale: 1.05 }}
            data-aos="fade-up"
            data-aos-delay="100"
            data-oid="kys69r1"
          >
            <span
              className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"
              data-oid="8186q.k"
            ></span>
            Your Smart Savings Companion
          </motion.div>

          {/* Main heading */}
          <h1
            className="text-5xl md:text-7xl font-bold mb-6 leading-tight"
            data-aos="fade-up"
            data-aos-delay="200"
            data-oid="2my0916"
          >
            <span className="text-white" data-oid="fhqkqmd">
              Save
            </span>{" "}
            <span className="text-gradient-animate" data-oid="nbgbx5:">
              Smarter
            </span>
            <br data-oid="cls9so9" />
            <span className="text-white" data-oid="uo3vcmo">
              Live
            </span>{" "}
            <span className="text-gradient-animate" data-oid="a:mt2k5">
              Better
            </span>
          </h1>

          {/* Subtitle */}
          <p
            className="text-xl md:text-2xl text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed"
            data-aos="fade-up"
            data-aos-delay="300"
            data-oid="9ilvfpp"
          >
            Transform your financial future with Koja Save - the intelligent
            savings app that helps you build wealth effortlessly through
            automated savings, smart insights, and personalized goals.
          </p>

          {/* CTA Buttons */}
          <div
            className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16"
            data-aos="fade-up"
            data-aos-delay="400"
            data-oid="n7xl0-:"
          >
            <Button
              variant="primary"
              size="lg"
              className="group"
              data-oid="t40qnp6"
            >
              Get Started Free
              <FiArrowRight
                className="ml-2 group-hover:translate-x-1 transition-transform duration-300"
                data-oid="_qjmvlc"
              />
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="group"
              data-oid="_8kpahp"
            >
              <FiPlay
                className="mr-2 group-hover:scale-110 transition-transform duration-300"
                data-oid="lz061:x"
              />
              Watch Demo
            </Button>
          </div>

          {/* Stats */}
          <div
            className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
            data-aos="fade-up"
            data-aos-delay="500"
            data-oid="ey0:.e0"
          >
            <motion.div
              className="text-center"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 300 }}
              data-oid="ctej1r0"
            >
              <div
                className="text-3xl md:text-4xl font-bold text-green-400 mb-2"
                data-oid="2aan3:z"
              >
                $2.5M+
              </div>
              <div className="text-gray-400" data-oid="8b8rtur">
                Total Saved
              </div>
            </motion.div>
            <motion.div
              className="text-center"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 300 }}
              data-oid="ofs7--q"
            >
              <div
                className="text-3xl md:text-4xl font-bold text-green-400 mb-2"
                data-oid="f2urzuc"
              >
                50K+
              </div>
              <div className="text-gray-400" data-oid="d76p7zz">
                Happy Users
              </div>
            </motion.div>
            <motion.div
              className="text-center"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 300 }}
              data-oid="g5gnfw3"
            >
              <div
                className="text-3xl md:text-4xl font-bold text-green-400 mb-2"
                data-oid="s.6sy9w"
              >
                4.9★
              </div>
              <div className="text-gray-400" data-oid="6x.9pz9">
                App Rating
              </div>
            </motion.div>
          </div>
        </motion.div>

        {/* Scroll indicator */}
        <motion.div
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          data-oid="scroll-indicator"
        >
          <div
            className="w-6 h-10 border-2 border-green-400/50 rounded-full flex justify-center"
            data-oid="60q1cso"
          >
            <div
              className="w-1 h-3 bg-green-400 rounded-full mt-2 animate-pulse"
              data-oid="b.s3rp3"
            ></div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
