// Simple test script to verify authentication integration
const API_BASE_URL = 'http://localhost:8000';

async function testLogin() {
  console.log('🧪 Testing Login Endpoint...');
  
  try {
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Demo123!'
      })
    });

    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log('✅ Login successful!');
      console.log('📧 User:', data.data.user.email);
      console.log('👤 Name:', `${data.data.user.firstName} ${data.data.user.lastName}`);
      console.log('💰 Balance:', data.data.user.balance);
      console.log('🔑 Token:', data.data.token.substring(0, 20) + '...');
      return data.data.token;
    } else {
      console.log('❌ Login failed:', data.message);
      return null;
    }
  } catch (error) {
    console.log('❌ Login error:', error.message);
    return null;
  }
}

async function testProfile(token) {
  console.log('\n🧪 Testing Profile Endpoint...');
  
  try {
    const response = await fetch(`${API_BASE_URL}/auth/me`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log('✅ Profile fetch successful!');
      console.log('📧 User:', data.data.user.email);
      console.log('👤 Role:', data.data.user.role);
      return true;
    } else {
      console.log('❌ Profile fetch failed:', data.message);
      return false;
    }
  } catch (error) {
    console.log('❌ Profile error:', error.message);
    return false;
  }
}

async function testSignup() {
  console.log('\n🧪 Testing Signup Endpoint...');
  
  try {
    const response = await fetch(`${API_BASE_URL}/auth/signup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: `test${Date.now()}@betterinterest.com`,
        password: 'Test123!',
        firstName: 'Test',
        lastName: 'User'
      })
    });

    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log('✅ Signup successful!');
      console.log('📧 User:', data.data.user.email);
      console.log('💰 Balance:', data.data.user.balance);
      return true;
    } else {
      console.log('❌ Signup failed:', data.message);
      return false;
    }
  } catch (error) {
    console.log('❌ Signup error:', error.message);
    return false;
  }
}

async function testRefreshToken(refreshToken) {
  console.log('\n🧪 Testing Refresh Token Endpoint...');
  
  try {
    const response = await fetch(`${API_BASE_URL}/auth/refresh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        refreshToken: refreshToken
      })
    });

    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log('✅ Token refresh successful!');
      console.log('🔑 New Token:', data.data.token.substring(0, 20) + '...');
      return true;
    } else {
      console.log('❌ Token refresh failed:', data.message);
      return false;
    }
  } catch (error) {
    console.log('❌ Token refresh error:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting Authentication Integration Tests\n');
  
  // Test login with demo credentials
  const token = await testLogin();
  
  if (token) {
    // Test profile endpoint with token
    await testProfile(token);
  }
  
  // Test signup
  await testSignup();
  
  // Test refresh token (using demo refresh token)
  await testRefreshToken('demo_refresh_123_' + Date.now());
  
  console.log('\n🏁 Tests completed!');
}

// Run tests if this script is executed directly
if (typeof window === 'undefined') {
  runTests();
}

module.exports = { testLogin, testProfile, testSignup, testRefreshToken, runTests };
