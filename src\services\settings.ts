import { 
  AppSettings, 
  FinancialSettings,
  SecuritySettings,
  NotificationSettings,
  FeatureSettings,
  UpdateSettingData,
  SettingsCategory,
  SettingsValidationResult,
  SettingsBackup,
  CreateSettingsBackupData,
  RestoreSettingsData,
  ApiResponse
} from '../types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

class SettingsService {
  private getAuthHeaders() {
    const token = localStorage.getItem('auth_token');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` })
    };
  }

  // General Settings Management
  async getAllSettings(): Promise<AppSettings[]> {
    const response = await fetch(`${API_BASE_URL}/settings`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch settings');
    }

    return response.json();
  }

  async getSettingsByCategory(category: string): Promise<AppSettings[]> {
    const response = await fetch(`${API_BASE_URL}/settings/category/${category}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch settings by category');
    }

    return response.json();
  }

  async getSettingByKey(key: string): Promise<AppSettings> {
    const response = await fetch(`${API_BASE_URL}/settings/${key}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch setting');
    }

    return response.json();
  }

  async updateSetting(data: UpdateSettingData): Promise<AppSettings> {
    const response = await fetch(`${API_BASE_URL}/settings/${data.key}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update setting');
    }

    return response.json();
  }

  async bulkUpdateSettings(settings: UpdateSettingData[]): Promise<AppSettings[]> {
    const response = await fetch(`${API_BASE_URL}/settings/bulk`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ settings })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to bulk update settings');
    }

    return response.json();
  }

  async resetSetting(key: string): Promise<AppSettings> {
    const response = await fetch(`${API_BASE_URL}/settings/${key}/reset`, {
      method: 'POST',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to reset setting');
    }

    return response.json();
  }

  // Specialized Settings Getters
  async getFinancialSettings(): Promise<FinancialSettings> {
    const response = await fetch(`${API_BASE_URL}/settings/financial`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch financial settings');
    }

    return response.json();
  }

  async getSecuritySettings(): Promise<SecuritySettings> {
    const response = await fetch(`${API_BASE_URL}/settings/security`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch security settings');
    }

    return response.json();
  }

  async getNotificationSettings(): Promise<NotificationSettings> {
    const response = await fetch(`${API_BASE_URL}/settings/notifications`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch notification settings');
    }

    return response.json();
  }

  async getFeatureSettings(): Promise<FeatureSettings> {
    const response = await fetch(`${API_BASE_URL}/settings/features`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch feature settings');
    }

    return response.json();
  }

  // Settings Categories
  async getSettingsCategories(): Promise<SettingsCategory[]> {
    const response = await fetch(`${API_BASE_URL}/settings/categories`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch settings categories');
    }

    return response.json();
  }

  // Settings Validation
  async validateSettings(settings: UpdateSettingData[]): Promise<SettingsValidationResult> {
    const response = await fetch(`${API_BASE_URL}/settings/validate`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ settings })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to validate settings');
    }

    return response.json();
  }

  async validateSetting(key: string, value: any): Promise<SettingsValidationResult> {
    const response = await fetch(`${API_BASE_URL}/settings/${key}/validate`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ value })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to validate setting');
    }

    return response.json();
  }

  // Settings Backup and Restore
  async createBackup(data: CreateSettingsBackupData): Promise<SettingsBackup> {
    const response = await fetch(`${API_BASE_URL}/settings/backup`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to create settings backup');
    }

    return response.json();
  }

  async getBackups(): Promise<SettingsBackup[]> {
    const response = await fetch(`${API_BASE_URL}/settings/backups`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch settings backups');
    }

    return response.json();
  }

  async restoreBackup(data: RestoreSettingsData): Promise<{ restored: number; skipped: number }> {
    const response = await fetch(`${API_BASE_URL}/settings/restore`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to restore settings backup');
    }

    return response.json();
  }

  async deleteBackup(backupId: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/settings/backups/${backupId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to delete settings backup');
    }
  }

  // Public Settings (for frontend configuration)
  async getPublicSettings(): Promise<Record<string, any>> {
    const response = await fetch(`${API_BASE_URL}/settings/public`, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch public settings');
    }

    return response.json();
  }

  // Settings Import/Export
  async exportSettings(categories?: string[]): Promise<{ downloadUrl: string; fileName: string }> {
    const params = new URLSearchParams();
    if (categories) {
      categories.forEach(category => params.append('categories', category));
    }

    const response = await fetch(`${API_BASE_URL}/settings/export?${params}`, {
      method: 'POST',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to export settings');
    }

    return response.json();
  }

  async importSettings(file: File, overwrite: boolean = false): Promise<{ imported: number; skipped: number; errors: string[] }> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('overwrite', overwrite.toString());

    const token = localStorage.getItem('auth_token');
    const response = await fetch(`${API_BASE_URL}/settings/import`, {
      method: 'POST',
      headers: {
        ...(token && { Authorization: `Bearer ${token}` })
      },
      body: formData
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to import settings');
    }

    return response.json();
  }

  // Utility Methods
  formatSettingValue(setting: AppSettings): string {
    switch (setting.type) {
      case 'BOOLEAN':
        return setting.value ? 'Enabled' : 'Disabled';
      case 'NUMBER':
        if (setting.key.includes('rate') || setting.key.includes('percentage')) {
          return `${setting.value}%`;
        }
        if (setting.key.includes('amount') || setting.key.includes('fee')) {
          return this.formatCurrency(setting.value as number);
        }
        return setting.value.toString();
      case 'STRING':
        return setting.value as string;
      case 'JSON':
      case 'ARRAY':
        return JSON.stringify(setting.value);
      default:
        return setting.value?.toString() || '';
    }
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN'
    }).format(amount);
  }

  getCategoryIcon(category: string): string {
    const icons: Record<string, string> = {
      GENERAL: '⚙️',
      FINANCIAL: '💰',
      SECURITY: '🔒',
      NOTIFICATIONS: '🔔',
      FEATURES: '🎛️',
      LIMITS: '📊',
      PENALTIES: '⚠️'
    };
    
    return icons[category] || '⚙️';
  }

  getCategoryColor(category: string): string {
    const colors: Record<string, string> = {
      GENERAL: '#6B7280',
      FINANCIAL: '#10B981',
      SECURITY: '#EF4444',
      NOTIFICATIONS: '#3B82F6',
      FEATURES: '#8B5CF6',
      LIMITS: '#F59E0B',
      PENALTIES: '#DC2626'
    };
    
    return colors[category] || '#6B7280';
  }

  validateSettingValue(setting: AppSettings, value: any): { isValid: boolean; error?: string } {
    const rules = setting.validationRules;
    if (!rules) return { isValid: true };

    // Required validation
    if (rules.required && (value === null || value === undefined || value === '')) {
      return { isValid: false, error: 'This field is required' };
    }

    // Type-specific validation
    switch (setting.type) {
      case 'NUMBER':
        const numValue = Number(value);
        if (isNaN(numValue)) {
          return { isValid: false, error: 'Must be a valid number' };
        }
        if (rules.min !== undefined && numValue < rules.min) {
          return { isValid: false, error: `Must be at least ${rules.min}` };
        }
        if (rules.max !== undefined && numValue > rules.max) {
          return { isValid: false, error: `Must be at most ${rules.max}` };
        }
        break;

      case 'STRING':
        const strValue = String(value);
        if (rules.pattern && !new RegExp(rules.pattern).test(strValue)) {
          return { isValid: false, error: 'Invalid format' };
        }
        if (rules.options && !rules.options.includes(strValue)) {
          return { isValid: false, error: `Must be one of: ${rules.options.join(', ')}` };
        }
        break;

      case 'BOOLEAN':
        if (typeof value !== 'boolean') {
          return { isValid: false, error: 'Must be true or false' };
        }
        break;
    }

    return { isValid: true };
  }

  groupSettingsByCategory(settings: AppSettings[]): Record<string, AppSettings[]> {
    return settings.reduce((groups, setting) => {
      const category = setting.category;
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(setting);
      return groups;
    }, {} as Record<string, AppSettings[]>);
  }

  sortSettingsByDisplayOrder(settings: AppSettings[]): AppSettings[] {
    return settings.sort((a, b) => a.displayOrder - b.displayOrder);
  }
}

export const settingsService = new SettingsService();
