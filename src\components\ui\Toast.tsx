"use client";

import React from 'react';
import toast, { Toaster, ToastBar } from 'react-hot-toast';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>Check, FiX, FiAlertTriangle, FiInfo } from 'react-icons/fi';

// Toast notification functions
export const showToast = {
  success: (message: string) => {
    toast.success(message, {
      duration: 4000,
      position: 'top-right',
      style: {
        background: '#065F46',
        color: '#ECFDF5',
        border: '1px solid #10B981',
      },
      iconTheme: {
        primary: '#10B981',
        secondary: '#ECFDF5',
      },
    });
  },

  error: (message: string) => {
    toast.error(message, {
      duration: 5000,
      position: 'top-right',
      style: {
        background: '#7F1D1D',
        color: '#FEF2F2',
        border: '1px solid #EF4444',
      },
      iconTheme: {
        primary: '#EF4444',
        secondary: '#FEF2F2',
      },
    });
  },

  warning: (message: string) => {
    toast(message, {
      duration: 4000,
      position: 'top-right',
      icon: '⚠️',
      style: {
        background: '#92400E',
        color: '#FFFBEB',
        border: '1px solid #F59E0B',
      },
    });
  },

  info: (message: string) => {
    toast(message, {
      duration: 4000,
      position: 'top-right',
      icon: 'ℹ️',
      style: {
        background: '#1E3A8A',
        color: '#EFF6FF',
        border: '1px solid #3B82F6',
      },
    });
  },

  loading: (message: string) => {
    return toast.loading(message, {
      position: 'top-right',
      style: {
        background: '#374151',
        color: '#F9FAFB',
        border: '1px solid #6B7280',
      },
    });
  },

  dismiss: (toastId?: string) => {
    toast.dismiss(toastId);
  },

  promise: <T,>(
    promise: Promise<T>,
    messages: {
      loading: string;
      success: string | ((data: T) => string);
      error: string | ((error: any) => string);
    }
  ) => {
    return toast.promise(promise, messages, {
      position: 'top-right',
      style: {
        background: '#374151',
        color: '#F9FAFB',
        border: '1px solid #6B7280',
      },
      success: {
        style: {
          background: '#065F46',
          color: '#ECFDF5',
          border: '1px solid #10B981',
        },
        iconTheme: {
          primary: '#10B981',
          secondary: '#ECFDF5',
        },
      },
      error: {
        style: {
          background: '#7F1D1D',
          color: '#FEF2F2',
          border: '1px solid #EF4444',
        },
        iconTheme: {
          primary: '#EF4444',
          secondary: '#FEF2F2',
        },
      },
    });
  },
};

// Custom Toast Component
export function CustomToaster() {
  return (
    <Toaster
      position="top-right"
      reverseOrder={false}
      gutter={8}
      containerClassName=""
      containerStyle={{}}
      toastOptions={{
        className: '',
        duration: 4000,
        style: {
          background: '#1F2937',
          color: '#F9FAFB',
          border: '1px solid #374151',
          borderRadius: '8px',
          fontSize: '14px',
          fontWeight: '500',
          padding: '12px 16px',
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        },
        success: {
          iconTheme: {
            primary: '#10B981',
            secondary: '#ECFDF5',
          },
        },
        error: {
          iconTheme: {
            primary: '#EF4444',
            secondary: '#FEF2F2',
          },
        },
      }}
    >
      {(t) => (
        <ToastBar toast={t}>
          {({ icon, message }) => (
            <motion.div
              initial={{ opacity: 0, y: -50, scale: 0.3 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -50, scale: 0.3 }}
              transition={{ 
                type: "spring", 
                stiffness: 500, 
                damping: 30,
                duration: 0.3 
              }}
              className="flex items-center space-x-3"
            >
              <div className="flex-shrink-0">
                {icon}
              </div>
              <div className="flex-1">
                {message}
              </div>
              {t.type !== 'loading' && (
                <button
                  onClick={() => toast.dismiss(t.id)}
                  className="flex-shrink-0 p-1 rounded-full hover:bg-gray-600 transition-colors"
                >
                  <FiX className="w-4 h-4" />
                </button>
              )}
            </motion.div>
          )}
        </ToastBar>
      )}
    </Toaster>
  );
}

// Notification Badge Component
export function NotificationBadge({ 
  count, 
  type = 'default',
  onClick 
}: { 
  count: number; 
  type?: 'default' | 'success' | 'warning' | 'error';
  onClick?: () => void;
}) {
  if (count === 0) return null;

  const typeStyles = {
    default: 'bg-gray-500',
    success: 'bg-green-500',
    warning: 'bg-yellow-500',
    error: 'bg-red-500',
  };

  return (
    <motion.div
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      exit={{ scale: 0 }}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
      className={`
        absolute -top-2 -right-2 w-5 h-5 rounded-full flex items-center justify-center
        text-white text-xs font-bold cursor-pointer ${typeStyles[type]}
      `}
      onClick={onClick}
    >
      {count > 99 ? '99+' : count}
    </motion.div>
  );
}

// In-app Notification Component
export function InAppNotification({
  title,
  message,
  type = 'info',
  isVisible,
  onClose,
  action,
}: {
  title: string;
  message: string;
  type?: 'info' | 'success' | 'warning' | 'error';
  isVisible: boolean;
  onClose: () => void;
  action?: {
    label: string;
    onClick: () => void;
  };
}) {
  const typeConfig = {
    info: {
      icon: FiInfo,
      bgColor: 'bg-blue-500/10',
      borderColor: 'border-blue-500/30',
      iconColor: 'text-blue-400',
      titleColor: 'text-blue-300',
    },
    success: {
      icon: FiCheck,
      bgColor: 'bg-green-500/10',
      borderColor: 'border-green-500/30',
      iconColor: 'text-green-400',
      titleColor: 'text-green-300',
    },
    warning: {
      icon: FiAlertTriangle,
      bgColor: 'bg-yellow-500/10',
      borderColor: 'border-yellow-500/30',
      iconColor: 'text-yellow-400',
      titleColor: 'text-yellow-300',
    },
    error: {
      icon: FiX,
      bgColor: 'bg-red-500/10',
      borderColor: 'border-red-500/30',
      iconColor: 'text-red-400',
      titleColor: 'text-red-300',
    },
  };

  const config = typeConfig[type];
  const Icon = config.icon;

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -20, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: -20, scale: 0.95 }}
          transition={{ duration: 0.3 }}
          className={`
            fixed top-4 right-4 z-50 max-w-sm w-full
            ${config.bgColor} ${config.borderColor} border rounded-lg p-4
            backdrop-blur-sm shadow-lg
          `}
        >
          <div className="flex items-start space-x-3">
            <div className={`flex-shrink-0 p-1 rounded-full ${config.bgColor}`}>
              <Icon className={`w-5 h-5 ${config.iconColor}`} />
            </div>
            
            <div className="flex-1 min-w-0">
              <h4 className={`text-sm font-semibold ${config.titleColor}`}>
                {title}
              </h4>
              <p className="text-sm text-gray-300 mt-1">
                {message}
              </p>
              
              {action && (
                <button
                  onClick={action.onClick}
                  className={`
                    mt-2 text-sm font-medium ${config.iconColor} 
                    hover:underline focus:outline-none
                  `}
                >
                  {action.label}
                </button>
              )}
            </div>
            
            <button
              onClick={onClose}
              className="flex-shrink-0 p-1 rounded-full hover:bg-gray-700 transition-colors"
            >
              <FiX className="w-4 h-4 text-gray-400" />
            </button>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
