"use client";

import React, { forwardRef } from 'react';

interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
  helperText?: string;
  variant?: 'default' | 'filled' | 'outline';
  resize?: 'none' | 'vertical' | 'horizontal' | 'both';
}

export const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(({
  label,
  error,
  helperText,
  variant = 'default',
  resize = 'vertical',
  className = '',
  ...props
}, ref) => {
  const baseClasses = `
    w-full px-4 py-3 rounded-lg transition-all duration-200
    focus:outline-none focus:ring-2 focus:ring-green-500/50
    disabled:opacity-50 disabled:cursor-not-allowed
    min-h-[100px]
  `;

  const variantClasses = {
    default: `
      bg-gray-800 border border-gray-700 text-white
      placeholder-gray-400 hover:border-gray-600
      focus:border-green-500
    `,
    filled: `
      bg-gray-700 border-0 text-white
      placeholder-gray-400 hover:bg-gray-600
    `,
    outline: `
      bg-transparent border-2 border-gray-600 text-white
      placeholder-gray-400 hover:border-gray-500
      focus:border-green-500
    `
  };

  const resizeClasses = {
    none: 'resize-none',
    vertical: 'resize-y',
    horizontal: 'resize-x',
    both: 'resize'
  };

  const textareaClasses = `
    ${baseClasses} 
    ${variantClasses[variant]} 
    ${resizeClasses[resize]} 
    ${className} 
    ${error ? 'border-red-500 focus:border-red-500 focus:ring-red-500/50' : ''}
  `;

  return (
    <div className="w-full">
      {label && (
        <label className="block text-sm font-medium text-gray-300 mb-2">
          {label}
        </label>
      )}
      
      <textarea
        ref={ref}
        className={textareaClasses}
        {...props}
      />
      
      {error && (
        <p className="mt-1 text-sm text-red-400">
          {error}
        </p>
      )}
      
      {helperText && !error && (
        <p className="mt-1 text-sm text-gray-500">
          {helperText}
        </p>
      )}
    </div>
  );
});

Textarea.displayName = 'Textarea';

export default Textarea;
