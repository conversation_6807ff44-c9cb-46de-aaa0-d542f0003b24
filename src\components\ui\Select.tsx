"use client";

import React, { forwardRef } from 'react';
import { FiChevronDown } from 'react-icons/fi';

interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

interface SelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'children'> {
  label?: string;
  error?: string;
  helperText?: string;
  options: SelectOption[];
  placeholder?: string;
  variant?: 'default' | 'filled' | 'outline';
}

export const Select = forwardRef<HTMLSelectElement, SelectProps>(({
  label,
  error,
  helperText,
  options,
  placeholder,
  variant = 'default',
  className = '',
  ...props
}, ref) => {
  const baseClasses = `
    w-full px-4 py-3 pr-12 rounded-lg transition-all duration-200
    focus:outline-none focus:ring-2 focus:ring-green-500/50
    disabled:opacity-50 disabled:cursor-not-allowed
    appearance-none cursor-pointer
  `;

  const variantClasses = {
    default: `
      bg-gray-800 border border-gray-700 text-white
      hover:border-gray-600 focus:border-green-500
    `,
    filled: `
      bg-gray-700 border-0 text-white
      hover:bg-gray-600
    `,
    outline: `
      bg-transparent border-2 border-gray-600 text-white
      hover:border-gray-500 focus:border-green-500
    `
  };

  const selectClasses = `
    ${baseClasses} 
    ${variantClasses[variant]} 
    ${className} 
    ${error ? 'border-red-500 focus:border-red-500 focus:ring-red-500/50' : ''}
  `;

  return (
    <div className="w-full">
      {label && (
        <label className="block text-sm font-medium text-gray-300 mb-2">
          {label}
        </label>
      )}
      
      <div className="relative">
        <select
          ref={ref}
          className={selectClasses}
          {...props}
        >
          {placeholder && (
            <option value="" disabled>
              {placeholder}
            </option>
          )}
          {options.map((option) => (
            <option
              key={option.value}
              value={option.value}
              disabled={option.disabled}
              className="bg-gray-800 text-white"
            >
              {option.label}
            </option>
          ))}
        </select>
        
        <div className="absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none">
          <FiChevronDown className="w-5 h-5 text-gray-400" />
        </div>
      </div>
      
      {error && (
        <p className="mt-1 text-sm text-red-400">
          {error}
        </p>
      )}
      
      {helperText && !error && (
        <p className="mt-1 text-sm text-gray-500">
          {helperText}
        </p>
      )}
    </div>
  );
});

Select.displayName = 'Select';

export default Select;
