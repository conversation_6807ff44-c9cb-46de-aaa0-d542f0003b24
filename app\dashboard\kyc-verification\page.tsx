"use client";

import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';
import {
    FiAlertTriangle,
    FiCamera,
    FiCheck,
    FiClock,
    FiEye,
    FiFile,
    FiHome,
    FiInfo,
    FiRefreshCw,
    FiShield,
    FiUpload,
    FiUser,
    FiX
} from 'react-icons/fi';
import DashboardLayout from '../../../src/components/dashboard/DashboardLayout';
import { Badge } from '../../../src/components/ui/Badge';
import { Button } from '../../../src/components/ui/Button';
import { Card } from '../../../src/components/ui/Card';
import { Input } from '../../../src/components/ui/Input';
import { Modal } from '../../../src/components/ui/Modal';
import { Select } from '../../../src/components/ui/Select';
import { toast } from '../../../src/components/ui/Toast';
import { kycService } from '../../../src/services';
import { KYC, KYCDocument } from '../../../src/types';

// Local type definitions based on KYC interface
type PersonalInfo = {
  firstName: string;
  lastName: string;
  middleName?: string;
  dateOfBirth: string;
  gender: string;
  nationality: string;
  stateOfOrigin?: string;
  localGovernment?: string;
  occupation?: string;
  employerName?: string;
  annualIncome?: string;
};

type AddressInfo = {
  street: string;
  city: string;
  state: string;
  postalCode?: string;
  country: string;
  landmark?: string;
};

type ContactInfo = {
  email: string;
  phoneNumber: string;
  alternatePhoneNumber?: string;
};

const documentTypes = [
  { value: 'NATIONAL_ID', label: 'National ID Card', icon: '🆔', required: true },
  { value: 'PASSPORT', label: 'International Passport', icon: '📘', required: false },
  { value: 'DRIVERS_LICENSE', label: 'Driver\'s License', icon: '🚗', required: false },
  { value: 'VOTERS_CARD', label: 'Voter\'s Card', icon: '🗳️', required: false },
  { value: 'UTILITY_BILL', label: 'Utility Bill', icon: '📄', required: true },
  { value: 'BANK_STATEMENT', label: 'Bank Statement', icon: '🏦', required: false },
  { value: 'SELFIE', label: 'Selfie with ID', icon: '🤳', required: true },
  { value: 'SIGNATURE', label: 'Signature Sample', icon: '✍️', required: true }
];

const kycLevels = [
  {
    level: 'BASIC',
    name: 'Basic Verification',
    description: 'Basic account verification with limited features',
    requirements: ['NATIONAL_ID', 'SELFIE'],
    limits: { deposit: 100000, withdrawal: 50000, monthly: 500000 },
    color: 'bg-green-600'
  },
  {
    level: 'INTERMEDIATE',
    name: 'Intermediate Verification',
    description: 'Enhanced verification with higher limits',
    requirements: ['NATIONAL_ID', 'UTILITY_BILL', 'SELFIE', 'SIGNATURE'],
    limits: { deposit: 1000000, withdrawal: 500000, monthly: 5000000 },
    color: 'bg-blue-600'
  },
  {
    level: 'ADVANCED',
    name: 'Advanced Verification',
    description: 'Full verification with unlimited access',
    requirements: ['NATIONAL_ID', 'UTILITY_BILL', 'BANK_STATEMENT', 'SELFIE', 'SIGNATURE'],
    limits: { deposit: 'Unlimited', withdrawal: 'Unlimited', monthly: 'Unlimited' },
    color: 'bg-purple-600'
  }
];

export default function KYCVerificationPage() {
  const [kyc, setKyc] = useState<KYC | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedLevel, setSelectedLevel] = useState('BASIC');
  const [showDocumentModal, setShowDocumentModal] = useState(false);
  const [selectedDocumentType, setSelectedDocumentType] = useState('');
  const [uploadingDocument, setUploadingDocument] = useState(false);

  const [personalInfo, setPersonalInfo] = useState<PersonalInfo>({
    firstName: '',
    lastName: '',
    middleName: '',
    dateOfBirth: '',
    gender: '',
    nationality: 'Nigerian',
    stateOfOrigin: '',
    localGovernment: '',
    occupation: '',
    employerName: '',
    annualIncome: ''
  });

  const [contactInfo, setContactInfo] = useState<ContactInfo>({
    email: '',
    phoneNumber: '',
    alternatePhoneNumber: ''
  });

  const [addressInfo, setAddressInfo] = useState<AddressInfo>({
    street: '',
    city: '',
    state: '',
    postalCode: '',
    country: 'Nigeria',
    landmark: ''
  });

  const [documents, setDocuments] = useState<KYCDocument[]>([]);

  useEffect(() => {
    loadKYCData();
  }, []);

  const loadKYCData = async () => {
    try {
      setLoading(true);
      const response = await kycService.getUserKYC();
      if (response) {
        setKyc(response);
        setPersonalInfo(response.personalInfo);
        setContactInfo(response.contactInfo);
        setAddressInfo(response.addressInfo);
        setDocuments(response.documents);
        setSelectedLevel(response.level);
        
        // Determine current step based on completion
        if (!response.personalInfo.firstName) {
          setCurrentStep(1);
        } else if (!response.addressInfo.street) {
          setCurrentStep(2);
        } else if (response.documents.length === 0) {
          setCurrentStep(3);
        } else {
          setCurrentStep(4);
        }
      }
    } catch (error) {
      console.error('Failed to load KYC data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSavePersonalInfo = async () => {
    try {
      if (!personalInfo.firstName || !personalInfo.lastName || !personalInfo.dateOfBirth) {
        toast.error('Please fill in all required fields');
        return;
      }

      if (kyc?.id) {
        await kycService.updateKYC(kyc.id, { personalInfo });
      }
      toast.success('Personal information saved');
      setCurrentStep(2);
    } catch (error: any) {
      toast.error(error.message || 'Failed to save personal information');
    }
  };

  const handleSaveAddressInfo = async () => {
    try {
      if (!addressInfo.street || !addressInfo.city || !addressInfo.state) {
        toast.error('Please fill in all required fields');
        return;
      }

      if (kyc?.id) {
        await kycService.updateKYC(kyc.id, { addressInfo });
      }
      toast.success('Address information saved');
      setCurrentStep(3);
    } catch (error: any) {
      toast.error(error.message || 'Failed to save address information');
    }
  };

  const handleDocumentUpload = async (file: File) => {
    try {
      setUploadingDocument(true);
      const response = await kycService.uploadDocument({
        type: selectedDocumentType as any,
        file,
        description: `${selectedDocumentType} document`
      });

      setDocuments([...documents, response.document]);
      setShowDocumentModal(false);
      toast.success('Document uploaded successfully');
    } catch (error: any) {
      toast.error(error.message || 'Failed to upload document');
    } finally {
      setUploadingDocument(false);
    }
  };

  const handleSubmitKYC = async () => {
    try {
      const requiredDocs = kycLevels.find(l => l.level === selectedLevel)?.requirements || [];
      const uploadedTypes = documents.map(d => d.type);
      const missingDocs = requiredDocs.filter(type => !uploadedTypes.includes(type));

      if (missingDocs.length > 0) {
        toast.error(`Missing required documents: ${missingDocs.join(', ')}`);
        return;
      }

      await kycService.submitKYC({
        level: selectedLevel as any,
        personalInfo,
        contactInfo,
        addressInfo
      });

      toast.success('KYC submitted for review');
      loadKYCData();
    } catch (error: any) {
      toast.error(error.message || 'Failed to submit KYC');
    }
  };

  const formatCurrency = (amount: number | string) => {
    if (amount === 'Unlimited') return amount;
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN'
    }).format(Number(amount));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED': return 'success';
      case 'PENDING': return 'warning';
      case 'UNDER_REVIEW': return 'info';
      case 'REJECTED': return 'error';
      default: return 'secondary';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'APPROVED': return <FiCheck className="text-green-500" />;
      case 'PENDING': return <FiClock className="text-yellow-500" />;
      case 'UNDER_REVIEW': return <FiRefreshCw className="text-blue-500" />;
      case 'REJECTED': return <FiX className="text-red-500" />;
      default: return <FiClock className="text-gray-500" />;
    }
  };

  const getDocumentIcon = (type: string) => {
    const docType = documentTypes.find(d => d.value === type);
    return docType?.icon || '📄';
  };

  if (loading) {
    return (
      <DashboardLayout title="KYC Verification">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title="KYC Verification">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white">KYC Verification</h1>
            <p className="text-gray-400 mt-2">Complete your identity verification to unlock all features</p>
          </div>
          {kyc && (
            <div className="flex items-center space-x-2">
              {getStatusIcon(kyc.status)}
              <Badge variant={getStatusColor(kyc.status)}>
                {kyc.status.replace('_', ' ')}
              </Badge>
            </div>
          )}
        </div>

        {/* KYC Status Card */}
        {kyc && (
          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-white">Current Status</h3>
                <p className="text-gray-400">
                  {kyc.status === 'APPROVED' 
                    ? `Your ${kyc.level} verification is approved`
                    : kyc.status === 'REJECTED'
                    ? 'Your verification was rejected. Please review and resubmit.'
                    : 'Your verification is being processed'
                  }
                </p>
              </div>
              <div className="text-right">
                <Badge variant={getStatusColor(kyc.status)} className="mb-2">
                  {kyc.level} Level
                </Badge>
                <p className="text-sm text-gray-400">
                  {kyc.submittedAt && `Submitted: ${new Date(kyc.submittedAt).toLocaleDateString()}`}
                </p>
              </div>
            </div>

            {kyc.rejectionReason && (
              <div className="mt-4 p-4 bg-red-600/20 border border-red-600 rounded-lg">
                <div className="flex items-start space-x-2">
                  <FiAlertTriangle className="text-red-400 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-red-400">Rejection Reason</h4>
                    <p className="text-sm text-gray-300 mt-1">{kyc.rejectionReason}</p>
                  </div>
                </div>
              </div>
            )}
          </Card>
        )}

        {/* KYC Levels */}
        <div>
          <h3 className="text-lg font-semibold text-white mb-4">Choose Verification Level</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {kycLevels.map((level) => (
              <motion.div
                key={level.level}
                whileHover={{ scale: 1.02 }}
                className="cursor-pointer"
                onClick={() => setSelectedLevel(level.level)}
              >
                <Card className={`p-6 border-2 transition-colors ${
                  selectedLevel === level.level 
                    ? 'border-green-500 bg-green-600/10' 
                    : 'border-gray-700 bg-gray-800 hover:border-gray-600'
                }`}>
                  <div className={`w-12 h-12 ${level.color} rounded-lg flex items-center justify-center mb-4`}>
                    <FiShield className="text-white text-xl" />
                  </div>
                  
                  <h4 className="font-semibold text-white mb-2">{level.name}</h4>
                  <p className="text-gray-400 text-sm mb-4">{level.description}</p>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Daily Deposit:</span>
                      <span className="text-white">{formatCurrency(level.limits.deposit)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Daily Withdrawal:</span>
                      <span className="text-white">{formatCurrency(level.limits.withdrawal)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Monthly Limit:</span>
                      <span className="text-white">{formatCurrency(level.limits.monthly)}</span>
                    </div>
                  </div>

                  <div className="mt-4 pt-4 border-t border-gray-700">
                    <p className="text-xs text-gray-400 mb-2">Required Documents:</p>
                    <div className="flex flex-wrap gap-1">
                      {level.requirements.map((req) => (
                        <Badge key={req} variant="secondary" className="text-xs">
                          {getDocumentIcon(req)} {req.replace('_', ' ')}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Progress Steps */}
        <Card className="bg-gray-800 border-gray-700 p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-white">Verification Progress</h3>
            <span className="text-sm text-gray-400">Step {currentStep} of 4</span>
          </div>

          <div className="flex items-center space-x-4 mb-6">
            {[1, 2, 3, 4].map((step) => (
              <div key={step} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  step <= currentStep ? 'bg-green-600 text-white' : 'bg-gray-700 text-gray-400'
                }`}>
                  {step < currentStep ? <FiCheck /> : step}
                </div>
                {step < 4 && (
                  <div className={`w-16 h-1 ${
                    step < currentStep ? 'bg-green-600' : 'bg-gray-700'
                  }`}></div>
                )}
              </div>
            ))}
          </div>

          <div className="grid grid-cols-4 gap-4 text-center text-sm">
            <div>
              <p className="text-gray-400">Personal Info</p>
            </div>
            <div>
              <p className="text-gray-400">Address Info</p>
            </div>
            <div>
              <p className="text-gray-400">Documents</p>
            </div>
            <div>
              <p className="text-gray-400">Review & Submit</p>
            </div>
          </div>
        </Card>

        {/* Step Forms */}
        {currentStep === 1 && (
          <Card className="bg-gray-800 border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <FiUser className="mr-2" />
              Personal Information
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="First Name"
                value={personalInfo.firstName}
                onChange={(e) => setPersonalInfo({ ...personalInfo, firstName: e.target.value })}
                required
              />
              <Input
                label="Last Name"
                value={personalInfo.lastName}
                onChange={(e) => setPersonalInfo({ ...personalInfo, lastName: e.target.value })}
                required
              />
              <Input
                label="Middle Name"
                value={personalInfo.middleName}
                onChange={(e) => setPersonalInfo({ ...personalInfo, middleName: e.target.value })}
              />
              <Input
                label="Date of Birth"
                type="date"
                value={personalInfo.dateOfBirth}
                onChange={(e) => setPersonalInfo({ ...personalInfo, dateOfBirth: e.target.value })}
                required
              />
              <Select
                label="Gender"
                value={personalInfo.gender}
                onChange={(value) => setPersonalInfo({ ...personalInfo, gender: value })}
                options={[
                  { value: '', label: 'Select Gender' },
                  { value: 'MALE', label: 'Male' },
                  { value: 'FEMALE', label: 'Female' },
                  { value: 'OTHER', label: 'Other' }
                ]}
                required
              />
              <Input
                label="Nationality"
                value={personalInfo.nationality}
                onChange={(e) => setPersonalInfo({ ...personalInfo, nationality: e.target.value })}
                required
              />
              <Input
                label="State of Origin"
                value={personalInfo.stateOfOrigin}
                onChange={(e) => setPersonalInfo({ ...personalInfo, stateOfOrigin: e.target.value })}
              />
              <Input
                label="Local Government"
                value={personalInfo.localGovernment}
                onChange={(e) => setPersonalInfo({ ...personalInfo, localGovernment: e.target.value })}
              />
              <Input
                label="Occupation"
                value={personalInfo.occupation}
                onChange={(e) => setPersonalInfo({ ...personalInfo, occupation: e.target.value })}
              />
              <Input
                label="Employer Name"
                value={personalInfo.employerName}
                onChange={(e) => setPersonalInfo({ ...personalInfo, employerName: e.target.value })}
              />
              <Select
                label="Annual Income"
                value={personalInfo.annualIncome}
                onChange={(value) => setPersonalInfo({ ...personalInfo, annualIncome: value })}
                options={[
                  { value: '', label: 'Select Income Range' },
                  { value: 'BELOW_500K', label: 'Below ₦500,000' },
                  { value: '500K_1M', label: '₦500,000 - ₦1,000,000' },
                  { value: '1M_5M', label: '₦1,000,000 - ₦5,000,000' },
                  { value: '5M_10M', label: '₦5,000,000 - ₦10,000,000' },
                  { value: 'ABOVE_10M', label: 'Above ₦10,000,000' }
                ]}
              />
            </div>

            <div className="flex justify-end mt-6">
              <Button
                onClick={handleSavePersonalInfo}
                className="bg-green-600 hover:bg-green-700"
              >
                Continue to Address
              </Button>
            </div>
          </Card>
        )}

        {currentStep === 2 && (
          <Card className="bg-gray-800 border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <FiHome className="mr-2" />
              Address Information
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <Input
                  label="Street Address"
                  value={addressInfo.street}
                  onChange={(e) => setAddressInfo({ ...addressInfo, street: e.target.value })}
                  required
                />
              </div>
              <Input
                label="City"
                value={addressInfo.city}
                onChange={(e) => setAddressInfo({ ...addressInfo, city: e.target.value })}
                required
              />
              <Input
                label="State"
                value={addressInfo.state}
                onChange={(e) => setAddressInfo({ ...addressInfo, state: e.target.value })}
                required
              />
              <Input
                label="Postal Code"
                value={addressInfo.postalCode}
                onChange={(e) => setAddressInfo({ ...addressInfo, postalCode: e.target.value })}
              />
              <Input
                label="Country"
                value={addressInfo.country}
                onChange={(e) => setAddressInfo({ ...addressInfo, country: e.target.value })}
                required
              />
              <div className="md:col-span-2">
                <Input
                  label="Landmark (Optional)"
                  value={addressInfo.landmark}
                  onChange={(e) => setAddressInfo({ ...addressInfo, landmark: e.target.value })}
                  placeholder="Nearest landmark or notable location"
                />
              </div>
            </div>

            <div className="flex justify-between mt-6">
              <Button
                variant="outline"
                onClick={() => setCurrentStep(1)}
              >
                Back to Personal Info
              </Button>
              <Button
                onClick={handleSaveAddressInfo}
                className="bg-green-600 hover:bg-green-700"
              >
                Continue to Documents
              </Button>
            </div>
          </Card>
        )}

        {currentStep === 3 && (
          <Card className="bg-gray-800 border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <FiFile className="mr-2" />
              Document Upload
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
              {documentTypes.map((docType) => {
                const isRequired = kycLevels.find(l => l.level === selectedLevel)?.requirements.includes(docType.value);
                const isUploaded = documents.some(d => d.type === docType.value);

                return (
                  <div
                    key={docType.value}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      isUploaded
                        ? 'border-green-500 bg-green-600/10'
                        : isRequired
                        ? 'border-yellow-500 bg-yellow-600/10 hover:border-yellow-400'
                        : 'border-gray-600 bg-gray-700 hover:border-gray-500'
                    }`}
                    onClick={() => {
                      setSelectedDocumentType(docType.value);
                      setShowDocumentModal(true);
                    }}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-2xl">{docType.icon}</span>
                      {isUploaded && <FiCheck className="text-green-500" />}
                      {isRequired && !isUploaded && <FiAlertTriangle className="text-yellow-500" />}
                    </div>
                    <h4 className="font-medium text-white text-sm">{docType.label}</h4>
                    <p className="text-xs text-gray-400 mt-1">
                      {isRequired ? 'Required' : 'Optional'} • {isUploaded ? 'Uploaded' : 'Not uploaded'}
                    </p>
                  </div>
                );
              })}
            </div>

            {/* Uploaded Documents */}
            {documents.length > 0 && (
              <div className="mb-6">
                <h4 className="font-medium text-white mb-3">Uploaded Documents</h4>
                <div className="space-y-2">
                  {documents.map((doc) => (
                    <div key={doc.id} className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <span className="text-lg">{getDocumentIcon(doc.type)}</span>
                        <div>
                          <p className="text-white font-medium">{doc.name}</p>
                          <p className="text-sm text-gray-400">{doc.type.replace('_', ' ')}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={getStatusColor(doc.status)}>
                          {doc.status}
                        </Badge>
                        <Button size="sm" variant="outline">
                          <FiEye />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="flex justify-between">
              <Button
                variant="outline"
                onClick={() => setCurrentStep(2)}
              >
                Back to Address
              </Button>
              <Button
                onClick={() => setCurrentStep(4)}
                className="bg-green-600 hover:bg-green-700"
                disabled={documents.length === 0}
              >
                Review & Submit
              </Button>
            </div>
          </Card>
        )}

        {currentStep === 4 && (
          <Card className="bg-gray-800 border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <FiCheck className="mr-2" />
              Review & Submit
            </h3>

            <div className="space-y-6">
              {/* Summary */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-white mb-3">Personal Information</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Name:</span>
                      <span className="text-white">{personalInfo.firstName} {personalInfo.lastName}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Date of Birth:</span>
                      <span className="text-white">{personalInfo.dateOfBirth}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Gender:</span>
                      <span className="text-white">{personalInfo.gender}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Nationality:</span>
                      <span className="text-white">{personalInfo.nationality}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-white mb-3">Address Information</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Street:</span>
                      <span className="text-white">{addressInfo.street}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">City:</span>
                      <span className="text-white">{addressInfo.city}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">State:</span>
                      <span className="text-white">{addressInfo.state}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Country:</span>
                      <span className="text-white">{addressInfo.country}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium text-white mb-3">Documents ({documents.length})</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                  {documents.map((doc) => (
                    <div key={doc.id} className="flex items-center space-x-2 p-2 bg-gray-700 rounded">
                      <span>{getDocumentIcon(doc.type)}</span>
                      <span className="text-sm text-white">{doc.type.replace('_', ' ')}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="p-4 bg-blue-600/20 border border-blue-600 rounded-lg">
                <div className="flex items-start space-x-2">
                  <FiInfo className="text-blue-400 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-blue-400">Important Notice</h4>
                    <p className="text-sm text-gray-300 mt-1">
                      By submitting this KYC application, you confirm that all information provided is accurate and complete.
                      False information may result in account suspension.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-between mt-6">
              <Button
                variant="outline"
                onClick={() => setCurrentStep(3)}
              >
                Back to Documents
              </Button>
              <Button
                onClick={handleSubmitKYC}
                className="bg-green-600 hover:bg-green-700"
              >
                <FiShield className="mr-2" />
                Submit for Review
              </Button>
            </div>
          </Card>
        )}

        {/* Document Upload Modal */}
        <Modal
          isOpen={showDocumentModal}
          onClose={() => setShowDocumentModal(false)}
          title="Upload Document"
        >
          <div className="space-y-4">
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">
                  {documentTypes.find(d => d.value === selectedDocumentType)?.icon}
                </span>
              </div>
              <h3 className="text-lg font-semibold text-white">
                {documentTypes.find(d => d.value === selectedDocumentType)?.label}
              </h3>
            </div>

            <div className="border-2 border-dashed border-gray-600 rounded-lg p-8 text-center">
              <FiUpload className="mx-auto text-4xl text-gray-400 mb-4" />
              <p className="text-gray-400 mb-4">
                Drag and drop your file here, or click to browse
              </p>
              <input
                type="file"
                accept="image/*,.pdf"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    handleDocumentUpload(file);
                  }
                }}
                className="hidden"
                id="document-upload"
              />
              <label
                htmlFor="document-upload"
                className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg cursor-pointer hover:bg-green-700"
              >
                <FiCamera className="mr-2" />
                Choose File
              </label>
            </div>

            <div className="text-sm text-gray-400">
              <p>• Supported formats: JPG, PNG, PDF</p>
              <p>• Maximum file size: 5MB</p>
              <p>• Ensure document is clear and readable</p>
            </div>

            {uploadingDocument && (
              <div className="flex items-center justify-center py-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500 mr-3"></div>
                <span className="text-white">Uploading document...</span>
              </div>
            )}
          </div>
        </Modal>
      </div>
    </DashboardLayout>
  );
}
