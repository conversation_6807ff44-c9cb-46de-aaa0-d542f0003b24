"use client";

import React, { useRef } from 'react';
import { motion } from 'framer-motion';
import { IconType } from 'react-icons';
import { useTheme, getThemeClasses } from '../../contexts/ThemeContext';

interface DashboardCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: IconType;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  color?: 'green' | 'blue' | 'red' | 'yellow' | 'purple';
  onClick?: () => void;
  className?: string;
}

const colorClasses = {
  green: {
    bg: 'bg-green-500/20',
    text: 'text-green-400',
    border: 'border-green-500/30',
    icon: 'text-green-400',
  },
  blue: {
    bg: 'bg-blue-500/20',
    text: 'text-blue-400',
    border: 'border-blue-500/30',
    icon: 'text-blue-400',
  },
  red: {
    bg: 'bg-red-500/20',
    text: 'text-red-400',
    border: 'border-red-500/30',
    icon: 'text-red-400',
  },
  yellow: {
    bg: 'bg-yellow-500/20',
    text: 'text-yellow-400',
    border: 'border-yellow-500/30',
    icon: 'text-yellow-400',
  },
  purple: {
    bg: 'bg-purple-500/20',
    text: 'text-purple-400',
    border: 'border-purple-500/30',
    icon: 'text-purple-400',
  },
};

export default function DashboardCard({
  title,
  value,
  subtitle,
  icon: Icon,
  trend,
  color = 'green',
  onClick,
  className = '',
}: DashboardCardProps) {
  const colors = colorClasses[color] || colorClasses.green; // Fallback to green if color not found
  const { theme } = useTheme();
  const themeClasses = getThemeClasses(theme);
  const cardRef = useRef<HTMLDivElement>(null);

  // Material Design elevation levels
  const getElevation = (level: number) => {
    const elevations = {
      1: theme === 'light'
        ? '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)'
        : '0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.5)',
      2: theme === 'light'
        ? '0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23)'
        : '0 3px 6px rgba(0, 0, 0, 0.4), 0 3px 6px rgba(0, 0, 0, 0.6)',
      3: theme === 'light'
        ? '0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23)'
        : '0 10px 20px rgba(0, 0, 0, 0.5), 0 6px 6px rgba(0, 0, 0, 0.7)',
    };
    return elevations[level as keyof typeof elevations] || elevations[1];
  };

  return (
    <motion.div
      ref={cardRef}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{
        y: -4,
        transition: { duration: 0.2, ease: "easeOut" }
      }}
      className={`
        relative overflow-hidden rounded-xl p-6 transition-all duration-300 group
        ${onClick ? 'cursor-pointer' : ''}
        ${theme === 'light'
          ? 'bg-white border border-gray-200 hover:border-gray-300'
          : 'bg-gray-900/80 border border-gray-700 hover:border-gray-600'
        }
        ${className}
      `}
      style={{
        boxShadow: getElevation(1),
      }}
      whileHover={{
        boxShadow: getElevation(3),
      }}
      onClick={onClick}
    >
      {/* Material Design ripple effect */}
      {onClick && (
        <motion.div
          className="absolute inset-0 bg-green-400 opacity-0 group-hover:opacity-5 transition-opacity duration-300"
          initial={{ scale: 0, opacity: 0 }}
          whileHover={{ scale: 1, opacity: 0.05 }}
          transition={{ duration: 0.3 }}
        />
      )}

      {/* Background gradient accent */}
      <div className={`absolute top-0 left-0 w-full h-1 bg-gradient-to-r ${colors?.bg?.replace('bg-', 'from-') || 'from-brand'} to-transparent opacity-60`} />

      <div className="flex items-start justify-between relative z-10">
        <div className="flex-1">
          <p className={`text-sm font-medium mb-3 ${themeClasses.text.secondary}`}>
            {title}
          </p>
          <p className={`text-3xl font-bold mb-2 ${themeClasses.text.primary}`} style={{ fontFamily: 'Inter, system-ui, sans-serif' }}>
            {value}
          </p>
          {subtitle && (
            <p className={`text-sm ${themeClasses.text.tertiary} mb-2`}>
              {subtitle}
            </p>
          )}
          {trend && (
            <motion.div
              className="flex items-center mt-3"
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
            >
              <span
                className={`text-sm font-semibold px-2 py-1 rounded-full ${
                  trend.isPositive
                    ? 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/30'
                    : 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/30'
                }`}
              >
                {trend.isPositive ? '↗' : '↘'} {trend.isPositive ? '+' : ''}{trend.value}%
              </span>
              <span className={`text-xs ml-2 ${themeClasses.text.tertiary}`}>vs last month</span>
            </motion.div>
          )}
        </div>
        {Icon && (
          <motion.div
            className={`p-4 rounded-xl ${colors?.bg || 'bg-brand/20'} ${colors?.border || 'border-brand'} border-2 shadow-lg`}
            whileHover={{ scale: 1.05, rotate: 5 }}
            transition={{ duration: 0.2 }}
            style={{
              boxShadow: `0 4px 12px ${colors?.bg?.includes('green') ? 'rgba(34, 197, 94, 0.3)' :
                         colors?.bg?.includes('blue') ? 'rgba(59, 130, 246, 0.3)' :
                         colors?.bg?.includes('purple') ? 'rgba(147, 51, 234, 0.3)' :
                         'rgba(34, 197, 94, 0.3)'}`
            }}
          >
            <Icon className={`w-7 h-7 ${colors?.icon || 'text-brand'}`} />
          </motion.div>
        )}
      </div>
    </motion.div>
  );
}

// Specialized card components
export function StatCard({
  title,
  value,
  subtitle,
  icon,
  color = 'green',
  trend,
}: Omit<DashboardCardProps, 'onClick'>) {
  return (
    <DashboardCard
      title={title}
      value={value}
      subtitle={subtitle}
      icon={icon}
      color={color}
      trend={trend}
    />
  );
}

export function ActionCard({
  title,
  subtitle,
  icon: Icon,
  color = 'blue',
  onClick,
}: {
  title: string;
  subtitle: string;
  icon: IconType;
  color?: 'green' | 'blue' | 'red' | 'yellow' | 'purple';
  onClick: () => void;
}) {
  const colors = colorClasses[color] || colorClasses.blue; // Fallback to blue for ActionCard
  const { theme } = useTheme();
  const themeClasses = getThemeClasses(theme);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{
        y: -6,
        scale: 1.02,
        transition: { duration: 0.2, ease: "easeOut" }
      }}
      whileTap={{ scale: 0.98 }}
      className={`
        relative overflow-hidden rounded-xl p-6 cursor-pointer transition-all duration-300 group
        ${theme === 'light'
          ? 'bg-gradient-to-br from-white to-gray-50 border border-gray-200 hover:border-gray-300'
          : 'bg-gradient-to-br from-gray-800 to-gray-900 border border-gray-700 hover:border-gray-600'
        }
      `}
      style={{
        boxShadow: theme === 'light'
          ? '0 2px 8px rgba(0, 0, 0, 0.1), 0 4px 16px rgba(0, 0, 0, 0.05)'
          : '0 2px 8px rgba(0, 0, 0, 0.3), 0 4px 16px rgba(0, 0, 0, 0.2)',
      }}
      whileHover={{
        boxShadow: theme === 'light'
          ? '0 8px 25px rgba(0, 0, 0, 0.15), 0 16px 40px rgba(0, 0, 0, 0.1)'
          : '0 8px 25px rgba(0, 0, 0, 0.4), 0 16px 40px rgba(0, 0, 0, 0.3)',
      }}
      onClick={onClick}
    >
      {/* Material Design ripple effect */}
      <motion.div
        className={`absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-300 ${colors?.bg || 'bg-brand/10'}`}
        initial={{ scale: 0 }}
        whileHover={{ scale: 1 }}
        transition={{ duration: 0.3 }}
      />

      {/* Accent line */}
      <div className={`absolute top-0 left-0 w-full h-1 bg-gradient-to-r ${colors?.bg?.replace('bg-', 'from-') || 'from-brand'} to-transparent`} />

      <div className="flex items-center space-x-4 relative z-10">
        <motion.div
          className={`p-4 rounded-xl ${colors?.bg || 'bg-brand/20'} shadow-lg`}
          whileHover={{ scale: 1.1, rotate: 10 }}
          transition={{ duration: 0.2 }}
          style={{
            boxShadow: `0 4px 12px ${colors?.bg?.includes('green') ? 'rgba(34, 197, 94, 0.3)' :
                       colors?.bg?.includes('blue') ? 'rgba(59, 130, 246, 0.3)' :
                       colors?.bg?.includes('purple') ? 'rgba(147, 51, 234, 0.3)' :
                       'rgba(34, 197, 94, 0.3)'}`
          }}
        >
          <Icon className={`w-6 h-6 ${colors?.icon || 'text-brand'}`} />
        </motion.div>
        <div className="flex-1">
          <h3 className={`font-semibold text-lg ${themeClasses.text.primary} mb-1`}>
            {title}
          </h3>
          <p className={`text-sm ${themeClasses.text.secondary}`}>
            {subtitle}
          </p>
        </div>
        <motion.div
          className={`w-8 h-8 rounded-full flex items-center justify-center ${colors?.bg || 'bg-brand/20'} opacity-70`}
          whileHover={{ scale: 1.2, opacity: 1 }}
          transition={{ duration: 0.2 }}
        >
          <svg className={`w-4 h-4 ${colors?.icon || 'text-brand'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </motion.div>
      </div>
    </motion.div>
  );
}

export function ProgressCard({
  title,
  current,
  target,
  unit = '',
  color = 'green',
}: {
  title: string;
  current: number;
  target: number;
  unit?: string;
  color?: 'green' | 'blue' | 'red' | 'yellow' | 'purple';
}) {
  const percentage = Math.min((current / target) * 100, 100);
  const colors = colorClasses[color] || colorClasses.green;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gray-900/50 border border-gray-800 rounded-lg p-6"
    >
      <h3 className="text-white font-semibold mb-4">{title}</h3>
      
      <div className="space-y-3">
        <div className="flex justify-between text-sm">
          <span className="text-gray-400">Progress</span>
          <span className={colors?.text || 'text-brand'}>{percentage.toFixed(1)}%</span>
        </div>
        
        <div className="w-full bg-gray-800 rounded-full h-2">
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: `${percentage}%` }}
            transition={{ duration: 1, ease: "easeOut" }}
            className={`h-2 rounded-full bg-gradient-to-r from-${color}-400 to-${color}-600`}
          />
        </div>
        
        <div className="flex justify-between text-sm">
          <span className="text-gray-400">
            {current.toLocaleString()}{unit} / {target.toLocaleString()}{unit}
          </span>
          <span className="text-gray-400">
            {(target - current).toLocaleString()}{unit} remaining
          </span>
        </div>
      </div>
    </motion.div>
  );
}
