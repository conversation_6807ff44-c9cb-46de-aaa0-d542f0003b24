export interface TargetSavings {
  id: string;
  userId: string;
  title: string;
  description: string;
  targetAmount: number;
  currentAmount: number;
  targetDate: string;
  category: 'EMERGENCY' | 'VACATION' | 'EDUCATION' | 'HOUSE' | 'CAR' | 'BUSINESS' | 'WEDDING' | 'HEALTH' | 'OTHER';
  priority: 'LOW' | 'MEDIUM' | 'HIGH';
  status: 'ACTIVE' | 'COMPLETED' | 'PAUSED' | 'CANCELLED';
  imageUrl?: string;
  autoContribution: boolean;
  contributionAmount?: number;
  contributionFrequency?: 'DAILY' | 'WEEKLY' | 'MONTHLY';
  nextContributionDate?: string;
  interestRate: number;
  totalContributions: number;
  interestEarned: number;
  milestones: TargetMilestone[];
  reminders: TargetReminder[];
  createdAt: string;
  updatedAt: string;
}

export interface TargetMilestone {
  id: string;
  targetId: string;
  title: string;
  description?: string;
  targetAmount: number;
  isCompleted: boolean;
  completedAt?: string;
  reward?: string;
  celebrationMessage?: string;
  order: number;
}

export interface TargetReminder {
  id: string;
  targetId: string;
  type: 'CONTRIBUTION' | 'MILESTONE' | 'DEADLINE' | 'CUSTOM';
  title: string;
  message: string;
  reminderDate: string;
  isActive: boolean;
  isSent: boolean;
  sentAt?: string;
  frequency?: 'ONCE' | 'DAILY' | 'WEEKLY' | 'MONTHLY';
}

export interface CreateTargetSavingsData {
  title: string;
  description: string;
  targetAmount: number;
  targetDate: string;
  category: 'EMERGENCY' | 'VACATION' | 'EDUCATION' | 'HOUSE' | 'CAR' | 'BUSINESS' | 'WEDDING' | 'HEALTH' | 'OTHER';
  priority: 'LOW' | 'MEDIUM' | 'HIGH';
  imageUrl?: string;
  autoContribution: boolean;
  contributionAmount?: number;
  contributionFrequency?: 'DAILY' | 'WEEKLY' | 'MONTHLY';
  milestones?: Omit<TargetMilestone, 'id' | 'targetId' | 'isCompleted' | 'completedAt'>[];
  reminders?: Omit<TargetReminder, 'id' | 'targetId' | 'isSent' | 'sentAt'>[];
}

export interface UpdateTargetSavingsData {
  title?: string;
  description?: string;
  targetAmount?: number;
  targetDate?: string;
  category?: 'EMERGENCY' | 'VACATION' | 'EDUCATION' | 'HOUSE' | 'CAR' | 'BUSINESS' | 'WEDDING' | 'HEALTH' | 'OTHER';
  priority?: 'LOW' | 'MEDIUM' | 'HIGH';
  imageUrl?: string;
  autoContribution?: boolean;
  contributionAmount?: number;
  contributionFrequency?: 'DAILY' | 'WEEKLY' | 'MONTHLY';
  status?: 'ACTIVE' | 'COMPLETED' | 'PAUSED' | 'CANCELLED';
}

export interface TargetContributionData {
  targetId: string;
  amount: number;
  paymentMethod: string;
  description?: string;
}

export interface TargetSearchFilters {
  search?: string;
  category?: string;
  priority?: 'LOW' | 'MEDIUM' | 'HIGH';
  status?: 'ACTIVE' | 'COMPLETED' | 'PAUSED' | 'CANCELLED';
  minAmount?: number;
  maxAmount?: number;
  dateFrom?: string;
  dateTo?: string;
  sortBy?: 'createdAt' | 'targetDate' | 'targetAmount' | 'priority';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface TargetStats {
  totalTargets: number;
  activeTargets: number;
  completedTargets: number;
  totalSaved: number;
  averageProgress: number;
  completionRate: number;
  upcomingDeadlines: number;
}

export interface TargetProgress {
  targetId: string;
  progressPercentage: number;
  remainingAmount: number;
  daysRemaining: number;
  isOnTrack: boolean;
  recommendedMonthlyContribution: number;
  projectedCompletionDate: string;
}
