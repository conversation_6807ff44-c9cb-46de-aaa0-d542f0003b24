"use client";

import React, { useState } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';

interface Image3DProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
  fill?: boolean;
  sizes?: string;
  quality?: number;
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down';
  variant?: 'default' | 'hero' | 'card' | 'testimonial' | 'feature';
  intensity?: 'light' | 'medium' | 'strong';
}

const variants = {
  default: {
    container: 'relative inline-block',
    wrapper: 'relative overflow-hidden rounded-lg',
    transform: 'none',
    hoverTransform: 'scale(1.05)',
    shadow: 'rgba(0, 0, 0, 0.24) 0px 8px 20px'
  },
  hero: {
    container: 'relative inline-block',
    wrapper: 'relative overflow-hidden rounded-xl',
    transform: 'perspective(600px) rotateX(20deg) rotateZ(-8deg)',
    hoverTransform: 'perspective(600px) rotateX(8deg) rotateY(15deg) rotateZ(-3deg)',
    shadow: 'rgba(0, 0, 0, 0.3) -15px 25px 30px'
  },
  card: {
    container: 'relative inline-block',
    wrapper: 'relative overflow-hidden rounded-lg',
    transform: 'none',
    hoverTransform: 'scale(1.03)',
    shadow: 'rgba(0, 0, 0, 0.2) 0px 6px 15px'
  },
  testimonial: {
    container: 'relative inline-block',
    wrapper: 'relative overflow-hidden rounded-full',
    transform: 'none',
    hoverTransform: 'scale(1.1)',
    shadow: 'rgba(0, 0, 0, 0.15) 0px 4px 12px'
  },
  feature: {
    container: 'relative inline-block',
    wrapper: 'relative overflow-hidden rounded-lg',
    transform: 'none',
    hoverTransform: 'scale(1.02)',
    shadow: 'rgba(0, 0, 0, 0.25) 0px 8px 25px'
  }
};

const intensityMultipliers = {
  light: 0.5,
  medium: 1,
  strong: 1.5
};

export function Image3D({
  src,
  alt,
  width,
  height,
  className = '',
  priority = false,
  fill = false,
  sizes,
  quality = 75,
  objectFit = 'cover',
  variant = 'default',
  intensity = 'medium',
  ...props
}: Image3DProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const config = variants[variant];
  const multiplier = intensityMultipliers[intensity];

  const handleLoad = () => {
    setIsLoading(false);
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  if (hasError) {
    return (
      <div 
        className={`bg-gray-800 border border-gray-700 flex items-center justify-center ${config.wrapper} ${className}`}
        style={{ 
          width: fill ? '100%' : width, 
          height: fill ? '100%' : height,
          transform: config.transform,
          boxShadow: config.shadow,
          transformStyle: 'preserve-3d',
          transition: 'transform 0.6s ease-out'
        }}
      >
        <div className="text-center text-gray-400">
          <svg className="w-8 h-8 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
          </svg>
          <p className="text-xs">Image not found</p>
        </div>
      </div>
    );
  }

  return (
    <div className={config.container}>
      {/* Loading skeleton */}
      {isLoading && (
        <div 
          className={`absolute inset-0 bg-gray-800 animate-pulse ${config.wrapper}`}
          style={{ 
            width: fill ? '100%' : width, 
            height: fill ? '100%' : height,
            transform: config.transform,
            boxShadow: config.shadow,
            transformStyle: 'preserve-3d',
            zIndex: 10
          }}
        >
          <div className="flex items-center justify-center h-full">
            <div className="w-8 h-8 border-2 border-green-400 border-t-transparent rounded-full animate-spin"></div>
          </div>
        </div>
      )}

      {/* 3D Image Container */}
      <motion.div
        className={`${config.wrapper} ${className}`}
        style={{
          transformStyle: 'preserve-3d',
          transform: config.transform,
          boxShadow: config.shadow,
          transition: 'transform 0.6s ease-out, box-shadow 0.6s ease-out'
        }}
        animate={{
          transform: isHovered ? config.hoverTransform : config.transform,
          boxShadow: isHovered 
            ? config.shadow.replace(/rgba\(0, 0, 0, ([\d.]+)\)/, (match, opacity) => 
                `rgba(0, 0, 0, ${parseFloat(opacity) * 1.3})`)
            : config.shadow
        }}
        transition={{ 
          type: "spring", 
          stiffness: 300, 
          damping: 30 
        }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Gradient overlay for depth */}
        <div 
          className="absolute inset-0 z-10 pointer-events-none"
          style={{
            background: 'linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.05) 50%, rgba(0, 0, 0, 0.1) 100%)',
            opacity: isHovered ? 0.8 : 0.4,
            transition: 'opacity 0.3s ease'
          }}
        />

        {/* Main Image */}
        <Image
          src={src}
          alt={alt}
          width={fill ? undefined : width}
          height={fill ? undefined : height}
          fill={fill}
          priority={priority}
          quality={quality}
          sizes={sizes}
          className={`
            ${objectFit === 'cover' ? 'object-cover' : ''}
            ${objectFit === 'contain' ? 'object-contain' : ''}
            ${objectFit === 'fill' ? 'object-fill' : ''}
            ${objectFit === 'none' ? 'object-none' : ''}
            ${objectFit === 'scale-down' ? 'object-scale-down' : ''}
            transition-transform duration-300
          `}
          style={{
            opacity: isLoading ? 0 : 1,
            transition: 'opacity 0.3s ease'
          }}
          onLoad={handleLoad}
          onError={handleError}
          {...props}
        />

        {/* Shine effect on hover */}
        <motion.div
          className="absolute inset-0 pointer-events-none"
          style={{
            background: 'linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%)',
            transform: 'translateX(-100%)',
          }}
          animate={{
            transform: isHovered ? 'translateX(100%)' : 'translateX(-100%)'
          }}
          transition={{ duration: 0.6, ease: "easeInOut" }}
        />
      </motion.div>
    </div>
  );
}

// Specialized 3D image variants
export function HeroImage3D(props: Omit<Image3DProps, 'variant'>) {
  return <Image3D {...props} variant="hero" />;
}

export function CardImage3D(props: Omit<Image3DProps, 'variant'>) {
  return <Image3D {...props} variant="card" />;
}

export function TestimonialImage3D(props: Omit<Image3DProps, 'variant'>) {
  return <Image3D {...props} variant="testimonial" />;
}

export function FeatureImage3D(props: Omit<Image3DProps, 'variant'>) {
  return <Image3D {...props} variant="feature" />;
}

export default Image3D;
