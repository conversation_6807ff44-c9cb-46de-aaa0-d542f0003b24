"use client";

import React from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { FiArrowLeft, FiShield, FiCheck } from 'react-icons/fi';
import { BackgroundAnimation } from '../../src/components/ui/BackgroundAnimation';
import { BetterInterestLogo } from '../../src/components/ui/BetterInterestLogo';

export default function TermsPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-green-900 to-black text-white">
      <BackgroundAnimation variant="default" />
      
      {/* Header */}
      <header className="relative z-50 px-6 py-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <Link href="/" className="flex items-center">
            <BetterInterestLogo size="md" variant="light" />
          </Link>
          <Link
            href="/"
            className="inline-flex items-center text-gray-400 hover:text-white transition-colors"
          >
            <FiArrowLeft className="mr-2" />
            Back to Home
          </Link>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 max-w-4xl mx-auto px-6 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <div className="flex items-center justify-center mb-4">
            <FiShield className="text-green-400 text-4xl mr-3" />
            <h1 className="text-4xl font-bold">Terms & Conditions</h1>
          </div>
          <p className="text-gray-300 text-lg">
            Last updated: {new Date().toLocaleDateString()}
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-900/50 border border-gray-800 rounded-lg p-8 space-y-8"
        >
          <section>
            <h2 className="text-2xl font-semibold text-green-400 mb-4 flex items-center">
              <FiCheck className="mr-2" />
              1. Acceptance of Terms
            </h2>
            <p className="text-gray-300 leading-relaxed">
              By accessing and using BetterInterest services, you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.
            </p>
          </section>

          <section>
            <h2 className="text-2xl font-semibold text-green-400 mb-4 flex items-center">
              <FiCheck className="mr-2" />
              2. Savings Services
            </h2>
            <p className="text-gray-300 leading-relaxed mb-4">
              BetterInterest provides digital savings and investment services designed to help users achieve their financial goals. Our services include:
            </p>
            <ul className="list-disc list-inside text-gray-300 space-y-2 ml-4">
              <li>Individual savings plans with competitive interest rates</li>
              <li>Group savings and investment opportunities</li>
              <li>Target-based savings goals</li>
              <li>Automated savings features</li>
            </ul>
          </section>

          <section>
            <h2 className="text-2xl font-semibold text-green-400 mb-4 flex items-center">
              <FiCheck className="mr-2" />
              3. User Responsibilities
            </h2>
            <p className="text-gray-300 leading-relaxed mb-4">
              As a user of our platform, you agree to:
            </p>
            <ul className="list-disc list-inside text-gray-300 space-y-2 ml-4">
              <li>Provide accurate and complete information during registration</li>
              <li>Maintain the security of your account credentials</li>
              <li>Comply with all applicable laws and regulations</li>
              <li>Use the service only for lawful purposes</li>
            </ul>
          </section>

          <section>
            <h2 className="text-2xl font-semibold text-green-400 mb-4 flex items-center">
              <FiCheck className="mr-2" />
              4. Financial Regulations
            </h2>
            <p className="text-gray-300 leading-relaxed">
              BetterInterest operates in compliance with applicable financial regulations. All savings and investment activities are subject to regulatory oversight and may be subject to taxes as per local laws.
            </p>
          </section>

          <section>
            <h2 className="text-2xl font-semibold text-green-400 mb-4 flex items-center">
              <FiCheck className="mr-2" />
              5. Privacy and Data Protection
            </h2>
            <p className="text-gray-300 leading-relaxed">
              Your privacy is important to us. Please review our Privacy Policy to understand how we collect, use, and protect your personal information.
            </p>
          </section>

          <section>
            <h2 className="text-2xl font-semibold text-green-400 mb-4 flex items-center">
              <FiCheck className="mr-2" />
              6. Limitation of Liability
            </h2>
            <p className="text-gray-300 leading-relaxed">
              BetterInterest shall not be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses.
            </p>
          </section>

          <section>
            <h2 className="text-2xl font-semibold text-green-400 mb-4 flex items-center">
              <FiCheck className="mr-2" />
              7. Contact Information
            </h2>
            <p className="text-gray-300 leading-relaxed">
              If you have any questions about these Terms & Conditions, please contact us at:
            </p>
            <div className="mt-4 p-4 bg-gray-800/50 rounded-lg">
              <p className="text-green-400 font-semibold">BetterInterest Support</p>
              <p className="text-gray-300">Email: <EMAIL></p>
              <p className="text-gray-300">Phone: +234 (0) ************</p>
            </div>
          </section>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="text-center mt-12"
        >
          <Link
            href="/"
            className="inline-flex items-center bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg transition-colors"
          >
            <FiArrowLeft className="mr-2" />
            Return to Home
          </Link>
        </motion.div>
      </main>
    </div>
  );
}
