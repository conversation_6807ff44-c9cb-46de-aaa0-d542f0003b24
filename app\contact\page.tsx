"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import AOS from "aos";
import "aos/dist/aos.css";
import Link from "next/link";
import {
  FiSave,
  FiArrowLeft,
  FiMail,
  FiPhone,
  FiMapPin,
  FiSend,
  FiMessageCircle,
  FiClock,
  FiUsers,
  FiUser,
} from "react-icons/fi";
import PageLayout from "../../components/PageLayout";
import EnhancedHero from "../../components/EnhancedHero";

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: "",
  });

  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
      easing: "ease-out-cubic",
    });
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle contact form submission
    console.log("Contact form submitted:", formData);
  };

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const contactInfo = [
    {
      icon: <FiMail className="w-6 h-6" data-oid="g2.-a9l" />,
      title: "Email Us",
      description: "Get in touch via email",
      contact: "<EMAIL>",
      action: "mailto:<EMAIL>",
    },
    {
      icon: <FiPhone className="w-6 h-6" data-oid="asn5hbz" />,
      title: "Call Us",
      description: "Speak with our team",
      contact: "+****************",
      action: "tel:+15551234567",
    },
    {
      icon: <FiMapPin className="w-6 h-6" data-oid="0da__e5" />,
      title: "Visit Us",
      description: "Our headquarters",
      contact: "123 Finance St, San Francisco, CA 94105",
      action: "#",
    },
  ];

  const supportOptions = [
    {
      icon: <FiMessageCircle className="w-8 h-8" data-oid="0.arxhe" />,
      title: "Live Chat",
      description: "Chat with our support team in real-time",
      availability: "Available 24/7",
    },
    {
      icon: <FiClock className="w-8 h-8" data-oid="xrtxt4q" />,
      title: "Response Time",
      description: "We typically respond within 2 hours",
      availability: "During business hours",
    },
    {
      icon: <FiUsers className="w-8 h-8" data-oid="fbslxr0" />,
      title: "Expert Support",
      description: "Our team of financial experts is here to help",
      availability: "Mon-Fri, 9AM-6PM PST",
    },
  ];

  return (
    <PageLayout>
      {/* Enhanced Hero Section */}
      <EnhancedHero
        icon={<FiMessageCircle className="w-12 h-12 text-white" />}
        title="Get in"
        highlightText="Touch"
        description="Have questions about your savings journey? We're here to help you every step of the way. Our dedicated support team is ready to assist you with any inquiries."
        primaryButton={{
          text: "Start Chat",
          href: "#contact-form"
        }}
        secondaryButton={{
          text: "Call Us",
          href: "tel:+234-800-KOJA-SAVE"
        }}
        features={[
          { icon: <FiMail />, label: "Email Support" },
          { icon: <FiPhone />, label: "Phone Support" },
          { icon: <FiClock />, label: "24/7 Available" },
          { icon: <FiUsers />, label: "Expert Team" }
        ]}
        stats={[
          { value: "<1hr", label: "Response Time" },
          { value: "24/7", label: "Support" },
          { value: "99%", label: "Satisfaction" },
          { value: "5★", label: "Rating" }
        ]}
      />

      {/* Contact Info Cards */}
      <section className="relative z-10 px-6 py-12" data-oid="j8rynpl">
        <div className="max-w-7xl mx-auto" data-oid="_chcatc">
          <div className="grid md:grid-cols-3 gap-8 mb-16" data-oid="d:5-v4d">
            {contactInfo.map((info, index) => (
              <motion.a
                key={index}
                href={info.action}
                data-aos="fade-up"
                data-aos-delay={index * 100}
                className="app-card hover-lift-advanced p-8 text-center card-stack"
                data-oid="emeh0dj"
              >
                <div
                  className="text-green-400 mb-4 flex justify-center group-hover:animate-pulse-green transition-all"
                  data-oid="weplxyt"
                >
                  {info.icon}
                </div>
                <h3
                  className="text-xl font-semibold mb-2 group-hover:text-green-400 transition-colors"
                  data-oid="sxwpa5d"
                >
                  {info.title}
                </h3>
                <p className="text-gray-400 mb-3" data-oid="nmo.bdt">
                  {info.description}
                </p>
                <p className="text-green-400 font-medium" data-oid="t4v864f">
                  {info.contact}
                </p>
              </motion.a>
            ))}
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="relative z-10 px-6 py-12" data-oid="exkveb4">
        <div className="max-w-7xl mx-auto" data-oid="5pddllo">
          <div className="grid lg:grid-cols-2 gap-12" data-oid="zgtn0lf">
            {/* Contact Form */}
            <div
              className="app-card hover-lift-advanced p-8"
              data-aos="fade-right"
              data-oid="oy---.s"
            >
              <h2
                className="text-3xl font-bold mb-6 text-gradient-animate"
                data-oid="0vgop07"
              >
                Send us a Message
              </h2>

              <form
                onSubmit={handleSubmit}
                className="space-y-6"
                data-oid="ga028ja"
              >
                <div data-oid="g9mvhhr">
                  <label
                    htmlFor="name"
                    className="block text-sm font-medium text-gray-300 mb-2"
                    data-oid="-fv4e2j"
                  >
                    Full Name
                  </label>
                  <div className="relative" data-oid="ome-nan">
                    <FiUser
                      className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"
                      data-oid="6k:qx1k"
                    />
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className="w-full pl-10 pr-4 py-3 bg-black/30 border border-green-400/30 rounded-lg focus:border-green-400 focus:outline-none focus:ring-2 focus:ring-green-400/20 transition-all text-white placeholder-gray-400"
                      placeholder="Enter your full name"
                      required
                      data-oid="o6vxmj0"
                    />
                  </div>
                </div>

                <div data-oid="vwczjzk">
                  <label
                    htmlFor="email"
                    className="block text-sm font-medium text-gray-300 mb-2"
                    data-oid="vy3rzbd"
                  >
                    Email Address
                  </label>
                  <div className="relative" data-oid="aelk4km">
                    <FiMail
                      className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"
                      data-oid="o6va0ab"
                    />
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="w-full pl-10 pr-4 py-3 bg-black/30 border border-green-400/30 rounded-lg focus:border-green-400 focus:outline-none focus:ring-2 focus:ring-green-400/20 transition-all text-white placeholder-gray-400"
                      placeholder="Enter your email address"
                      required
                      data-oid="wilyndu"
                    />
                  </div>
                </div>

                <div data-oid="wbnmqp.">
                  <label
                    htmlFor="subject"
                    className="block text-sm font-medium text-gray-300 mb-2"
                    data-oid="x78ij0w"
                  >
                    Subject
                  </label>
                  <select
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 bg-black/30 border border-green-400/30 rounded-lg focus:border-green-400 focus:outline-none focus:ring-2 focus:ring-green-400/20 transition-all text-white"
                    required
                    data-oid="mmegxc6"
                  >
                    <option value="" data-oid="aw2v9on">
                      Select a subject
                    </option>
                    <option value="general" data-oid="f8wc9u9">
                      General Inquiry
                    </option>
                    <option value="support" data-oid="fi0e:qy">
                      Technical Support
                    </option>
                    <option value="billing" data-oid="m2atgx7">
                      Billing Question
                    </option>
                    <option value="feature" data-oid="-h:_653">
                      Feature Request
                    </option>
                    <option value="partnership" data-oid=".5j73th">
                      Partnership
                    </option>
                    <option value="other" data-oid="1ko3wmr">
                      Other
                    </option>
                  </select>
                </div>

                <div data-oid="bli9.q5">
                  <label
                    htmlFor="message"
                    className="block text-sm font-medium text-gray-300 mb-2"
                    data-oid="6.lixvz"
                  >
                    Message
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    rows={6}
                    className="w-full px-4 py-3 bg-black/30 border border-green-400/30 rounded-lg focus:border-green-400 focus:outline-none focus:ring-2 focus:ring-green-400/20 transition-all text-white placeholder-gray-400 resize-none"
                    placeholder="Tell us how we can help you..."
                    required
                    data-oid="f52xvrr"
                  />
                </div>

                <motion.button
                  type="submit"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="w-full py-3 bg-gradient-to-r from-green-400 to-green-600 rounded-lg font-semibold hover:from-green-500 hover:to-green-700 transition-all btn-glow btn-hover-effect focus:outline-none focus:ring-2 focus:ring-green-400/20 flex items-center justify-center space-x-2"
                  data-oid="gguk9t9"
                >
                  <FiSend className="w-5 h-5" data-oid="qmc1dk5" />
                  <span data-oid="35r4vdo">Send Message</span>
                </motion.button>
              </form>
            </div>

            {/* Support Information */}
            <div className="space-y-8" data-aos="fade-left" data-oid="mpzj4np">
              <div data-oid="c0obeso">
                <h2
                  className="text-3xl font-bold mb-6 text-gradient-animate"
                  data-oid="jjfrrc-"
                >
                  How We Can Help
                </h2>
                <div className="space-y-6" data-oid="8y.:w-3">
                  {supportOptions.map((option, index) => (
                    <div
                      key={index}
                      className="app-card hover-lift-advanced p-6"
                      data-aos="fade-up"
                      data-aos-delay={index * 100}
                      data-oid="vn_6did"
                    >
                      <div
                        className="flex items-start space-x-4"
                        data-oid="znaf030"
                      >
                        <div className="text-green-400 mt-1" data-oid="o7yl:fz">
                          {option.icon}
                        </div>
                        <div data-oid="ss_xkl3">
                          <h3
                            className="text-xl font-semibold mb-2"
                            data-oid="z4ec2tt"
                          >
                            {option.title}
                          </h3>
                          <p className="text-gray-300 mb-2" data-oid="14xrwq2">
                            {option.description}
                          </p>
                          <p
                            className="text-green-400 text-sm font-medium"
                            data-oid="zbsoon3"
                          >
                            {option.availability}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* FAQ Section */}
              <div
                className="app-card hover-lift-advanced p-6"
                data-aos="fade-up"
                data-aos-delay="400"
                data-oid="g3mn2r:"
              >
                <h3
                  className="text-xl font-semibold mb-4 text-gradient-animate"
                  data-oid=".hh0n:w"
                >
                  Frequently Asked Questions
                </h3>
                <div className="space-y-4" data-oid="be1u9yf">
                  <div data-oid="tgtbc28">
                    <h4
                      className="font-medium text-green-400 mb-1"
                      data-oid="tse9xr4"
                    >
                      How secure is my money?
                    </h4>
                    <p className="text-gray-300 text-sm" data-oid="mrgww4w">
                      Your funds are FDIC insured up to $250,000 and protected
                      with bank-level security.
                    </p>
                  </div>
                  <div data-oid="t4dv1qx">
                    <h4
                      className="font-medium text-green-400 mb-1"
                      data-oid="vt58:19"
                    >
                      How do I withdraw my savings?
                    </h4>
                    <p className="text-gray-300 text-sm" data-oid="-5qdwt0">
                      You can withdraw your funds anytime through the app with
                      no penalties or fees.
                    </p>
                  </div>
                  <div data-oid="h2n3wvh">
                    <h4
                      className="font-medium text-green-400 mb-1"
                      data-oid="vufxi18"
                    >
                      What's the minimum deposit?
                    </h4>
                    <p className="text-gray-300 text-sm" data-oid="4a_sc2-">
                      You can start saving with as little as $1. No minimum
                      balance required.
                    </p>
                  </div>
                </div>
                <Link
                  href="#"
                  className="inline-block mt-4 text-green-400 hover:text-green-300 transition-colors text-sm font-medium"
                  data-oid="64zqq.:"
                >
                  View all FAQs →
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

    </PageLayout>
  );
}
