"use client";

import { motion } from "framer-motion";
import {
  FiSave,
  FiTrendingUp,
  FiShield,
  FiTarget,
  FiPieChart,
  FiSmartphone,
  FiBell,
  FiCreditCard,
  FiUsers,
} from "react-icons/fi";

const features = [
  {
    icon: FiSave,
    title: "Automated Savings",
    description:
      "Set it and forget it. Our smart algorithm automatically saves spare change from your daily transactions.",
    color: "from-green-400 to-green-600",
  },
  {
    icon: FiTrendingUp,
    title: "Investment Growth",
    description:
      "Watch your money grow with our diversified investment portfolios tailored to your risk tolerance.",
    color: "from-blue-400 to-blue-600",
  },
  {
    icon: FiShield,
    title: "Bank-Level Security",
    description:
      "Your money and data are protected with 256-bit encryption and FDIC insurance up to $250,000.",
    color: "from-purple-400 to-purple-600",
  },
  {
    icon: FiTarget,
    title: "Goal Setting",
    description:
      "Create personalized savings goals and track your progress with visual milestones and achievements.",
    color: "from-orange-400 to-orange-600",
  },
  {
    icon: <PERSON><PERSON><PERSON><PERSON><PERSON>,
    title: "Smart Analytics",
    description:
      "Get detailed insights into your spending patterns and receive personalized recommendations.",
    color: "from-pink-400 to-pink-600",
  },
  {
    icon: FiSmartphone,
    title: "Mobile First",
    description:
      "Manage your savings on-the-go with our intuitive mobile app available on iOS and Android.",
    color: "from-indigo-400 to-indigo-600",
  },
  {
    icon: FiBell,
    title: "Smart Notifications",
    description:
      "Stay informed with intelligent alerts about your savings progress and investment opportunities.",
    color: "from-teal-400 to-teal-600",
  },
  {
    icon: FiCreditCard,
    title: "Round-Up Savings",
    description:
      "Automatically round up purchases to the nearest dollar and save the difference effortlessly.",
    color: "from-red-400 to-red-600",
  },
  {
    icon: FiUsers,
    title: "Family Sharing",
    description:
      "Create shared savings goals with family members and track collective progress together.",
    color: "from-yellow-400 to-yellow-600",
  },
];

export default function Features() {
  return (
    <section
      id="features"
      className="py-20 bg-gradient-to-b from-black to-gray-900"
      data-oid="gcsb-j1"
    >
      <div
        className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"
        data-oid="1kk9nja"
      >
        {/* Section Header */}
        <div
          className="text-center mb-16"
          data-aos="fade-up"
          data-oid="k601cu6"
        >
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            data-oid="_lbpn7o"
          >
            <h2
              className="text-4xl md:text-5xl font-bold text-white mb-6"
              data-oid="4l-mpu0"
            >
              Powerful Features for
              <span className="text-gradient-animate" data-oid="h3x95u4">
                {" "}
                Smart Savers
              </span>
            </h2>
            <p
              className="text-xl text-gray-300 max-w-3xl mx-auto"
              data-oid="ne8st0u"
            >
              Discover the tools and features that make Koja Save the ultimate
              savings companion for achieving your financial goals faster and
              smarter.
            </p>
          </motion.div>
        </div>

        {/* Features Grid */}
        <div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          data-oid="-eau9he"
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -10 }}
              className="group"
              data-aos="fade-up"
              data-aos-delay={index * 100}
              data-oid="jrrvg5:"
            >
              <div
                className="relative p-8 rounded-2xl bg-gradient-to-br from-gray-800/50 to-gray-900/50 border border-gray-700/50 backdrop-blur-sm hover:border-green-500/30 transition-all duration-300 hover-lift"
                data-oid="kwx2lci"
              >
                {/* Icon */}
                <div
                  className={`inline-flex p-3 rounded-xl bg-gradient-to-r ${feature.color} mb-6 group-hover:scale-110 transition-transform duration-300`}
                  data-oid="dejj6kd"
                >
                  <feature.icon
                    className="text-white text-2xl"
                    data-oid="t7nftjr"
                  />
                </div>

                {/* Content */}
                <h3
                  className="text-xl font-semibold text-white mb-4 group-hover:text-green-400 transition-colors duration-300"
                  data-oid="id_bo6_"
                >
                  {feature.title}
                </h3>
                <p className="text-gray-300 leading-relaxed" data-oid="i9r_6n6">
                  {feature.description}
                </p>

                {/* Hover effect overlay */}
                <div
                  className="absolute inset-0 rounded-2xl bg-gradient-to-r from-green-500/5 to-green-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"
                  data-oid="k7oh0_6"
                ></div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
          data-aos="fade-up"
          data-aos-delay="900"
          data-oid="uu_i5.e"
        >
          <p className="text-gray-300 mb-8 text-lg" data-oid="qhru5xc">
            Ready to experience the future of savings?
          </p>
          <motion.button
            className="px-8 py-4 bg-gradient-to-r from-green-500 to-green-600 text-white font-semibold rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-green-500/25 btn-glow"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            data-oid="m.chg5:"
          >
            Start Saving Today
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
}
