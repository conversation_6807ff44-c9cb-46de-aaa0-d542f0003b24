"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  FiPlus, 
  FiTarget, 
  FiTrendingUp, 
  FiCalendar, 
  FiDollarSign,
  FiEdit3,
  FiTrash2,
  FiPause,
  FiPlay,
  FiStar,
  FiClock,
  FiCheckCircle
} from 'react-icons/fi';
import DashboardLayout from '../../../src/components/dashboard/DashboardLayout';
import { targetSavingsService } from '../../../src/services';
import { withdrawalsService } from '../../../src/services/withdrawals';
import { TargetSavings, CreateTargetSavingsData } from '../../../src/types';
import Button from '../../../src/components/ui/Button';
import Card from '../../../src/components/ui/Card';
import Badge from '../../../src/components/ui/Badge';
import Modal from '../../../src/components/ui/Modal';
import Input from '../../../src/components/ui/Input';
import Select from '../../../src/components/ui/Select';
import Textarea from '../../../src/components/ui/Textarea';
import { showToast as toast } from '../../../src/components/ui/Toast';

const categoryIcons = {
  EMERGENCY: '🚨',
  VACATION: '✈️',
  EDUCATION: '🎓',
  HOUSE: '🏠',
  CAR: '🚗',
  BUSINESS: '💼',
  WEDDING: '💒',
  HEALTH: '🏥',
  OTHER: '🎯'
};

const priorityColors = {
  LOW: 'bg-green-500',
  MEDIUM: 'bg-yellow-500',
  HIGH: 'bg-red-500'
};

export default function TargetSavingsPage() {
  const [targets, setTargets] = useState<TargetSavings[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedTarget, setSelectedTarget] = useState<TargetSavings | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [pendingWithdrawTarget, setPendingWithdrawTarget] = useState<any>(null);

  // Only include fields expected by backend: goalName (title), targetAmount, timelineMonths, frequency
  const [createForm, setCreateForm] = useState({
    title: '',
    targetAmount: 0,
    timelineMonths: 1,
    frequency: 'monthly',
  });

  useEffect(() => {
    loadTargets();
  }, []);

  const loadTargets = async () => {
    try {
      setLoading(true);
      const response = await targetSavingsService.getTargetSavings();
      setTargets(response.targets);
    } catch (error) {
      toast.error('Failed to load target savings');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTarget = async () => {
    try {
      if (!createForm.title || !createForm.targetAmount || !createForm.timelineMonths || !createForm.frequency) {
        toast.error('Please fill in all required fields');
        return;
      }
      await targetSavingsService.createTargetSavings(createForm);
      toast.success('Target savings created successfully');
      setShowCreateModal(false);
      setCreateForm({
        title: '',
        targetAmount: 0,
        timelineMonths: 1,
        frequency: 'monthly',
      });
      loadTargets();
    } catch (error) {
      toast.error('Failed to create target savings');
    }
  };

  const calculateProgress = (current: number, target: number) => {
    return Math.min((current / target) * 100, 100);
  };

  const calculateDaysRemaining = (targetDate: string) => {
    const target = new Date(targetDate);
    const now = new Date();
    const diffTime = target.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN'
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'bg-green-500';
      case 'COMPLETED': return 'bg-blue-500';
      case 'PAUSED': return 'bg-yellow-500';
      case 'CANCELLED': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  if (loading) {
    return (
      <DashboardLayout title="Target Savings">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title="Target Savings">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white">Target Savings</h1>
            <p className="text-gray-400 mt-2">Set and achieve your financial goals</p>
          </div>
          <Button
            onClick={() => setShowCreateModal(true)}
            className="bg-green-600 hover:bg-green-700"
          >
            <FiPlus className="mr-2" />
            Create Target
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Targets</p>
                <p className="text-2xl font-bold text-white">{targets.length}</p>
              </div>
              <FiTarget className="text-green-500 text-2xl" />
            </div>
          </Card>

          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Active Targets</p>
                <p className="text-2xl font-bold text-white">
                  {targets.filter(t => t.status === 'ACTIVE').length}
                </p>
              </div>
              <FiTrendingUp className="text-blue-500 text-2xl" />
            </div>
          </Card>

          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Saved</p>
                <p className="text-2xl font-bold text-white">
                  {formatCurrency(targets.reduce((sum, t) => sum + t.currentAmount, 0))}
                </p>
              </div>
              <FiDollarSign className="text-yellow-500 text-2xl" />
            </div>
          </Card>

          <Card className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Completed</p>
                <p className="text-2xl font-bold text-white">
                  {targets.filter(t => {
                    const tt: any = t;
                    // Consider completed if status is COMPLETED or progress >= 100
                    if (tt.status === 'COMPLETED') return true;
                    const currentAmount = typeof tt.currentAmount === 'number' ? tt.currentAmount : (tt.savedAmount || 0);
                    const targetAmount = typeof tt.targetAmount === 'number' ? tt.targetAmount : 0;
                    const progress = tt.progress !== undefined ? tt.progress : (targetAmount > 0 ? (currentAmount / targetAmount) * 100 : 0);
                    return progress >= 100;
                  }).length}
                </p>
              </div>
              <FiCheckCircle className="text-green-500 text-2xl" />
            </div>
          </Card>
        </div>

        {/* Targets Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {targets.map((target) => {
            // Accept both backend and frontend field names using type assertions
            const t: any = target;
            const title = t.goalName || t.title || '';
            const description = t.description || '';
            const currentAmount = typeof t.currentAmount === 'number' ? t.currentAmount : (t.savedAmount || 0);
            const targetAmount = typeof t.targetAmount === 'number' ? t.targetAmount : 0;
            const progress = t.progress !== undefined ? t.progress : calculateProgress(currentAmount, targetAmount);
            const interestAccrued = t.interestAccrued || t.interestEarned || 0;
            const interestRate = t.interestRate || 0;
            const frequency = (t.frequency || t.contributionFrequency || '').toUpperCase();
            const timelineMonths = t.timelineMonths || 1;
            const startDate = t.startDate || t.targetDate || t.createdAt || '';
            const status = t.status || (progress >= 100 ? 'COMPLETED' : 'ACTIVE');
            // Calculate end date if possible
            let endDate = '';
            if (startDate && timelineMonths) {
              const d = new Date(startDate);
              d.setMonth(d.getMonth() + timelineMonths);
              endDate = d.toISOString().split('T')[0];
            }
            // Days remaining
            let daysRemaining = 0;
            if (endDate) {
              const now = new Date();
              const end = new Date(endDate);
              daysRemaining = Math.ceil((end.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
            }

            // Show withdraw/close buttons if completed
            const isCompleted = status === 'COMPLETED' || progress >= 100;
            const handleWithdraw = async () => {
              setPendingWithdrawTarget(t);
              setShowWithdrawModal(true);
            };

            return (
              <motion.div
                key={t._id || t.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="cursor-pointer"
                onClick={() => {
                  setSelectedTarget(target);
                  setShowDetailsModal(true);
                }}
              >
                <Card className="bg-gray-800 border-gray-700 p-6 hover:border-green-500 transition-colors">
                  <div className="space-y-4">
                    {/* Header */}
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">{categoryIcons[t.category] || '🎯'}</span>
                        <div>
                          <h3 className="font-semibold text-white">{title}</h3>
                          <p className="text-sm text-gray-400">{description}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className={`w-3 h-3 rounded-full ${priorityColors[t.priority] || 'bg-gray-500'}`}></div>
                        <Badge variant={status === 'ACTIVE' ? 'success' : 'default'}>
                          {status}
                        </Badge>
                      </div>
                    </div>

                    {/* Progress */}
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-400">Progress</span>
                        <span className="text-white">{progress.toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div 
                          className="bg-green-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${progress}%` }}
                        ></div>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-400">
                          {formatCurrency(currentAmount)}
                        </span>
                        <span className="text-white">
                          {formatCurrency(targetAmount)}
                        </span>
                      </div>
                      <div className="flex justify-between text-xs text-gray-400 mt-1">
                        <span>Interest: {formatCurrency(interestAccrued)} ({interestRate}% p.a.)</span>
                        <span>Freq: {frequency}</span>
                      </div>
                    </div>

                    {/* Footer */}
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center text-gray-400">
                        <FiCalendar className="mr-1" />
                        {daysRemaining > 0 ? `${daysRemaining} days left` : 'Overdue'}
                      </div>
                      <div className="flex items-center text-gray-400">
                        <span>Start: {startDate ? new Date(startDate).toLocaleDateString() : '-'}</span>
                        {endDate && <span className="ml-2">End: {new Date(endDate).toLocaleDateString()}</span>}
                      </div>
                    </div>

                    {/* Withdraw/Close Buttons */}
                    {isCompleted && (
                      <div className="flex gap-2 pt-2">
                        <Button className="bg-blue-600 hover:bg-blue-700" onClick={handleWithdraw}>
                          Withdraw & Close Target
                        </Button>
                      </div>
                    )}
                  </div>
                </Card>
              </motion.div>
            );
          })}
        </div>

        {targets.length === 0 && (
          <div className="text-center py-12">
            <FiTarget className="mx-auto text-6xl text-gray-600 mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">No Target Savings Yet</h3>
            <p className="text-gray-400 mb-6">Create your first savings target to get started</p>
            <Button
              onClick={() => setShowCreateModal(true)}
              className="bg-green-600 hover:bg-green-700"
            >
              <FiPlus className="mr-2" />
              Create Your First Target
            </Button>
          </div>
        )}
      </div>

      {/* Create Target Modal */}
      <Modal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        title="Create Target Savings"
        size="lg"
      >
        <div className="space-y-4">

      <Input
        label="Target Title"
        value={createForm.title}
        onChange={(e) => setCreateForm({ ...createForm, title: e.target.value })}
        placeholder="e.g., Emergency Fund"
        required
      />

      <Input
        label="Target Amount"
        type="number"
        value={createForm.targetAmount}
        onChange={(e) => setCreateForm({ ...createForm, targetAmount: Number(e.target.value) })}
        placeholder="0"
        required
      />

      <Input
        label="Timeline (months)"
        type="number"
        value={createForm.timelineMonths}
        onChange={(e) => setCreateForm({ ...createForm, timelineMonths: Number(e.target.value) })}
        placeholder="e.g., 12"
        required
      />

      <Select
        label="Frequency"
        value={createForm.frequency}
        onChange={(value) => setCreateForm({ ...createForm, frequency: value as string })}
        options={[
          { value: 'daily', label: 'Daily' },
          { value: 'weekly', label: 'Weekly' },
          { value: 'monthly', label: 'Monthly' }
        ]}
        required
      />

          <div className="flex justify-end space-x-3 pt-4">
            <Button
              variant="outline"
              onClick={() => setShowCreateModal(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateTarget}
              className="bg-green-600 hover:bg-green-700"
            >
              Create Target
            </Button>
          </div>
        </div>
      </Modal>
      {/* Withdraw Confirmation Modal */}
      <Modal
        isOpen={showWithdrawModal}
        onClose={() => {
          setShowWithdrawModal(false);
          setPendingWithdrawTarget(null);
        }}
        title="Withdraw & Close Target"
        size="md"
      >
        <div className="space-y-4">
          <p className="text-gray-200 text-lg font-semibold">Are you sure you want to withdraw and close this target?</p>
          <p className="text-gray-400">This will credit your in-app balance with the saved amount. This action cannot be undone.</p>
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              variant="outline"
              onClick={() => {
                setShowWithdrawModal(false);
                setPendingWithdrawTarget(null);
              }}
            >
              Cancel
            </Button>
            <Button
              className="bg-blue-600 hover:bg-blue-700"
              onClick={async () => {
                if (!pendingWithdrawTarget) return;
                try {
                  // Send both planId and amount as required by backend
                  await withdrawalsService.initiatePlanClosure({
                    planId: pendingWithdrawTarget._id || pendingWithdrawTarget.id,
                    amount: pendingWithdrawTarget.currentAmount || pendingWithdrawTarget.savedAmount || 0
                  });
                  toast.success('Withdrawal successful! Funds credited to your in-app balance.');
                  setShowWithdrawModal(false);
                  setPendingWithdrawTarget(null);
                  loadTargets();
                } catch (err: any) {
                  toast.error(err.message || 'Withdrawal failed');
                }
              }}
            >
              Yes, Withdraw & Close
            </Button>
          </div>
        </div>
      </Modal>
    </DashboardLayout>
  );
}
