"use client";

import { ReactNode } from "react";
import Link from "next/link";
import { motion } from "framer-motion";
import { FiSave, FiArrowLeft } from "react-icons/fi";
import Background3D from "./Background3D";

interface PageLayoutProps {
  children: ReactNode;
  showBackButton?: boolean;
  backHref?: string;
  backText?: string;
}

export default function PageLayout({ 
  children, 
  showBackButton = true, 
  backHref = "/", 
  backText = "Back to Home" 
}: PageLayoutProps) {
  return (
    <div className="min-h-screen bg-black text-white overflow-hidden relative">
      {/* 3D Background */}
      <Background3D />
      
      {/* Enhanced Animated Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-black"></div>
        <div className="mesh-gradient"></div>
        <div className="mesh-gradient-2"></div>
        <div className="mesh-gradient-3"></div>
        <div className="absolute inset-0 opacity-20">
          {[...Array(40)].map((_, i) => (
            <div
              key={i}
              className="absolute bg-green-400 rounded-full floating-particle"
              style={{
                width: `${Math.random() * 3 + 1}px`,
                height: `${Math.random() * 3 + 1}px`,
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 8}s`,
                animationDuration: `${8 + Math.random() * 4}s`,
              }}
            />
          ))}
        </div>
        <div className="absolute inset-0 grid-pattern opacity-5"></div>
      </div>

      {/* Navigation */}
      <nav className="relative z-50 px-6 py-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          {showBackButton && (
            <Link
              href={backHref}
              className="flex items-center space-x-2 hover:opacity-80 transition-opacity"
            >
              <FiArrowLeft className="w-5 h-5 text-white" />
              <span className="text-white">{backText}</span>
            </Link>
          )}

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center space-x-2"
          >
            <div className="w-10 h-10 bg-gradient-to-r from-green-400 to-green-600 rounded-lg flex items-center justify-center">
              <FiSave className="w-6 h-6 text-white" />
            </div>
            <span className="text-2xl font-bold text-white">
              Koja Save
            </span>
          </motion.div>
        </div>
      </nav>

      {/* Page Content */}
      <main className="relative z-10">
        {children}
      </main>

      {/* Footer */}
      <footer className="relative z-10 px-6 py-12 border-t border-gray-700">
        <div className="max-w-7xl mx-auto text-center">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <div className="w-8 h-8 bg-gradient-to-r from-green-400 to-green-600 rounded-lg flex items-center justify-center">
              <FiSave className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold text-white">
              Koja Save
            </span>
          </div>
          <p className="text-gray-400">
            Building the future of personal finance, one save at a time.
          </p>
        </div>
      </footer>
    </div>
  );
}
