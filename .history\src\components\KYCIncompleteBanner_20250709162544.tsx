import React from 'react';
import { FiUserCheck } from 'react-icons/fi';
import { useRouter } from 'next/navigation';

/**
 * Banner/modal for incomplete KYC status.
 * Shows a warning and a button to go to KYC page.
 */
export function KYCIncompleteBanner({ className = '' }: { className?: string }) {
  const router = useRouter();
  return (
    <div className={`bg-red-600/10 border border-red-600 rounded-lg p-6 mb-6 flex items-center space-x-3 ${className}`}>
      <FiUserCheck className="w-6 h-6 text-red-500" />
      <div className="flex-1">
        <h3 className="text-red-500 font-semibold">Complete Your KYC Verification</h3>
        <p className="text-red-300 text-sm">
          Verify your identity to unlock higher savings limits and additional features.
        </p>
      </div>
      <button
        onClick={() => router.push('/dashboard/kyc')}
        className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
      >
        Verify Now
      </button>
    </div>
  );
}
