"use client";

import { useEffect, useState } from "react";
import {
  FiActivity,
  FiBarChart,
  FiCreditCard,
  FiDollarSign,
  FiDownload,
  FiPieChart,
  FiRefreshCw,
  FiTarget,
  FiTrendingDown,
  FiTrendingUp,
  FiUsers,
} from "react-icons/fi";
import { OutlineButton } from "../../../src/components/ui/AnimatedButton";
import { Card3D } from "../../../src/components/ui/Card3D";
import { showToast } from "../../../src/components/ui/Toast";
import { adminService } from "../../../src/services";

export default function AdminAnalyticsPage() {
  const [loading, setLoading] = useState(true);
  const [timeframe, setTimeframe] = useState("month");
  const [analytics, setAnalytics] = useState<any>(null);

  useEffect(() => {
    loadAnalytics();
  }, [timeframe]);

  const loadAnalytics = async () => {
    try {
      setLoading(true);
      const data = await adminService.getAnalytics({
        period: timeframe as any,
      });
      setAnalytics(data);
    } catch (error) {
      showToast.error("Failed to load analytics");
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-NG", {
      style: "currency",
      currency: "NGN",
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? "+" : ""}${value.toFixed(1)}%`;
  };

  const getGrowthColor = (value: number) => {
    return value >= 0 ? "text-green-400" : "text-red-400";
  };

  const getGrowthIcon = (value: number) => {
    return value >= 0 ? <FiTrendingUp /> : <FiTrendingDown />;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 text-white p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"></div>
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="min-h-screen bg-gray-900 text-white p-6">
        <div className="text-center py-12">
          <p className="text-gray-400">Failed to load analytics data</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white">
              Analytics Dashboard
            </h1>
            <p className="text-gray-400 mt-2">
              Comprehensive platform insights and metrics
            </p>
          </div>
          <div className="flex space-x-2">
            <select
              value={timeframe}
              onChange={(e) => setTimeframe(e.target.value)}
              className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              <option value="week">Last Week</option>
              <option value="month">Last Month</option>
              <option value="quarter">Last Quarter</option>
              <option value="year">Last Year</option>
            </select>
            <OutlineButton onClick={loadAnalytics}>
              <FiRefreshCw className="mr-2" />
              Refresh
            </OutlineButton>
            <OutlineButton>
              <FiDownload className="mr-2" />
              Export
            </OutlineButton>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Users</p>
                <p className="text-2xl font-bold text-white">
                  {analytics.users.total.toLocaleString()}
                </p>
                <div className="flex items-center mt-1">
                  <span
                    className={`text-sm ${getGrowthColor(analytics.users.growth)}`}
                  >
                    {getGrowthIcon(analytics.users.growth)}
                    {formatPercentage(analytics.users.growth)}
                  </span>
                  <span className="text-gray-400 text-sm ml-1">
                    vs last {timeframe}
                  </span>
                </div>
              </div>
              <FiUsers className="text-blue-500 text-3xl" />
            </div>
          </div>

          <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Volume</p>
                <p className="text-2xl font-bold text-white">
                  {formatCurrency(analytics.financial.totalVolume)}
                </p>
                <div className="flex items-center mt-1">
                  <span
                    className={`text-sm ${getGrowthColor(analytics.financial.volumeGrowth)}`}
                  >
                    {getGrowthIcon(analytics.financial.volumeGrowth)}
                    {formatPercentage(analytics.financial.volumeGrowth)}
                  </span>
                  <span className="text-gray-400 text-sm ml-1">
                    vs last {timeframe}
                  </span>
                </div>
              </div>
              <FiDollarSign className="text-green-500 text-3xl" />
            </div>
          </div>

          <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Active Savings</p>
                <p className="text-2xl font-bold text-white">
                  {analytics.savings.activePlans.toLocaleString()}
                </p>
                <div className="flex items-center mt-1">
                  <span
                    className={`text-sm ${getGrowthColor(analytics.savings.growth)}`}
                  >
                    {getGrowthIcon(analytics.savings.growth)}
                    {formatPercentage(analytics.savings.growth)}
                  </span>
                  <span className="text-gray-400 text-sm ml-1">
                    vs last {timeframe}
                  </span>
                </div>
              </div>
              <FiTarget className="text-purple-500 text-3xl" />
            </div>
          </div>

          <Card3D className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Transaction Success</p>
                <p className="text-2xl font-bold text-white">
                  {analytics.transactions.successRate.toFixed(1)}%
                </p>
                <div className="flex items-center mt-1">
                  <span
                    className={`text-sm ${getGrowthColor(analytics.transactions.successRateChange)}`}
                  >
                    {getGrowthIcon(analytics.transactions.successRateChange)}
                    {formatPercentage(analytics.transactions.successRateChange)}
                  </span>
                  <span className="text-gray-400 text-sm ml-1">
                    vs last {timeframe}
                  </span>
                </div>
              </div>
              <FiActivity className="text-yellow-500 text-3xl" />
            </div>
          </Card3D>
        </div>

        {/* Charts Row */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* User Growth Chart */}
          <Card3D className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">
                User Growth Trend
              </h3>
              <FiBarChart className="text-blue-500" />
            </div>
            <div className="h-64 bg-gray-700 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <FiBarChart className="mx-auto text-4xl text-gray-500 mb-2" />
                <p className="text-gray-400">User Growth Chart</p>
                <p className="text-sm text-gray-500">
                  Chart visualization would be here
                </p>
              </div>
            </div>
            <div className="mt-4 grid grid-cols-3 gap-4 text-center">
              <div>
                <p className="text-gray-400 text-sm">New Users</p>
                <p className="text-white font-semibold">
                  {analytics.users.newUsers}
                </p>
              </div>
              <div>
                <p className="text-gray-400 text-sm">Active Users</p>
                <p className="text-white font-semibold">
                  {analytics.users.activeUsers}
                </p>
              </div>
              <div>
                <p className="text-gray-400 text-sm">Retention Rate</p>
                <p className="text-white font-semibold">
                  {analytics.users.retentionRate.toFixed(1)}%
                </p>
              </div>
            </div>
          </Card3D>

          {/* Revenue Chart */}
          <Card3D className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">
                Revenue Breakdown
              </h3>
              <FiPieChart className="text-green-500" />
            </div>
            <div className="h-64 bg-gray-700 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <FiPieChart className="mx-auto text-4xl text-gray-500 mb-2" />
                <p className="text-gray-400">Revenue Pie Chart</p>
                <p className="text-sm text-gray-500">
                  Chart visualization would be here
                </p>
              </div>
            </div>
            <div className="mt-4 space-y-2">
              <div className="flex justify-between items-center">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-gray-400">Deposit Fees</span>
                </div>
                <span className="text-white">
                  {formatCurrency(analytics.revenue.depositFees)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-gray-400">Withdrawal Fees</span>
                </div>
                <span className="text-white">
                  {formatCurrency(analytics.revenue.withdrawalFees)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                  <span className="text-gray-400">Interest Earned</span>
                </div>
                <span className="text-white">
                  {formatCurrency(analytics.revenue.interestEarned)}
                </span>
              </div>
            </div>
          </Card3D>
        </div>

        {/* Detailed Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* User Analytics */}
          <Card3D className="bg-gray-800 border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <FiUsers className="mr-2" />
              User Analytics
            </h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Total Registered</span>
                <span className="text-white font-semibold">
                  {analytics.users.total.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">KYC Verified</span>
                <div className="flex items-center space-x-2">
                  <span className="text-green-400 font-semibold">
                    {analytics.users.kycVerified.toLocaleString()}
                  </span>
                  <span className="bg-green-500/20 text-green-400 px-2 py-1 rounded-full text-xs font-medium">
                    {(
                      (analytics.users.kycVerified / analytics.users.total) *
                      100
                    ).toFixed(1)}
                    %
                  </span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Monthly Active</span>
                <span className="text-blue-400 font-semibold">
                  {analytics.users.monthlyActive.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Avg. Session Duration</span>
                <span className="text-white font-semibold">
                  {analytics.users.avgSessionDuration} min
                </span>
              </div>
            </div>
          </Card3D>

          {/* Financial Analytics */}
          <Card3D className="bg-gray-800 border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <FiDollarSign className="mr-2" />
              Financial Analytics
            </h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Total Deposits</span>
                <span className="text-green-400 font-semibold">
                  {formatCurrency(analytics.financial.totalDeposits)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Total Withdrawals</span>
                <span className="text-blue-400 font-semibold">
                  {formatCurrency(analytics.financial.totalWithdrawals)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Net Flow</span>
                <span className="text-white font-semibold">
                  {formatCurrency(
                    analytics.financial.totalDeposits -
                      analytics.financial.totalWithdrawals
                  )}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Avg. Transaction</span>
                <span className="text-white font-semibold">
                  {formatCurrency(analytics.financial.avgTransactionSize)}
                </span>
              </div>
            </div>
          </Card3D>

          {/* Savings Analytics */}
          <Card3D className="bg-gray-800 border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <FiTarget className="mr-2" />
              Savings Analytics
            </h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Active Plans</span>
                <span className="text-purple-400 font-semibold">
                  {analytics.savings.activePlans.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Completed Plans</span>
                <span className="text-green-400 font-semibold">
                  {analytics.savings.completedPlans.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Total Saved</span>
                <span className="text-white font-semibold">
                  {formatCurrency(analytics.savings.totalSaved)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Completion Rate</span>
                <span className="bg-green-500/20 text-green-400 px-2 py-1 rounded-full text-xs font-medium">
                  {analytics.savings.completionRate.toFixed(1)}%
                </span>
              </div>
            </div>
          </Card3D>
        </div>

        {/* Performance Metrics */}
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <FiActivity className="mr-2" />
            Platform Performance
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-green-600/20 rounded-full flex items-center justify-center mx-auto mb-2">
                <FiCreditCard className="text-green-400 text-2xl" />
              </div>
              <p className="text-gray-400 text-sm">Payment Success Rate</p>
              <p className="text-2xl font-bold text-white">
                {analytics.performance.paymentSuccessRate.toFixed(1)}%
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600/20 rounded-full flex items-center justify-center mx-auto mb-2">
                <FiActivity className="text-blue-400 text-2xl" />
              </div>
              <p className="text-gray-400 text-sm">System Uptime</p>
              <p className="text-2xl font-bold text-white">
                {analytics.performance.systemUptime.toFixed(2)}%
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-purple-600/20 rounded-full flex items-center justify-center mx-auto mb-2">
                <FiUsers className="text-purple-400 text-2xl" />
              </div>
              <p className="text-gray-400 text-sm">User Satisfaction</p>
              <p className="text-2xl font-bold text-white">
                {analytics.performance.userSatisfaction.toFixed(1)}/5
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-yellow-600/20 rounded-full flex items-center justify-center mx-auto mb-2">
                <FiTrendingUp className="text-yellow-400 text-2xl" />
              </div>
              <p className="text-gray-400 text-sm">Growth Rate</p>
              <p className="text-2xl font-bold text-white">
                {formatPercentage(analytics.performance.monthlyGrowthRate)}
              </p>
            </div>
          </div>
        </div>

        {/* Recent Activity Summary */}
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4">
            Recent Activity Summary
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h4 className="font-medium text-white mb-2">
                Top Performing Regions
              </h4>
              <div className="space-y-2">
                {analytics.regions.map((region: any, index: number) => (
                  <div
                    key={index}
                    className="flex justify-between items-center"
                  >
                    <span className="text-gray-400">{region.name}</span>
                    <span className="text-white">
                      {region.users.toLocaleString()} users
                    </span>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h4 className="font-medium text-white mb-2">
                Popular Savings Plans
              </h4>
              <div className="space-y-2">
                {analytics.popularPlans.map((plan: any, index: number) => (
                  <div
                    key={index}
                    className="flex justify-between items-center"
                  >
                    <span className="text-gray-400">{plan.type}</span>
                    <span className="text-white">{plan.count} plans</span>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h4 className="font-medium text-white mb-2">System Alerts</h4>
              <div className="space-y-2">
                {analytics.alerts.map((alert: any, index: number) => (
                  <div key={index} className="flex items-center space-x-2">
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${
                        alert.severity === "high"
                          ? "bg-red-500/20 text-red-400"
                          : alert.severity === "medium"
                            ? "bg-yellow-500/20 text-yellow-400"
                            : "bg-blue-500/20 text-blue-400"
                      }`}
                    >
                      {alert.severity}
                    </span>
                    <span className="text-gray-400 text-sm">
                      {alert.message}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
