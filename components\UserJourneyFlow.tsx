"use client";

import { motion } from "framer-motion";
import { 
  FiUserPlus, 
  FiCreditCard, 
  FiTarget, 
  FiTrendingUp, 
  FiAward,
  FiArrowRight,
  FiCheck
} from "react-icons/fi";

interface FlowStepProps {
  step: number;
  title: string;
  description: string;
  icon: React.ReactNode;
  features: string[];
  isLast?: boolean;
  delay?: number;
}

const FlowStep = ({ step, title, description, icon, features, isLast, delay = 0 }: FlowStepProps) => {
  return (
    <div className="relative">
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.6, delay }}
        className="infographic-node rounded-2xl p-6 relative z-10"
      >
        {/* Step number */}
        <div className="absolute -top-4 -left-4 w-8 h-8 bg-green-400 rounded-full flex items-center justify-center text-black font-bold text-sm">
          {step}
        </div>

        {/* Icon */}
        <div className="w-16 h-16 bg-green-400/20 rounded-2xl flex items-center justify-center mb-4 mx-auto">
          <div className="text-green-400 text-2xl">
            {icon}
          </div>
        </div>

        {/* Content */}
        <div className="text-center mb-6">
          <h3 className="text-xl font-bold text-white mb-2">{title}</h3>
          <p className="text-gray-300 text-sm">{description}</p>
        </div>

        {/* Features */}
        <div className="space-y-2">
          {features.map((feature, i) => (
            <motion.div
              key={i}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.4, delay: delay + 0.1 + (i * 0.1) }}
              className="flex items-center space-x-2 text-sm"
            >
              <FiCheck className="w-4 h-4 text-green-400 flex-shrink-0" />
              <span className="text-gray-300">{feature}</span>
            </motion.div>
          ))}
        </div>

        {/* Progress indicator */}
        <div className="mt-6 w-full bg-gray-800 rounded-full h-2">
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: `${(step / 5) * 100}%` }}
            transition={{ duration: 1, delay: delay + 0.5 }}
            className="bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full"
          />
        </div>
      </motion.div>

      {/* Connector arrow */}
      {!isLast && (
        <motion.div
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.4, delay: delay + 0.3 }}
          className="hidden lg:block absolute top-1/2 -right-8 transform -translate-y-1/2 z-20"
        >
          <div className="w-16 h-0.5 bg-gradient-to-r from-green-400 to-green-600 relative flow-animation">
            <FiArrowRight className="absolute -right-2 -top-2 w-4 h-4 text-green-400" />
          </div>
        </motion.div>
      )}

      {/* Mobile connector */}
      {!isLast && (
        <motion.div
          initial={{ opacity: 0, scaleY: 0 }}
          animate={{ opacity: 1, scaleY: 1 }}
          transition={{ duration: 0.4, delay: delay + 0.3 }}
          className="lg:hidden w-0.5 h-8 bg-gradient-to-b from-green-400 to-green-600 mx-auto my-4 relative"
        >
          <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2">
            <FiArrowRight className="w-4 h-4 text-green-400 rotate-90" />
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default function UserJourneyFlow() {
  const journeySteps = [
    {
      step: 1,
      title: "Sign Up",
      description: "Create your account in under 2 minutes with our streamlined onboarding process.",
      icon: <FiUserPlus />,
      features: [
        "Quick email verification",
        "Secure password setup",
        "Profile customization",
        "Welcome bonus eligibility"
      ]
    },
    {
      step: 2,
      title: "Connect Accounts",
      description: "Safely link your bank accounts and cards using bank-level encryption.",
      icon: <FiCreditCard />,
      features: [
        "Bank-grade security",
        "Multiple account support",
        "Instant verification",
        "Read-only access"
      ]
    },
    {
      step: 3,
      title: "Set Goals",
      description: "Define your financial objectives and let our AI create personalized strategies.",
      icon: <FiTarget />,
      features: [
        "Smart goal suggestions",
        "Timeline planning",
        "Budget allocation",
        "Milestone tracking"
      ]
    },
    {
      step: 4,
      title: "Start Saving",
      description: "Watch your money grow with automated savings and intelligent investments.",
      icon: <FiTrendingUp />,
      features: [
        "Automated transfers",
        "Round-up savings",
        "Investment portfolios",
        "Real-time tracking"
      ]
    },
    {
      step: 5,
      title: "Achieve Success",
      description: "Reach your financial milestones and unlock exclusive rewards and benefits.",
      icon: <FiAward />,
      features: [
        "Goal completion rewards",
        "Premium features unlock",
        "Financial insights",
        "Community recognition"
      ]
    }
  ];

  return (
    <section className="py-20 px-6 relative z-10">
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
            Your Journey to
            <span className="gradient-text block">Financial Freedom</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            Follow our proven 5-step process to transform your financial future. 
            Each step is designed to build upon the previous one, creating a solid 
            foundation for long-term wealth building.
          </p>
          
          {/* Overall progress indicator */}
          <div className="max-w-md mx-auto">
            <div className="flex justify-between text-sm text-gray-400 mb-2">
              <span>Start</span>
              <span>Financial Freedom</span>
            </div>
            <div className="w-full bg-gray-800 rounded-full h-3">
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: "100%" }}
                transition={{ duration: 2, delay: 1 }}
                className="bg-gradient-to-r from-green-400 via-green-500 to-green-600 h-3 rounded-full relative"
              >
                <div className="absolute right-0 top-0 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
              </motion.div>
            </div>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-5 gap-8 lg:gap-4">
          {journeySteps.map((step, index) => (
            <FlowStep
              key={index}
              {...step}
              isLast={index === journeySteps.length - 1}
              delay={index * 0.2}
            />
          ))}
        </div>

        {/* Call to action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.5 }}
          className="text-center mt-16"
        >
          <div className="glass-advanced rounded-2xl p-8 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-white mb-4">
              Ready to Start Your Journey?
            </h3>
            <p className="text-gray-300 mb-6">
              Join thousands of users who have already transformed their financial lives with Koja Save.
            </p>
            <button className="btn-advanced px-8 py-4 rounded-lg text-white font-semibold">
              Begin Your Journey Today
            </button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
