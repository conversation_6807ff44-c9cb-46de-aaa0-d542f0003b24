"use client";

import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import AOS from "aos";
import "aos/dist/aos.css";
import Link from "next/link";
import {
  FiSave,
  FiTrendingUp,
  FiShield,
  FiSmartphone,
  FiArrowRight,
  FiMenu,
  FiX,
} from "react-icons/fi";
import { FaFacebook, FaTwitter, FaInstagram } from "react-icons/fa";
import Background3D from "../components/Background3D";
import AppCards from "../components/AppCards";
import UserJourneyFlow from "../components/UserJourneyFlow";
import { BetterInterestLogo } from "../src/components/ui/BetterInterestLogo";
import { BackgroundAnimation } from "../src/components/ui/BackgroundAnimation";
import { AnimatedButton, PrimaryButton, OutlineButton } from "../src/components/ui/AnimatedButton";
import { HeroImage3D, TestimonialImage3D, CardImage3D, FeatureImage3D } from "../src/components/ui/Image3D";
import Image from "next/image";

export default function Page() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
      easing: "ease-out-cubic",
    });
  }, []);

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-gray-900 via-green-900 to-black text-white overflow-hidden relative"
      data-oid="ks._8a0"
    >
      {/* Background Animation */}
      <BackgroundAnimation variant="default" />

      {/* Navigation */}
      <nav className="relative z-50 px-6 py-4" data-oid="g5gj77n">
        <div
          className="max-w-7xl mx-auto flex items-center justify-between"
          data-oid="hp_52bw"
        >
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center space-x-2"
            data-oid="489sboo"
          >
            <BetterInterestLogo size="md" variant="light" />
          </motion.div>

          {/* Desktop Navigation */}
          <div
            className="hidden md:flex items-center space-x-8"
            data-oid="982t-4t"
          >
            <Link
              href="/features"
              className="text-white hover:text-green-400 transition-colors"
              data-oid="gn3b7rs"
            >
              Features
            </Link>
            <Link
              href="/packages"
              className="text-white hover:text-green-400 transition-colors"
              data-oid="sfikgqx"
            >
              Savings Packages
            </Link>
            <Link
              href="/about"
              className="text-white hover:text-green-400 transition-colors"
              data-oid="ntp_4-_"
            >
              About Us
            </Link>
            <Link
              href="/contact"
              className="text-white hover:text-green-400 transition-colors"
              data-oid="hi775.x"
            >
              Contact
            </Link>
            <OutlineButton href="/auth/login" size="sm">
              Login
            </OutlineButton>
            <PrimaryButton href="/auth/signup" size="sm">
              Sign Up
            </PrimaryButton>
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden text-white"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            data-oid="dg4bjrr"
          >
            {isMenuOpen ? (
              <FiX className="w-6 h-6" data-oid="l7mi5i6" />
            ) : (
              <FiMenu className="w-6 h-6" data-oid=":c-68ho" />
            )}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="md:hidden absolute top-full left-0 right-0 bg-black/90 backdrop-blur-lg border-t border-green-400/20 p-6"
            data-oid="3dae6zc"
          >
            <div className="flex flex-col space-y-4" data-oid="6fykwdx">
              <Link
                href="/features"
                className="text-white hover:text-green-400 transition-colors"
                data-oid="81psh67"
              >
                Features
              </Link>
              <Link
                href="/packages"
                className="text-white hover:text-green-400 transition-colors"
                data-oid="_p-gpkn"
              >
                Savings Packages
              </Link>
              <Link
                href="/about"
                className="text-white hover:text-green-400 transition-colors"
                data-oid="wrgko2f"
              >
                About Us
              </Link>
              <Link
                href="/contact"
                className="text-white hover:text-green-400 transition-colors"
                data-oid="ka::_94"
              >
                Contact
              </Link>
              <OutlineButton href="/auth/login" size="sm" className="w-full">
                Login
              </OutlineButton>
              <PrimaryButton href="/auth/signup" size="sm" className="w-full">
                Sign Up
              </PrimaryButton>
            </div>
          </motion.div>
        )}
      </nav>

      {/* Hero Section */}
      <section className="relative z-10 px-6 py-20 md:py-32" data-oid="j.qvedz">
        <div className="max-w-7xl mx-auto text-center" data-oid="5vxfm7q">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mb-8"
          >
            <h1
              className="text-5xl md:text-7xl font-inter font-bold mb-6 text-white text-shadow-green"
              data-aos="fade-up"
              data-oid="7d0cjki"
            >
              Save Smarter,
              <br data-oid="z5y1x-4" />
              <span className="text-green-400" data-oid="8pjh6w4">
                Earn Better Interest
              </span>
            </h1>

            <p
              className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto"
              data-aos="fade-up"
              data-aos-delay="200"
              data-oid="b_r155n"
            >
              Transform your financial future with BetterInterest's intelligent savings
              platform. Automate your savings, earn better interest rates, and watch your money
              grow faster than ever before.
            </p>

            <div
              className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12"
              data-aos="fade-up"
              data-aos-delay="400"
              data-oid="9g5:cm."
            >
              <PrimaryButton href="/auth/signup" size="lg">
                Start Saving Now
              </PrimaryButton>
              <OutlineButton size="lg">
                Watch Demo
              </OutlineButton>
            </div>

            {/* Hero Image - Enhanced 3D Effect */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1.2, delay: 0.6, type: "spring", stiffness: 100 }}
              className="relative max-w-sm mx-auto flex justify-center"
              data-aos="fade-up"
              data-aos-delay="600"
            >
              <div className="relative">
                <div className="hero-card flex items-center justify-center overflow-hidden">
                  <HeroImage3D
                  src="/suite.png"
                    alt="Better Interest comprehensive financial suite"
                    width={190}
                    height={250}
                    priority
                    intensity="strong"
                    className="w-full h-full object-cover"
                  />
                </div>

                {/* Single floating badge */}
                <motion.div
                  animate={{
                    y: [0, -5, 0],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className="absolute -top-2 -right-2 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-semibold shadow-lg z-20"
                >
                  Better Interest ✨
                </motion.div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Quick Features Preview */}
      <section className="relative z-10 px-6 py-20" data-oid="t0r.1tk">
        <div className="max-w-7xl mx-auto" data-oid="uoxcknv">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
            data-oid="gf8:866"
          >
            <h2
              className="text-4xl md:text-5xl font-inter font-bold mb-6 text-white text-shadow"
              data-aos="fade-up"
              data-oid="7jxzm_n"
            >
              Why Choose{" "}
              <span className="text-green-400" data-oid="34q1jha">
                BetterInterest?
              </span>
            </h2>
            <p
              className="text-xl text-gray-300 max-w-3xl mx-auto mb-8"
              data-aos="fade-up"
              data-aos-delay="200"
              data-oid="z.ku9kv"
            >
              Our platform combines cutting-edge technology with proven
              financial strategies to help you achieve your savings goals faster
              than ever before.
            </p>
            <div
              data-aos="fade-up"
              data-aos-delay="400"
            >
              <PrimaryButton href="/features" size="md">
                View All Features
              </PrimaryButton>
            </div>
          </motion.div>

          <div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
            data-oid="w1f_i:e"
          >
            {[
              {
                icon: <FiSave className="w-8 h-8" data-oid="e4fh5d2" />,
                title: "Smart Savings",
                description:
                  "Automatically save money with our intelligent algorithms.",
              },
              {
                icon: <FiTrendingUp className="w-8 h-8" data-oid="ntn7d.g" />,
                title: "Investment Growth",
                description:
                  "Watch your savings grow with curated investment portfolios.",
              },
              {
                icon: <FiShield className="w-8 h-8" data-oid="7-xq3l2" />,
                title: "Bank-Level Security",
                description:
                  "Your money is protected with military-grade encryption.",
              },
              {
                icon: <FiSmartphone className="w-8 h-8" data-oid="xubpu0p" />,
                title: "Mobile First",
                description:
                  "Manage your savings on-the-go with our intuitive mobile app.",
              },
            ].map((feature, index) => (
              <div
                key={index}
                data-aos="fade-up"
                data-aos-delay={index * 100}
                className="glass rounded-xl p-6 hover-lift group bg-gray-900/30 border border-gray-700"
                data-oid="8js.:n_"
              >
                <div
                  className="text-green-400 mb-4 group-hover:animate-pulse-green transition-all"
                  data-oid=":_i39j6"
                >
                  {feature.icon}
                </div>
                <h3
                  className="text-xl font-semibold mb-3 text-white"
                  data-oid="_-yrjg8"
                >
                  {feature.title}
                </h3>
                <p className="text-gray-300" data-oid="pnfsu5d">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Our Office & Team Section */}
      <section className="relative z-10 px-6 py-20" data-oid="office-team">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2
              className="text-4xl md:text-5xl font-inter font-bold mb-6 text-white text-shadow"
              data-aos="fade-up"
            >
              Welcome to{" "}
              <span className="text-green-400">
                BetterInterest
              </span>
            </h2>
            <p
              className="text-xl text-gray-300 max-w-3xl mx-auto"
              data-aos="fade-up"
              data-aos-delay="200"
            >
              Step into our modern office where financial innovation meets personal service.
              Our team is dedicated to helping you achieve better interest rates and financial success.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Office Image */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="relative flex justify-center"
            >
              <div className="relative">
                <FeatureImage3D
                  src="/Entering Better Interest Office (1).png"
                  alt="BetterInterest modern office entrance - Professional financial services"
                  width={600}
                  height={400}
                  intensity="strong"
                  className="w-full h-auto mx-auto"
                />
                <div className="absolute bottom-4 left-4 bg-black/70 backdrop-blur-sm rounded-lg p-4 z-20">
                  <h3 className="text-white font-semibold mb-1">Modern Office</h3>
                  <p className="text-green-400 text-sm">Professional & Welcoming Environment</p>
                </div>
              </div>
            </motion.div>

            {/* Team Success Stories */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="space-y-8"
            >
              <div className="grid grid-cols-2 gap-6">
                <div className="relative flex justify-center">
                  <div className="relative">
                    <CardImage3D
                      src="/Celebrating with Her New iPhone.png"
                      alt="Happy client celebrating financial success"
                      width={250}
                      height={200}
                      intensity="medium"
                      className="w-full h-auto mx-auto"
                    />
                    <div className="absolute -bottom-2 -right-2 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-semibold shadow-lg z-20">
                      Success Story
                    </div>
                  </div>
                </div>

                <div className="relative flex justify-center">
                  <div className="relative">
                    <CardImage3D
                      src="/images/nneww.png"
                      alt="Client achieving savings goals"
                      width={250}
                      height={200}
                      intensity="medium"
                      className="w-full h-auto mx-auto"
                    />
                    <div className="absolute -bottom-2 -right-2 bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-semibold shadow-lg z-20">
                      Goal Achieved
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-900/30 border border-gray-700 rounded-xl p-6">
                <h3 className="text-xl font-bold text-white mb-4">Why Choose Our Office?</h3>
                <ul className="space-y-3 text-gray-300">
                  <li className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span>Face-to-face financial consultations</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span>Secure document processing</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span>Personalized savings strategies</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span>Professional financial advisors</span>
                  </li>
                </ul>

                <div className="mt-6">
                  <PrimaryButton href="/contact" size="md">
                    Visit Our Office
                  </PrimaryButton>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Savings Packages Preview */}
      <section className="relative z-10 px-6 py-20" data-oid="q2e2hnx">
        <div className="max-w-7xl mx-auto" data-oid="z1geoeq">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
            data-oid="2-e1_z8"
          >
            <h2
              className="text-4xl md:text-5xl font-inter font-bold mb-6 text-white text-shadow"
              data-aos="fade-up"
              data-oid="dh4t_tm"
            >
              Choose Your{" "}
              <span className="text-green-400" data-oid="vmya1q0">
                Savings Package
              </span>
            </h2>
            <p
              className="text-xl text-gray-300 mb-8"
              data-aos="fade-up"
              data-aos-delay="200"
              data-oid="i0.0tkl"
            >
              Start free and upgrade as your savings grow.
            </p>
            <div
              data-aos="fade-up"
              data-aos-delay="400"
            >
              <PrimaryButton href="/packages" size="md">
                View All Packages
              </PrimaryButton>
            </div>
          </motion.div>

          <div
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
            data-oid="khkb:n9"
          >
            {[
              {
                name: "Basic",
                price: "Free",
                features: [
                  "Basic savings tracking",
                  "Mobile app access",
                  "Email support",
                ],

                popular: false,
              },
              {
                name: "Pro",
                price: "₦2,500/mo",
                features: [
                  "Advanced analytics",
                  "Investment portfolios",
                  "Priority support",
                  "Goal tracking",
                ],

                popular: true,
              },
              {
                name: "Premium",
                price: "₦5,000/mo",
                features: [
                  "Everything in Pro",
                  "Personal advisor",
                  "Custom strategies",
                  "Tax optimization",
                ],

                popular: false,
              },
            ].map((plan, index) => (
              <div
                key={index}
                data-aos="fade-up"
                data-aos-delay={index * 100}
                className={`glass rounded-xl p-8 hover-lift relative bg-gray-900/30 border ${
                  plan.popular ? "border-green-400" : "border-gray-700"
                }`}
                data-oid="l9fi5qz"
              >
                {plan.popular && (
                  <div
                    className="absolute -top-4 left-1/2 transform -translate-x-1/2"
                    data-oid="9-7q59i"
                  >
                    <span
                      className="bg-gradient-to-r from-green-400 to-green-600 px-4 py-1 rounded-full text-sm font-semibold text-white"
                      data-oid="-yo76q:"
                    >
                      Most Popular
                    </span>
                  </div>
                )}

                <div className="text-center mb-6" data-oid="3p-n9sb">
                  <h3
                    className="text-2xl font-bold mb-2 text-white"
                    data-oid="xqu2osc"
                  >
                    {plan.name}
                  </h3>
                  <div
                    className="text-4xl font-bold text-green-400 mb-2"
                    data-oid="o7-9rfa"
                  >
                    {plan.price}
                  </div>
                  {plan.price !== "Free" && (
                    <div className="text-gray-400" data-oid=".5:.n66">
                      per month
                    </div>
                  )}
                </div>

                <ul className="space-y-3 mb-8" data-oid="jzfc5:y">
                  {plan.features.slice(0, 3).map((feature, featureIndex) => (
                    <li
                      key={featureIndex}
                      className="flex items-center gap-3"
                      data-oid="mpg-ld4"
                    >
                      <div
                        className="w-2 h-2 bg-green-400 rounded-full"
                        data-oid="xa2z582"
                      ></div>
                      <span className="text-gray-300" data-oid="fg3xbhb">
                        {feature}
                      </span>
                    </li>
                  ))}
                </ul>

                {plan.popular ? (
                  <PrimaryButton className="w-full">
                    Get Started
                  </PrimaryButton>
                ) : (
                  <OutlineButton className="w-full">
                    Get Started
                  </OutlineButton>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* App Cards Section */}
      <AppCards />

      {/* User Journey Flow */}
      <UserJourneyFlow />

      {/* Daily Contributions Section */}
      <section className="relative z-10 px-6 py-20" data-oid="daily-contributions">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2
              className="text-4xl md:text-5xl font-inter font-bold mb-6 text-white text-shadow"
              data-aos="fade-up"
            >
              Daily{" "}
              <span className="text-green-400">
                Contributions
              </span>
            </h2>
            <p
              className="text-xl text-gray-300 max-w-3xl mx-auto"
              data-aos="fade-up"
              data-aos-delay="200"
            >
              See how small daily contributions can build substantial wealth over time with better interest rates.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                amount: "₦1,000",
                period: "Daily",
                monthly: "₦30,000",
                yearly: "₦365,000",
                withInterest: "₦420,000",
                interestRate: "15%",
                color: "from-green-400 to-green-600"
              },
              {
                amount: "₦2,500",
                period: "Daily",
                monthly: "₦75,000",
                yearly: "₦912,500",
                withInterest: "₦1,050,000",
                interestRate: "15%",
                color: "from-blue-400 to-blue-600"
              },
              {
                amount: "₦5,000",
                period: "Daily",
                monthly: "₦150,000",
                yearly: "₦1,825,000",
                withInterest: "₦2,100,000",
                interestRate: "15%",
                color: "from-purple-400 to-purple-600"
              }
            ].map((contribution, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="glass rounded-2xl p-6 bg-gray-900/30 border border-gray-700 hover:border-green-400/50 transition-all group"
                data-aos="fade-up"
                data-aos-delay={200 + index * 100}
              >
                <div className={`w-16 h-16 rounded-full bg-gradient-to-r ${contribution.color} flex items-center justify-center mb-4 mx-auto`}>
                  <span className="text-white font-bold text-lg">₦</span>
                </div>

                <div className="text-center">
                  <h3 className="text-2xl font-bold text-white mb-2">
                    {contribution.amount}
                  </h3>
                  <p className="text-green-400 text-sm mb-4">{contribution.period} Contribution</p>

                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Monthly Total:</span>
                      <span className="text-white font-semibold">{contribution.monthly}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Yearly Total:</span>
                      <span className="text-white font-semibold">{contribution.yearly}</span>
                    </div>
                    <div className="border-t border-gray-600 pt-3">
                      <div className="flex justify-between">
                        <span className="text-gray-400">With {contribution.interestRate} Interest:</span>
                        <span className="text-green-400 font-bold">{contribution.withInterest}</span>
                      </div>
                    </div>
                  </div>

                  <div className="mt-4 pt-4 border-t border-gray-600">
                    <p className="text-xs text-gray-500">
                      Extra earnings: <span className="text-green-400 font-semibold">
                        ₦{(parseInt(contribution.withInterest.replace(/[₦,]/g, '')) - parseInt(contribution.yearly.replace(/[₦,]/g, ''))).toLocaleString()}
                      </span>
                    </p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Call to Action */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="text-center mt-12"
          >
            <p className="text-gray-300 mb-6">
              Start with any amount and watch your money grow with better interest rates
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <PrimaryButton href="/auth/signup" size="lg">
                Start Contributing Today
              </PrimaryButton>
              <OutlineButton href="/dashboard/savings-plans" size="lg">
                View Savings Plans
              </OutlineButton>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Success Gallery Section */}
      <section className="relative z-10 px-6 py-20" data-oid="success-gallery">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2
              className="text-4xl md:text-5xl font-inter font-bold mb-6 text-white text-shadow"
              data-aos="fade-up"
            >
              Success{" "}
              <span className="text-green-400">
                Stories
              </span>
            </h2>
            <p
              className="text-xl text-gray-300 max-w-3xl mx-auto"
              data-aos="fade-up"
              data-aos-delay="200"
            >
              Real people achieving real financial success with BetterInterest.
              See how our platform has transformed lives and built financial futures.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              {
                src: "/images/nneww.png",
                alt: "Professional success with BetterInterest",
                title: "Career Growth",
                description: "Building wealth through smart savings"
              },
              {
                src: "/images/nneww.png",
                alt: "Financial independence achieved",
                title: "Independence",
                description: "Achieving financial freedom goals"
              },
              {
                src: "/images/nneww.png",
                alt: "Young professional saving success",
                title: "Young Saver",
                description: "Starting early, growing faster"
              },
              {
                src: "/Celebrating with her iPhone 14 Pro.png",
                alt: "Technology meets savings",
                title: "Tech Savvy",
                description: "Modern tools for modern savers"
              }
            ].map((story, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="group flex justify-center"
                data-aos="fade-up"
                data-aos-delay={200 + index * 100}
              >
                <div className="relative mb-4">
                  <CardImage3D
                    src={story.src}
                    alt={story.alt}
                    width={280}
                    height={200}
                    intensity="medium"
                    className="w-full h-auto mx-auto"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg z-10"></div>
                  <div className="absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                    <h4 className="text-white font-semibold text-sm">{story.title}</h4>
                    <p className="text-green-400 text-xs">{story.description}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="text-center mt-12"
          >
            <p className="text-gray-300 mb-6">
              Join thousands of successful savers who trust BetterInterest
            </p>
            <PrimaryButton href="/auth/signup" size="lg">
              Start Your Success Story
            </PrimaryButton>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative z-10 px-6 py-20" data-oid="4hjv3nm">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="max-w-4xl mx-auto text-center"
          data-oid="4fi0xr0"
        >
          <div
            className="glass rounded-2xl p-12 bg-gray-900/30 border border-gray-700"
            data-aos="fade-up"
            data-oid="6dki27c"
          >
            <h2
              className="text-4xl md:text-5xl font-inter font-bold mb-6 text-white text-shadow"
              data-aos="fade-up"
              data-aos-delay="200"
              data-oid="sm64vpa"
            >
              Ready to Transform Your{" "}
              <span className="text-green-400" data-oid="1zjpnno">
                Savings?
              </span>
            </h2>
            <p
              className="text-xl text-gray-300 mb-8"
              data-aos="fade-up"
              data-aos-delay="400"
              data-oid="w70::fn"
            >
              Join thousands of users who are already building their financial
              future with BetterInterest.
            </p>
            <div
              className="flex flex-col sm:flex-row gap-4 justify-center"
              data-aos="fade-up"
              data-aos-delay="600"
              data-oid="p0uq2wf"
            >
              <PrimaryButton href="/auth/signup" size="lg">
                Start Your Journey
              </PrimaryButton>
              <OutlineButton href="/contact" size="lg">
                Contact Sales
              </OutlineButton>
            </div>
          </div>
        </motion.div>
      </section>

      {/* Footer */}
      <footer
        className="relative z-10 px-6 py-12 border-t border-gray-700"
        data-oid="lplflog"
      >
        <div className="max-w-7xl mx-auto" data-oid="znmgn33">
          <div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 mb-8"
            data-oid="izbtmj1"
          >
            <div data-oid="rmifdn1">
              <div
                className="flex items-center space-x-2 mb-4"
                data-oid=":o-3e8f"
              >
                <div
                  className="w-8 h-8 bg-gradient-to-r from-green-400 to-green-600 rounded-lg flex items-center justify-center"
                  data-oid="uihlqg7"
                >
                  <FiSave className="w-5 h-5 text-white" data-oid="8u4yvyz" />
                </div>
                <span
                  className="text-xl font-bold text-white"
                  data-oid="sxm6pl2"
                >
                  BetterInterest
                </span>
              </div>
              <p className="text-gray-400" data-oid="nexr4v9">
                Building the future of personal finance, one save at a time.
              </p>
            </div>

            <div data-oid=":jnkc1v">
              <h4
                className="font-semibold mb-4 text-green-400"
                data-oid="hio07d1"
              >
                Product
              </h4>
              <ul className="space-y-2 text-gray-400" data-oid="lfui9r0">
                <li data-oid=":_gw0d-">
                  <Link
                    href="/features"
                    className="hover:text-green-400 transition-colors"
                    data-oid="shqkequ"
                  >
                    Features
                  </Link>
                </li>
                <li data-oid="v-zbx-d">
                  <Link
                    href="/packages"
                    className="hover:text-green-400 transition-colors"
                    data-oid="yjv64nc"
                  >
                    Savings Packages
                  </Link>
                </li>
                <li data-oid="gytxmaj">
                  <Link
                    href="/auth/signup"
                    className="hover:text-green-400 transition-colors"
                    data-oid="z3tt.hs"
                  >
                    Sign Up
                  </Link>
                </li>
              </ul>
            </div>

            <div data-oid="ik_3pg.">
              <h4
                className="font-semibold mb-4 text-green-400"
                data-oid="sn7vjl8"
              >
                Company
              </h4>
              <ul className="space-y-2 text-gray-400" data-oid="5x-:v5t">
                <li data-oid="_hqgoed">
                  <Link
                    href="/about"
                    className="hover:text-green-400 transition-colors"
                    data-oid="ks0-f-h"
                  >
                    About Us
                  </Link>
                </li>
                <li data-oid="as.l2k7">
                  <Link
                    href="/contact"
                    className="hover:text-green-400 transition-colors"
                    data-oid=":-7t-or"
                  >
                    Contact
                  </Link>
                </li>
                <li data-oid=".6u.gcv">
                  <a
                    href="#"
                    className="hover:text-green-400 transition-colors"
                    data-oid="1hb4_k6"
                  >
                    Careers
                  </a>
                </li>
              </ul>
            </div>

            <div data-oid=":8thyc2">
              <h4
                className="font-semibold mb-4 text-green-400"
                data-oid="jzt8rln"
              >
                Legal
              </h4>
              <ul className="space-y-2 text-gray-400" data-oid="hu6wyzz">
                <li data-oid="0-bxvbi">
                  <Link
                    href="/privacy"
                    className="hover:text-green-400 transition-colors"
                    data-oid=":9cn-yb"
                  >
                    Privacy Policy
                  </Link>
                </li>
                <li data-oid="62.tda1">
                  <Link
                    href="/terms"
                    className="hover:text-green-400 transition-colors"
                    data-oid="6222366"
                  >
                    Terms & Conditions
                  </Link>
                </li>
                <li data-oid="6jjq8.z">
                  <a
                    href="#"
                    className="hover:text-green-400 transition-colors"
                    data-oid="_gkjlhg"
                  >
                    Help Center
                  </a>
                </li>
              </ul>
            </div>

            <div data-oid="social-media">
              <h4
                className="font-semibold mb-4 text-green-400"
                data-oid="social-title"
              >
                Follow Us
              </h4>
              <div className="flex space-x-4" data-oid="social-links">
                <a
                  href="https://www.facebook.com/profile.php?id=61578308722142"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-green-400 transition-colors transform hover:scale-110"
                  data-oid="facebook-link"
                >
                  <FaFacebook className="w-6 h-6" />
                </a>
                <a
                  href="https://x.com/KojaPay"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-green-400 transition-colors transform hover:scale-110"
                  data-oid="twitter-link"
                >
                  <FaTwitter className="w-6 h-6" />
                </a>
                <a
                  href="https://www.instagram.com/koja.io/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-green-400 transition-colors transform hover:scale-110"
                  data-oid="instagram-link"
                >
                  <FaInstagram className="w-6 h-6" />
                </a>
              </div>
            </div>
          </div>

          <div
            className="border-t border-gray-700 pt-8 text-center text-gray-400"
            data-oid="ok:qpq5"
          >
            <p data-oid="ma_wxn4">
              &copy; 2024 Koja Save. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
