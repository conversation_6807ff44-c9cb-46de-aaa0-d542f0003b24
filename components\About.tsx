"use client";

import { motion } from "framer-motion";
import {
  FiU<PERSON><PERSON>,
  <PERSON>TrendingUp,
  FiShield,
  <PERSON>Award,
  <PERSON>Heart,
  FiTarget,
} from "react-icons/fi";

const stats = [
  { icon: FiUsers, value: "50K+", label: "Active Users" },
  { icon: FiTrendingUp, value: "$2.5M+", label: "Total Saved" },
  { icon: FiShield, value: "99.9%", label: "Uptime" },
  { icon: FiAward, value: "4.9★", label: "App Rating" },
];

const values = [
  {
    icon: FiHeart,
    title: "User-Centric",
    description:
      "Every feature is designed with our users' financial wellbeing in mind.",
  },
  {
    icon: FiShield,
    title: "Security First",
    description:
      "Bank-level security ensures your money and data are always protected.",
  },
  {
    icon: FiTarget,
    title: "Goal-Oriented",
    description:
      "We help you set, track, and achieve your financial goals systematically.",
  },
];

export default function About() {
  return (
    <section
      id="about"
      className="py-20 bg-gradient-to-b from-black to-gray-900"
      data-oid="-imdzc8"
    >
      <div
        className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"
        data-oid="zm3njzz"
      >
        {/* Section Header */}
        <div
          className="text-center mb-16"
          data-aos="fade-up"
          data-oid="1dxeox4"
        >
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            data-oid="vjxan35"
          >
            <h2
              className="text-4xl md:text-5xl font-bold text-white mb-6"
              data-oid="xgm:oy7"
            >
              About{" "}
              <span className="text-gradient-animate" data-oid="al6:bky">
                Koja Save
              </span>
            </h2>
            <p
              className="text-xl text-gray-300 max-w-3xl mx-auto"
              data-oid="xanpykg"
            >
              We're on a mission to make saving money simple, automatic, and
              rewarding for everyone. Join the financial revolution that's
              helping thousands build their dream future.
            </p>
          </motion.div>
        </div>

        {/* Stats Grid */}
        <div
          className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-20"
          data-aos="fade-up"
          data-aos-delay="200"
          data-oid="nl8-67q"
        >
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.5 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.05 }}
              className="text-center"
              data-oid="p5ivnhz"
            >
              <div
                className="inline-flex p-4 rounded-2xl bg-gradient-to-r from-green-500/20 to-green-600/20 border border-green-500/30 mb-4"
                data-oid="2x.:cv:"
              >
                <stat.icon
                  className="text-green-400 text-3xl"
                  data-oid="ra2a0kt"
                />
              </div>
              <div
                className="text-3xl md:text-4xl font-bold text-white mb-2"
                data-oid="o-:m2ql"
              >
                {stat.value}
              </div>
              <div className="text-gray-400" data-oid="s2072ki">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </div>

        {/* Story Section */}
        <div
          className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-20"
          data-oid="wt0u990"
        >
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            data-aos="fade-right"
            data-oid="33js4ui"
          >
            <h3
              className="text-3xl font-bold text-white mb-6"
              data-oid="punx3rj"
            >
              Our Story
            </h3>
            <div
              className="space-y-4 text-gray-300 leading-relaxed"
              data-oid="t35.jkq"
            >
              <p data-oid="l7uksti">
                Koja Save was born from a simple observation: most people want
                to save money, but traditional methods are either too
                complicated or require too much discipline.
              </p>
              <p data-oid="xy:unzt">
                Our founders, experienced fintech professionals, realized that
                technology could bridge this gap by making savings automatic,
                intelligent, and rewarding.
              </p>
              <p data-oid="y11l0v-">
                Today, we're proud to help over 50,000 users save more than $2.5
                million collectively, proving that small, consistent actions
                lead to big results.
              </p>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            data-aos="fade-left"
            className="relative"
            data-oid="noqgw:8"
          >
            <div
              className="relative rounded-2xl overflow-hidden"
              data-oid="76rdtak"
            >
              <div
                className="aspect-video bg-gradient-to-br from-green-500/20 to-green-600/20 border border-green-500/30 rounded-2xl flex items-center justify-center"
                data-oid="td19z04"
              >
                <div className="text-center" data-oid="ajp6vho">
                  <div
                    className="w-20 h-20 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4"
                    data-oid="jks.7b7"
                  >
                    <FiTrendingUp
                      className="text-white text-3xl"
                      data-oid="t0wx49o"
                    />
                  </div>
                  <p className="text-white font-semibold" data-oid="hu5jklg">
                    Building Financial Futures
                  </p>
                  <p className="text-gray-300 text-sm" data-oid="mj9xpb_">
                    One Save at a Time
                  </p>
                </div>
              </div>
              {/* Decorative elements */}
              <div
                className="absolute -top-4 -right-4 w-8 h-8 bg-green-400/30 rounded-full blur-sm"
                data-oid="n9afu-1"
              ></div>
              <div
                className="absolute -bottom-4 -left-4 w-12 h-12 bg-green-500/20 rounded-full blur-sm"
                data-oid="dy_dx9g"
              ></div>
            </div>
          </motion.div>
        </div>

        {/* Values Section */}
        <div
          className="text-center mb-12"
          data-aos="fade-up"
          data-oid="ghus81:"
        >
          <h3 className="text-3xl font-bold text-white mb-6" data-oid="l90.pos">
            Our Values
          </h3>
          <p className="text-gray-300 max-w-2xl mx-auto" data-oid="shw8k8e">
            These core principles guide everything we do and every decision we
            make.
          </p>
        </div>

        <div
          className="grid grid-cols-1 md:grid-cols-3 gap-8"
          data-oid="8.j5roi"
        >
          {values.map((value, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
              viewport={{ once: true }}
              whileHover={{ y: -10 }}
              className="text-center group"
              data-aos="fade-up"
              data-aos-delay={index * 200}
              data-oid="6dvkcci"
            >
              <div
                className="inline-flex p-6 rounded-2xl bg-gradient-to-br from-gray-800/50 to-gray-900/50 border border-gray-700/50 group-hover:border-green-500/30 transition-all duration-300 mb-6"
                data-oid="yztos0i"
              >
                <value.icon
                  className="text-green-400 text-4xl group-hover:scale-110 transition-transform duration-300"
                  data-oid=":qsu36e"
                />
              </div>
              <h4
                className="text-xl font-semibold text-white mb-4 group-hover:text-green-400 transition-colors duration-300"
                data-oid="m3kokqm"
              >
                {value.title}
              </h4>
              <p className="text-gray-300 leading-relaxed" data-oid="xk3oix0">
                {value.description}
              </p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
