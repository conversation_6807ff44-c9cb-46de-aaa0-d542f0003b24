"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { 
  FiDollarSign, 
  FiTrendingUp, 
  FiTarget, 
  FiCalendar,
  FiUsers,
  FiPieChart,
  FiArrowUpRight,
  FiPlus,
  FiActivity
} from 'react-icons/fi';
import { useAuth } from '../../hooks/use-auth';
import { SavingsStats } from '../../types/savings';
import { savingsService } from '../../services/savings';
import UserLayout from '../../components/UserLayout';

export default function UserDashboard() {
  const { user } = useAuth();
  const [stats, setStats] = useState<SavingsStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      const statsData = await savingsService.getSavingsStats();
      setStats(statsData);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  const quickActions = [
    {
      title: 'Create Savings Plan',
      description: 'Start a new savings journey',
      icon: FiPlus,
      color: 'green',
      href: '/user/savings-plans'
    },
    {
      title: 'Join Group Savings',
      description: 'Save together with others',
      icon: FiUsers,
      color: 'blue',
      href: '/user/group-savings'
    },
    {
      title: 'View Analytics',
      description: 'Track your progress',
      icon: FiPieChart,
      color: 'purple',
      href: '/user/analytics'
    },
    {
      title: 'Make Payment',
      description: 'Fund your savings',
      icon: FiDollarSign,
      color: 'orange',
      href: '/user/payments'
    }
  ];

  if (isLoading) {
    return (
      <div className="min-h-screen bg-black text-white p-6">
        <div className="max-w-7xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-800 rounded w-64 mb-6"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-800 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <UserLayout>
      <div className="max-w-7xl mx-auto p-6">
        {/* Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold mb-2">Dashboard</h1>
          <p className="text-gray-400">Track your savings progress and manage your financial goals</p>
        </motion.div>

        {/* Error Display */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-6"
          >
            <p className="text-red-400">{error}</p>
            <button
              onClick={() => setError(null)}
              className="text-red-300 hover:text-red-200 text-sm mt-2"
            >
              Dismiss
            </button>
          </motion.div>
        )}

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-gray-900/50 border border-gray-800 rounded-lg p-6"
            >
              <div className="flex items-center justify-between mb-4">
                <FiDollarSign className="text-green-400 text-2xl" />
                <FiArrowUpRight className="text-green-400" />
              </div>
              <p className="text-2xl font-bold mb-1">{savingsService.formatCurrency(stats.totalSaved)}</p>
              <p className="text-gray-400 text-sm">Total Saved</p>
              <p className="text-green-400 text-xs mt-1">+{stats.savingsRate}% this month</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-gray-900/50 border border-gray-800 rounded-lg p-6"
            >
              <div className="flex items-center justify-between mb-4">
                <FiTrendingUp className="text-blue-400 text-2xl" />
                <FiArrowUpRight className="text-blue-400" />
              </div>
              <p className="text-2xl font-bold mb-1">{savingsService.formatCurrency(stats.totalInterestEarned)}</p>
              <p className="text-gray-400 text-sm">Interest Earned</p>
              <p className="text-blue-400 text-xs mt-1">Projected: {savingsService.formatCurrency(stats.projectedEarnings)}</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-gray-900/50 border border-gray-800 rounded-lg p-6"
            >
              <div className="flex items-center justify-between mb-4">
                <FiTarget className="text-purple-400 text-2xl" />
                <span className="text-purple-400 text-sm font-medium">Active</span>
              </div>
              <p className="text-2xl font-bold mb-1">{stats.activePlans}</p>
              <p className="text-gray-400 text-sm">Savings Plans</p>
              <p className="text-purple-400 text-xs mt-1">{stats.completedGoals} goals completed</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-gray-900/50 border border-gray-800 rounded-lg p-6"
            >
              <div className="flex items-center justify-between mb-4">
                <FiCalendar className="text-orange-400 text-2xl" />
                <FiActivity className="text-orange-400" />
              </div>
              <p className="text-2xl font-bold mb-1">{savingsService.formatCurrency(stats.monthlyContributions)}</p>
              <p className="text-gray-400 text-sm">Monthly Contributions</p>
              <p className="text-orange-400 text-xs mt-1">{stats.goalCompletionRate}% completion rate</p>
            </motion.div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="mb-8">
          <h2 className="text-xl font-bold mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action, index) => (
              <motion.div
                key={action.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Link
                  href={action.href}
                  className={`block bg-gray-900/50 border border-gray-800 rounded-lg p-6 hover:border-${action.color}-500/30 transition-colors group`}
                >
                  <div className="flex items-center justify-between mb-4">
                    <action.icon className={`text-${action.color}-400 text-2xl group-hover:scale-110 transition-transform`} />
                    <FiArrowUpRight className={`text-${action.color}-400 opacity-0 group-hover:opacity-100 transition-opacity`} />
                  </div>
                  <h3 className="font-semibold mb-2">{action.title}</h3>
                  <p className="text-gray-400 text-sm">{action.description}</p>
                </Link>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="bg-gray-900/50 border border-gray-800 rounded-lg p-6"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Recent Savings</h3>
              <Link href="/user/savings-plans" className="text-green-400 hover:text-green-300 text-sm">
                View All
              </Link>
            </div>
            <div className="space-y-4">
              {/* Placeholder for recent savings */}
              <div className="text-center py-8">
                <FiTarget className="text-4xl text-gray-600 mx-auto mb-2" />
                <p className="text-gray-400">No recent savings activity</p>
                <Link
                  href="/user/savings-plans"
                  className="text-green-400 hover:text-green-300 text-sm mt-2 inline-block"
                >
                  Create your first savings plan
                </Link>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="bg-gray-900/50 border border-gray-800 rounded-lg p-6"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Goals Progress</h3>
              <Link href="/user/goals" className="text-purple-400 hover:text-purple-300 text-sm">
                View All
              </Link>
            </div>
            <div className="space-y-4">
              {/* Placeholder for goals progress */}
              <div className="text-center py-8">
                <FiTarget className="text-4xl text-gray-600 mx-auto mb-2" />
                <p className="text-gray-400">No active goals</p>
                <Link
                  href="/user/savings-plans"
                  className="text-purple-400 hover:text-purple-300 text-sm mt-2 inline-block"
                >
                  Set your first goal
                </Link>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </UserLayout>
  );
}
