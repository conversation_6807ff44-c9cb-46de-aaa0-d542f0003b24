"use client";

import { useRouter } from "next/navigation";
import React, { ReactNode, useEffect } from "react";
import LoadingLogo from "../../../components/LoadingLogo";
import { useAuth } from "../../hooks/use-auth";

interface PrivateRouteProps {
  children: ReactNode;
  requiredRole?: "USER" | "ADMIN";
  redirectTo?: string;
}

export default function PrivateRoute({
  children,
  requiredRole,
  redirectTo = "/auth/login",
}: PrivateRouteProps) {
  const { isAuthenticated, isLoading, user } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading) {
      if (!isAuthenticated) {
        router.push(redirectTo);
        return;
      }

      if (requiredRole && user?.role !== requiredRole) {
        // Redirect based on user role
        if (user?.role === "ADMIN") {
          router.push("/admin/dashboard");
        } else {
          router.push("/dashboard");
        }
        return;
      }
    }
  }, [isAuthenticated, isLoading, user, requiredRole, router, redirectTo]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <LoadingLogo size={64} showPulse={true} />
          <p className="text-green-400 mt-4 text-lg">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect in useEffect
  }

  if (requiredRole && user?.role !== requiredRole) {
    return null; // Will redirect in useEffect
  }

  return <>{children}</>;
}

// Higher-order component for protecting routes
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  requiredRole?: 'USER' | 'ADMIN'
) {
  return function AuthenticatedComponent(props: P) {
    return (
      <PrivateRoute requiredRole={requiredRole}>
        <Component {...props} />
      </PrivateRoute>
    );
  };
}

// Role-based route protection components
export function UserRoute({ children }: { children: ReactNode }) {
  return (
    <PrivateRoute requiredRole="USER" redirectTo="/login">
      {children}
    </PrivateRoute>
  );
}

export function AdminRoute({ children }: { children: ReactNode }) {
  return (
    <PrivateRoute requiredRole="ADMIN" redirectTo="/admin/login">
      {children}
    </PrivateRoute>
  );
}
