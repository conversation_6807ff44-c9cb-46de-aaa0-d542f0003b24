"use client";

import React from 'react';

interface KojaSaveIconProps {
  size?: number;
  className?: string;
}

export const KojaSaveIcon: React.FC<KojaSaveIconProps> = ({ 
  size = 24, 
  className = "" 
}) => {
  return (
    <div 
      className={`relative flex items-center justify-center ${className}`}
      style={{ width: size, height: size }}
    >
      <svg
        width={size}
        height={size}
        viewBox="0 0 100 100"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="drop-shadow-lg"
      >
        {/* Briefcase Handle */}
        <path
          d="M30 20 C30 15, 35 10, 40 10 L60 10 C65 10, 70 15, 70 20 L70 25 L30 25 Z"
          fill="currentColor"
          className="text-green-400"
          stroke="currentColor"
          strokeWidth="3"
          strokeLinejoin="round"
        />
        
        {/* Main Briefcase Body */}
        <rect
          x="15"
          y="25"
          width="70"
          height="50"
          rx="8"
          ry="8"
          fill="currentColor"
          className="text-green-400"
          stroke="currentColor"
          strokeWidth="3"
          strokeLinejoin="round"
        />
        
        {/* Briefcase Lock/Latch */}
        <rect
          x="45"
          y="20"
          width="10"
          height="8"
          rx="2"
          fill="currentColor"
          className="text-green-400"
        />
        
        {/* Inner Money/Document Area */}
        <rect
          x="22"
          y="32"
          width="56"
          height="36"
          rx="4"
          fill="rgba(255, 255, 255, 0.9)"
          stroke="currentColor"
          className="text-green-500"
          strokeWidth="2"
        />
        
        {/* Naira Symbol (₦) */}
        <g className="text-green-600" fill="currentColor">
          {/* Vertical lines of N */}
          <rect x="35" y="40" width="3" height="20" />
          <rect x="62" y="40" width="3" height="20" />
          
          {/* Diagonal line of N */}
          <path d="M35 58 L65 42" stroke="currentColor" strokeWidth="3" strokeLinecap="round" />
          
          {/* Horizontal lines for currency symbol */}
          <rect x="30" y="45" width="40" height="2" />
          <rect x="30" y="53" width="40" height="2" />
        </g>
        
        {/* Briefcase Side Details */}
        <circle cx="25" cy="50" r="2" fill="currentColor" className="text-green-500" />
        <circle cx="75" cy="50" r="2" fill="currentColor" className="text-green-500" />
        
        {/* Handle Grip Details */}
        <rect x="47" y="12" width="1" height="6" fill="rgba(255, 255, 255, 0.6)" />
        <rect x="49" y="12" width="1" height="6" fill="rgba(255, 255, 255, 0.6)" />
        <rect x="51" y="12" width="1" height="6" fill="rgba(255, 255, 255, 0.6)" />
      </svg>
    </div>
  );
};

export default KojaSaveIcon;
