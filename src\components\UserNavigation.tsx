"use client";

import { motion } from 'framer-motion';
import <PERSON> from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useState } from "react";
import {
  FiBell,
  FiDollarSign,
  FiHome,
  FiLogOut,
  FiMenu,
  FiPieChart,
  FiSettings,
  FiTarget,
  FiUser,
  FiUsers,
  FiX,
} from "react-icons/fi";
import KojaSaveLogo from "../../components/KojaSaveLogo";
import { useAuth } from "../hooks/use-auth";

const navigationItems = [
  { name: "Dashboard", href: "/dashboard", icon: FiHome },
  { name: "Savings Plans", href: "/dashboard/savings-plans", icon: FiTarget },
  { name: "Group Savings", href: "/dashboard/group-savings", icon: FiUsers },
  { name: "Payments", href: "/dashboard/payments", icon: FiDollarSign },
  { name: "Analytics", href: "/dashboard/analytics", icon: <PERSON><PERSON><PERSON><PERSON><PERSON> },
  { name: "Notifications", href: "/dashboard/notifications", icon: <PERSON>Bell },
  { name: "Settings", href: "/dashboard/settings", icon: FiSettings },
];

export default function UserNavigation() {
  const { user, logout } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const handleLogout = async () => {
    await logout();
    router.push("/");
  };

  const isActive = (href: string) => pathname === href;

  return (
    <>
      {/* Desktop Sidebar */}
      <div className="hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0 lg:bg-gray-900 lg:border-r lg:border-gray-800">
        <div className="flex flex-col flex-grow pt-5 pb-4 overflow-y-auto">
          {/* Logo */}
          <div className="flex items-center flex-shrink-0 px-4 mb-8">
            <KojaSaveLogo size={32} showText={true} />
          </div>

          {/* Navigation */}
          <nav className="mt-5 flex-1 px-2 space-y-1">
            {navigationItems.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${
                  isActive(item.href)
                    ? "bg-green-600 text-white"
                    : "text-gray-300 hover:bg-gray-800 hover:text-white"
                }`}
              >
                <item.icon
                  className={`mr-3 flex-shrink-0 h-5 w-5 ${
                    isActive(item.href)
                      ? "text-white"
                      : "text-gray-400 group-hover:text-white"
                  }`}
                />
                {item.name}
              </Link>
            ))}
          </nav>

          {/* User Profile */}
          <div className="flex-shrink-0 flex border-t border-gray-800 p-4">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-semibold">
                  {user?.firstName?.charAt(0)}
                  {user?.lastName?.charAt(0)}
                </span>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-white">
                  {user?.firstName} {user?.lastName}
                </p>
                <p className="text-xs text-gray-400">{user?.email}</p>
              </div>
            </div>
          </div>

          {/* Logout */}
          <div className="flex-shrink-0 px-2 pb-4">
            <button
              onClick={handleLogout}
              className="group flex items-center w-full px-2 py-2 text-sm font-medium text-gray-300 rounded-md hover:bg-red-600 hover:text-white transition-colors"
            >
              <FiLogOut className="mr-3 flex-shrink-0 h-5 w-5 text-gray-400 group-hover:text-white" />
              Logout
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      <div className="lg:hidden">
        {/* Mobile Header */}
        <div className="bg-gray-900 border-b border-gray-800 px-4 py-3 flex items-center justify-between">
          <KojaSaveLogo size={28} showText={true} />
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="text-gray-400 hover:text-white"
          >
            {isMobileMenuOpen ? <FiX size={24} /> : <FiMenu size={24} />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="lg:hidden bg-gray-900 border-b border-gray-800"
          >
            <div className="px-2 pt-2 pb-3 space-y-1">
              {navigationItems.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  onClick={() => setIsMobileMenuOpen(false)}
                  className={`group flex items-center px-3 py-2 text-base font-medium rounded-md transition-colors ${
                    isActive(item.href)
                      ? "bg-green-600 text-white"
                      : "text-gray-300 hover:bg-gray-800 hover:text-white"
                  }`}
                >
                  <item.icon
                    className={`mr-3 flex-shrink-0 h-5 w-5 ${
                      isActive(item.href)
                        ? "text-white"
                        : "text-gray-400 group-hover:text-white"
                    }`}
                  />
                  {item.name}
                </Link>
              ))}
            </div>

            {/* Mobile User Profile */}
            <div className="pt-4 pb-3 border-t border-gray-800">
              <div className="flex items-center px-5">
                <div className="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-semibold">
                    {user?.firstName?.charAt(0)}
                    {user?.lastName?.charAt(0)}
                  </span>
                </div>
                <div className="ml-3">
                  <div className="text-base font-medium text-white">
                    {user?.firstName} {user?.lastName}
                  </div>
                  <div className="text-sm text-gray-400">{user?.email}</div>
                </div>
              </div>
              <div className="mt-3 px-2 space-y-1">
                <Link
                  href="/dashboard/profile"
                  onClick={() => setIsMobileMenuOpen(false)}
                  className="group flex items-center px-3 py-2 text-base font-medium text-gray-300 rounded-md hover:bg-gray-800 hover:text-white transition-colors"
                >
                  <FiUser className="mr-3 flex-shrink-0 h-5 w-5 text-gray-400 group-hover:text-white" />
                  Profile
                </Link>
                <button
                  onClick={() => {
                    setIsMobileMenuOpen(false);
                    handleLogout();
                  }}
                  className="group flex items-center w-full px-3 py-2 text-base font-medium text-gray-300 rounded-md hover:bg-red-600 hover:text-white transition-colors"
                >
                  <FiLogOut className="mr-3 flex-shrink-0 h-5 w-5 text-gray-400 group-hover:text-white" />
                  Logout
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </>
  );
}
