export interface SavingsPlan {
  id: string;
  userId: string;
  name: string;
  description?: string;
  planType: 'INDIVIDUAL' | 'TARGET' | 'GOAL';
  targetAmount: number;
  currentAmount: number;
  interestRate: number;
  duration: number; // in months
  frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY';
  startDate: string;
  endDate: string;
  status: 'ACTIVE' | 'PAUSED' | 'COMPLETED' | 'CANCELLED';
  autoDebit: boolean;
  nextContributionDate?: string;
  contributionAmount: number;
  totalContributions: number;
  interestEarned: number;
  createdAt: string;
  updatedAt: string;
}

export interface SavingsGoal {
  id: string;
  userId: string;
  title: string;
  description: string;
  targetAmount: number;
  currentAmount: number;
  targetDate: string;
  category: 'EMERGENCY' | 'VACATION' | 'EDUCATION' | 'HOUSE' | 'CAR' | 'BUSINESS' | 'OTHER';
  priority: 'LOW' | 'MEDIUM' | 'HIGH';
  status: 'ACTIVE' | 'COMPLETED' | 'PAUSED';
  imageUrl?: string;
  milestones: SavingsMilestone[];
  createdAt: string;
  updatedAt: string;
}

export interface SavingsMilestone {
  id: string;
  goalId: string;
  title: string;
  targetAmount: number;
  isCompleted: boolean;
  completedAt?: string;
  reward?: string;
}

export interface SavingsTransaction {
  id: string;
  planId?: string;
  goalId?: string;
  userId: string;
  type: 'DEPOSIT' | 'WITHDRAWAL' | 'INTEREST' | 'PENALTY';
  amount: number;
  description: string;
  status: 'PENDING' | 'COMPLETED' | 'FAILED';
  reference: string;
  paymentMethod?: string;
  createdAt: string;
  processedAt?: string;
}

export interface CreateSavingsPlanData {
  name: string;
  description?: string;
  planType: 'INDIVIDUAL' | 'TARGET' | 'GOAL';
  targetAmount: number;
  contributionAmount: number;
  frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY';
  duration: number;
  autoDebit: boolean;
  startDate: string;
}

export interface CreateSavingsGoalData {
  title: string;
  description: string;
  targetAmount: number;
  targetDate: string;
  category: 'EMERGENCY' | 'VACATION' | 'EDUCATION' | 'HOUSE' | 'CAR' | 'BUSINESS' | 'OTHER';
  priority: 'LOW' | 'MEDIUM' | 'HIGH';
  imageUrl?: string;
  milestones?: Omit<SavingsMilestone, 'id' | 'goalId' | 'isCompleted' | 'completedAt'>[];
}

export interface SavingsStats {
  totalSaved: number;
  totalInterestEarned: number;
  activePlans: number;
  completedGoals: number;
  monthlyContributions: number;
  projectedEarnings: number;
  savingsRate: number;
  goalCompletionRate: number;
}

export interface InterestCalculation {
  principal: number;
  rate: number;
  time: number;
  frequency: number;
  compoundInterest: number;
  totalAmount: number;
  monthlyBreakdown: MonthlyBreakdown[];
}

export interface MonthlyBreakdown {
  month: number;
  contribution: number;
  interest: number;
  balance: number;
}

export interface SavingsPlanFormData {
  name: string;
  description: string;
  planType: 'INDIVIDUAL' | 'TARGET' | 'GOAL';
  targetAmount: string;
  contributionAmount: string;
  frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY';
  duration: string;
  autoDebit: boolean;
  startDate: string;
}

export interface SavingsGoalFormData {
  title: string;
  description: string;
  targetAmount: string;
  targetDate: string;
  category: 'EMERGENCY' | 'VACATION' | 'EDUCATION' | 'HOUSE' | 'CAR' | 'BUSINESS' | 'OTHER';
  priority: 'LOW' | 'MEDIUM' | 'HIGH';
  imageUrl: string;
}
