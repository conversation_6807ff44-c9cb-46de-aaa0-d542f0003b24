const express = require('express');
const router = express.Router();
const axios = require('axios');
const User = require('../models/user');
const KYC = require('../models/kyc');

// POST /api/kyc/verify-nin
const { authenticateToken } = require('../middleware/authMiddleware');

// Save KYC result to user after verification
router.post('/verify-nin', authenticateToken, async (req, res) => {
  try {
    const { fullName, phoneNumber, ninOrBvn } = req.body;
    if (!fullName || !phoneNumber || !ninOrBvn) {
      return res.status(400).json({ error: 'All fields are required.' });
    }

    // Call Dojah API for NIN/BVN verification
    const DOJAH_APP_ID = process.env.DOJAH_APP_ID;
    const DOJAH_SECRET_KEY = process.env.DOJAH_SECRET_KEY;
    if (!DOJAH_APP_ID || !DOJAH_SECRET_KEY) {
      return res.status(500).json({ error: 'Dojah API keys not set in environment.' });
    }

    // Determine if input is NIN or BVN
    const isNIN = ninOrBvn.length === 11 && ninOrBvn.match(/^\d+$/);
    const endpoint = isNIN
      ? 'https://api.dojah.io/api/v1/kyc/nin'
      : 'https://api.dojah.io/api/v1/kyc/bvn';
    const payload = isNIN
      ? { nin: ninOrBvn, phone: phoneNumber }
      : { bvn: ninOrBvn, phone: phoneNumber };

    const dojahRes = await axios.post(endpoint, payload, {
      headers: {
        'AppId': DOJAH_APP_ID,
        'Authorization': `Bearer ${DOJAH_SECRET_KEY}`,
        'Content-Type': 'application/json',
      },
    });

    const result = dojahRes.data;
    if (!result || !result.status || result.status !== 'success') {
      return res.status(400).json({ error: 'Verification failed', details: result });
    }

    // Save/update KYC status for user
    const userId = req.user && req.user._id ? req.user._id : null;
    if (!userId) {
      return res.status(401).json({ error: 'User authentication failed.' });
    }

    // Determine KYC type
    const kycType = isNIN ? 'NIN' : 'BVN';
    // Update user record
    await User.findByIdAndUpdate(userId, {
      kycStatus: 'APPROVED',
      kycType,
      kycResult: result,
    });

    res.status(200).json({ message: 'KYC verification successful', kycResult: result });
  } catch (error) {
    console.error('Dojah KYC error:', error.response?.data || error.message);
    res.status(500).json({ error: 'Dojah KYC verification failed', details: error.response?.data || error.message });
  }
});

module.exports = router;
