"use client";

import React from 'react';
import { motion } from 'framer-motion';

interface SwitchProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
  size?: 'sm' | 'md' | 'lg';
  color?: 'green' | 'blue' | 'purple' | 'red';
  label?: string;
  description?: string;
  className?: string;
}

export function Switch({
  checked,
  onChange,
  disabled = false,
  size = 'md',
  color = 'green',
  label,
  description,
  className = ''
}: SwitchProps) {
  const sizeClasses = {
    sm: {
      switch: 'w-8 h-4',
      thumb: 'w-3 h-3',
      translate: 'translate-x-4'
    },
    md: {
      switch: 'w-11 h-6',
      thumb: 'w-5 h-5',
      translate: 'translate-x-5'
    },
    lg: {
      switch: 'w-14 h-8',
      thumb: 'w-7 h-7',
      translate: 'translate-x-6'
    }
  };

  const colorClasses = {
    green: 'bg-green-600',
    blue: 'bg-blue-600',
    purple: 'bg-purple-600',
    red: 'bg-red-600'
  };

  const currentSize = sizeClasses[size];
  const currentColor = colorClasses[color];

  const handleClick = () => {
    if (!disabled) {
      onChange(!checked);
    }
  };

  const SwitchComponent = (
    <button
      type="button"
      role="switch"
      aria-checked={checked}
      disabled={disabled}
      onClick={handleClick}
      className={`
        relative inline-flex items-center rounded-full transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-green-500
        ${checked ? currentColor : 'bg-gray-600'}
        ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        ${currentSize.switch}
        ${className}
      `}
    >
      <motion.span
        className={`
          inline-block rounded-full bg-white shadow-lg transform transition-transform duration-200 ease-in-out
          ${currentSize.thumb}
        `}
        animate={{
          x: checked ? currentSize.translate.replace('translate-x-', '') : '0'
        }}
        transition={{
          type: "spring",
          stiffness: 500,
          damping: 30
        }}
      />
    </button>
  );

  if (label || description) {
    return (
      <div className="flex items-start space-x-3">
        {SwitchComponent}
        <div className="flex-1">
          {label && (
            <label className="text-sm font-medium text-white cursor-pointer" onClick={handleClick}>
              {label}
            </label>
          )}
          {description && (
            <p className="text-sm text-gray-400">{description}</p>
          )}
        </div>
      </div>
    );
  }

  return SwitchComponent;
}
