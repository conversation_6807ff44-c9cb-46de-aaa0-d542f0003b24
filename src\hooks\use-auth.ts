"use client";

import { useState, useEffect } from 'react';

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: 'user' | 'admin';
  planType?: string;
  totalSavings?: number;
  kycStatus?: 'pending' | 'approved' | 'rejected';
  createdAt?: string;
  intercomHash?: string;
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

export function useAuth(): AuthState {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true,
  });

  useEffect(() => {
    // Simulate checking for existing auth token
    const checkAuth = () => {
      try {
        const token = localStorage.getItem('auth_token');
        const userData = localStorage.getItem('user_data');
        
        if (token && userData) {
          const user = JSON.parse(userData);
          setAuthState({
            user,
            isAuthenticated: true,
            isLoading: false,
          });
        } else {
          setAuthState({
            user: null,
            isAuthenticated: false,
            isLoading: false,
          });
        }
      } catch (error) {
        console.error('Auth check failed:', error);
        setAuthState({
          user: null,
          isAuthenticated: false,
          isLoading: false,
        });
      }
    };

    // Add a small delay to prevent hydration issues
    const timer = setTimeout(checkAuth, 100);
    
    return () => clearTimeout(timer);
  }, []);

  return authState;
}

// Mock login function
export const login = async (email: string, password: string): Promise<User> => {
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  const mockUser: User = {
    id: '1',
    firstName: 'John',
    lastName: 'Doe',
    email,
    role: 'user',
    planType: 'premium',
    totalSavings: 150000,
    kycStatus: 'approved',
    createdAt: new Date().toISOString(),
  };

  // Store in localStorage
  localStorage.setItem('auth_token', 'mock_token_123');
  localStorage.setItem('user_data', JSON.stringify(mockUser));
  
  return mockUser;
};

// Mock logout function
export const logout = (): void => {
  localStorage.removeItem('auth_token');
  localStorage.removeItem('user_data');
  window.location.href = '/';
};
