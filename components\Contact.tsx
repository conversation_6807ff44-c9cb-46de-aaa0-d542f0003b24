"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { Button } from "./ui/button";
import {
  FiMail,
  FiPhone,
  FiMapPin,
  FiSend,
  FiMessageCircle,
  FiClock,
} from "react-icons/fi";

export default function Contact() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: "",
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission here
    console.log("Form submitted:", formData);
    // Reset form
    setFormData({ name: "", email: "", subject: "", message: "" });
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  return (
    <section
      id="contact"
      className="py-20 bg-gradient-to-b from-gray-900 to-black"
      data-oid="me525ui"
    >
      <div
        className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"
        data-oid="mb7-yde"
      >
        {/* Section Header */}
        <div
          className="text-center mb-16"
          data-aos="fade-up"
          data-oid="vw3ddvz"
        >
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            data-oid="miufj7f"
          >
            <h2
              className="text-4xl md:text-5xl font-bold text-white mb-6"
              data-oid="bygs.6e"
            >
              Get in{" "}
              <span className="text-gradient-animate" data-oid="tfh75.r">
                Touch
              </span>
            </h2>
            <p
              className="text-xl text-gray-300 max-w-3xl mx-auto"
              data-oid="bacfdr7"
            >
              Have questions about Koja Save? We're here to help you start your
              savings journey. Reach out to our friendly team anytime.
            </p>
          </motion.div>
        </div>

        <div
          className="grid grid-cols-1 lg:grid-cols-2 gap-12"
          data-oid="1iesws7"
        >
          {/* Contact Information */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            data-aos="fade-right"
            data-oid="1xa8xvw"
          >
            <div className="space-y-8" data-oid="57nqsxh">
              <div data-oid="d5sry-h">
                <h3
                  className="text-2xl font-semibold text-white mb-6"
                  data-oid="_6490e3"
                >
                  Let's Start a Conversation
                </h3>
                <p
                  className="text-gray-300 text-lg leading-relaxed"
                  data-oid=".91_n:9"
                >
                  Whether you're curious about features, pricing, or need
                  support with your account, our team is ready to answer all
                  your questions.
                </p>
              </div>

              {/* Contact Methods */}
              <div className="space-y-6" data-oid="qhu.eg0">
                <motion.div
                  className="flex items-center space-x-4 p-4 rounded-xl bg-gradient-to-r from-gray-800/50 to-gray-900/50 border border-gray-700/50 hover:border-green-500/30 transition-all duration-300"
                  whileHover={{ scale: 1.02 }}
                  data-oid="cj.6crf"
                >
                  <div
                    className="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center"
                    data-oid="ko8ttim"
                  >
                    <FiMail className="text-white text-xl" data-oid="o4r2qpm" />
                  </div>
                  <div data-oid="g:6-e7o">
                    <h4 className="text-white font-semibold" data-oid="oo:asu.">
                      Email Us
                    </h4>
                    <p className="text-gray-300" data-oid="9f_myb0">
                      <EMAIL>
                    </p>
                  </div>
                </motion.div>

                <motion.div
                  className="flex items-center space-x-4 p-4 rounded-xl bg-gradient-to-r from-gray-800/50 to-gray-900/50 border border-gray-700/50 hover:border-green-500/30 transition-all duration-300"
                  whileHover={{ scale: 1.02 }}
                  data-oid="npj:d:8"
                >
                  <div
                    className="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center"
                    data-oid="r26kz.g"
                  >
                    <FiPhone
                      className="text-white text-xl"
                      data-oid="gwh42n8"
                    />
                  </div>
                  <div data-oid="rf2_375">
                    <h4 className="text-white font-semibold" data-oid="-.it509">
                      Call Us
                    </h4>
                    <p className="text-gray-300" data-oid="n16_ffs">
                      +****************
                    </p>
                  </div>
                </motion.div>

                <motion.div
                  className="flex items-center space-x-4 p-4 rounded-xl bg-gradient-to-r from-gray-800/50 to-gray-900/50 border border-gray-700/50 hover:border-green-500/30 transition-all duration-300"
                  whileHover={{ scale: 1.02 }}
                  data-oid="k7.f:p."
                >
                  <div
                    className="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center"
                    data-oid="_fw1x04"
                  >
                    <FiMapPin
                      className="text-white text-xl"
                      data-oid="6ggg_yu"
                    />
                  </div>
                  <div data-oid="ypd5uwd">
                    <h4 className="text-white font-semibold" data-oid="oy_a0di">
                      Visit Us
                    </h4>
                    <p className="text-gray-300" data-oid="c219a.b">
                      123 Finance St, Tech City, TC 12345
                    </p>
                  </div>
                </motion.div>

                <motion.div
                  className="flex items-center space-x-4 p-4 rounded-xl bg-gradient-to-r from-gray-800/50 to-gray-900/50 border border-gray-700/50 hover:border-green-500/30 transition-all duration-300"
                  whileHover={{ scale: 1.02 }}
                  data-oid="31y1the"
                >
                  <div
                    className="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg flex items-center justify-center"
                    data-oid="1m7f2ij"
                  >
                    <FiClock
                      className="text-white text-xl"
                      data-oid="4.848l."
                    />
                  </div>
                  <div data-oid="w7pryff">
                    <h4 className="text-white font-semibold" data-oid="5:5olpf">
                      Support Hours
                    </h4>
                    <p className="text-gray-300" data-oid="mv3fo_x">
                      Mon-Fri: 9AM-6PM EST
                    </p>
                  </div>
                </motion.div>
              </div>
            </div>
          </motion.div>

          {/* Contact Form */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            data-aos="fade-left"
            data-oid=":6vzda4"
          >
            <div
              className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 border border-gray-700/50 rounded-2xl p-8 backdrop-blur-sm"
              data-oid="c.4tsq5"
            >
              <div
                className="flex items-center space-x-3 mb-6"
                data-oid="svtpedb"
              >
                <FiMessageCircle
                  className="text-green-400 text-2xl"
                  data-oid="kbtsixe"
                />
                <h3
                  className="text-2xl font-semibold text-white"
                  data-oid="ydij69p"
                >
                  Send us a Message
                </h3>
              </div>

              <form
                onSubmit={handleSubmit}
                className="space-y-6"
                data-oid="1l1ubbt"
              >
                <div
                  className="grid grid-cols-1 md:grid-cols-2 gap-6"
                  data-oid="qotgv3o"
                >
                  <div data-oid="3prjbtw">
                    <label
                      htmlFor="name"
                      className="block text-sm font-medium text-gray-300 mb-2"
                      data-oid="u1wmijk"
                    >
                      Full Name
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300"
                      placeholder="John Doe"
                      data-oid="vq_7tcv"
                    />
                  </div>
                  <div data-oid="gaoqge4">
                    <label
                      htmlFor="email"
                      className="block text-sm font-medium text-gray-300 mb-2"
                      data-oid="5_::vms"
                    >
                      Email Address
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300"
                      placeholder="<EMAIL>"
                      data-oid="v4hvks8"
                    />
                  </div>
                </div>

                <div data-oid="rhennhr">
                  <label
                    htmlFor="subject"
                    className="block text-sm font-medium text-gray-300 mb-2"
                    data-oid="6zfurep"
                  >
                    Subject
                  </label>
                  <input
                    type="text"
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300"
                    placeholder="How can we help you?"
                    data-oid="9l7kxrx"
                  />
                </div>

                <div data-oid="k6-i33g">
                  <label
                    htmlFor="message"
                    className="block text-sm font-medium text-gray-300 mb-2"
                    data-oid="w_r_wma"
                  >
                    Message
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    required
                    rows={5}
                    className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 resize-none"
                    placeholder="Tell us more about your inquiry..."
                    data-oid="bbhjdu6"
                  />
                </div>

                <Button
                  type="submit"
                  variant="primary"
                  size="lg"
                  className="w-full group"
                  data-oid="hfp.agm"
                >
                  Send Message
                  <FiSend
                    className="ml-2 group-hover:translate-x-1 transition-transform duration-300"
                    data-oid="cod35mk"
                  />
                </Button>
              </form>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
