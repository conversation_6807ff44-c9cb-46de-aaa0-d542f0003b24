import { 
  Withdrawal, 
  InitiateWithdrawalData,
  InitiateWithdrawalResponse,
  PlanClosureData,
  PlanClosureResponse,
  ApproveWithdrawalData,
  RejectWithdrawalData,
  WithdrawalStats,
  PenaltySummary,
  WithdrawalSearchFilters,
  PaginatedWithdrawalResponse,
  PaystackPayoutData,
  ApiResponse
} from '../types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL ;
class WithdrawalsService {
  private getAuthHeaders() {
    const token = localStorage.getItem('auth_token');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` })
    };
  }

  // Withdrawal Initiation
  async initiateWithdrawal(data: InitiateWithdrawalData): Promise<InitiateWithdrawalResponse> {
    const response = await fetch(`${API_BASE_URL}/withdraw/initiate`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to initiate withdrawal');
    }

    return response.json();
  }

  async initiatePlanClosure(data: PlanClosureData): Promise<PlanClosureResponse> {
    const response = await fetch(`${API_BASE_URL}/api/withdraw/plan`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to close plan');
    }

    return response.json();
  }

  // Withdrawal Management
  async getUserWithdrawals(filters?: WithdrawalSearchFilters): Promise<PaginatedWithdrawalResponse> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/withdraw/user/${localStorage.getItem('user_id')}?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch user withdrawals');
    }

    return response.json();
  }

  async getWithdrawalById(withdrawalId: string): Promise<Withdrawal> {
    const response = await fetch(`${API_BASE_URL}/withdraw/${withdrawalId}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch withdrawal');
    }

    return response.json();
  }

  async cancelWithdrawal(withdrawalId: string): Promise<Withdrawal> {
    const response = await fetch(`${API_BASE_URL}/withdraw/${withdrawalId}/cancel`, {
      method: 'POST',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to cancel withdrawal');
    }

    return response.json();
  }

  // Admin Withdrawal Management
  async getAllWithdrawals(filters?: WithdrawalSearchFilters): Promise<PaginatedWithdrawalResponse> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/withdraw/all?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch all withdrawals');
    }

    return response.json();
  }

  async approveWithdrawal(data: ApproveWithdrawalData): Promise<Withdrawal> {
    const response = await fetch(`${API_BASE_URL}/withdraw/${data.withdrawalId}`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({
        status: 'APPROVED',
        adminNotes: data.adminNotes,
        adjustedAmount: data.adjustedAmount
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to approve withdrawal');
    }

    return response.json();
  }

  async rejectWithdrawal(data: RejectWithdrawalData): Promise<Withdrawal> {
    const response = await fetch(`${API_BASE_URL}/withdraw/${data.withdrawalId}`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({
        status: 'REJECTED',
        rejectionReason: data.rejectionReason,
        adminNotes: data.adminNotes
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to reject withdrawal');
    }

    return response.json();
  }

  async updateWithdrawalStatus(
    withdrawalId: string, 
    status: 'PENDING' | 'PROCESSING' | 'APPROVED' | 'COMPLETED' | 'FAILED' | 'CANCELLED' | 'REJECTED',
    notes?: string
  ): Promise<Withdrawal> {
    const response = await fetch(`${API_BASE_URL}/withdraw/${withdrawalId}/status`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ status, notes })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update withdrawal status');
    }

    return response.json();
  }

  // Statistics and Analytics
  async getWithdrawalStats(filters?: {
    dateFrom?: string;
    dateTo?: string;
    userId?: string;
    type?: string;
  }): Promise<WithdrawalStats> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/withdraw/stats?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch withdrawal statistics');
    }

    return response.json();
  }

  async getPenaltySummary(filters?: {
    dateFrom?: string;
    dateTo?: string;
    userId?: string;
  }): Promise<PenaltySummary> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/withdraw/penalty-summary?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch penalty summary');
    }

    return response.json();
  }

  // Webhook Handling (for Paystack payouts)
  async handlePaystackWebhook(webhookData: any): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/withdraw/webhook`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(webhookData)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to handle webhook');
    }
  }

  // Utility Methods
  calculatePenalty(
    amount: number, 
    planType: string, 
    daysEarly: number, 
    penaltyRate: number = 5
  ): { penaltyAmount: number; netAmount: number; reason: string } {
    let penalty = 0;
    let reason = '';

    if (daysEarly > 0) {
      // Early withdrawal penalty
      penalty = (amount * penaltyRate) / 100;
      reason = `Early withdrawal penalty (${daysEarly} days before maturity)`;
    }

    return {
      penaltyAmount: Math.round(penalty),
      netAmount: amount - Math.round(penalty),
      reason
    };
  }

  calculateWithdrawalFees(amount: number, type: string): { fees: number; netAmount: number } {
    // Default fee structure - should be configurable
    const feeRates: Record<string, { percentage: number; fixed: number; cap?: number }> = {
      PLAN_CLOSURE: { percentage: 0, fixed: 0 }, // No fees for plan closure
      EXTERNAL_PAYOUT: { percentage: 1.0, fixed: 100, cap: 1000 }
    };

    const feeStructure = feeRates[type] || feeRates.EXTERNAL_PAYOUT;
    let fees = (amount * feeStructure.percentage / 100) + feeStructure.fixed;
    
    if (feeStructure.cap && fees > feeStructure.cap) {
      fees = feeStructure.cap;
    }

    return {
      fees: Math.round(fees),
      netAmount: amount - Math.round(fees)
    };
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN'
    }).format(amount);
  }

  getStatusColor(status: string): string {
    const colors: Record<string, string> = {
      PENDING: '#F59E0B',
      PROCESSING: '#3B82F6',
      APPROVED: '#10B981',
      COMPLETED: '#10B981',
      FAILED: '#EF4444',
      CANCELLED: '#6B7280',
      REJECTED: '#EF4444'
    };
    
    return colors[status] || '#6B7280';
  }

  getTypeIcon(type: string): string {
    const icons: Record<string, string> = {
      PLAN_CLOSURE: '📦',
      EXTERNAL_PAYOUT: '🏦'
    };
    
    return icons[type] || '💰';
  }

  generateReference(): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8);
    return `WTH_${timestamp}_${random}`.toUpperCase();
  }

  validateWithdrawalAmount(
    amount: number, 
    availableBalance: number, 
    type: string
  ): { isValid: boolean; error?: string } {
    const minAmount = 1000; // Minimum withdrawal amount
    const maxAmount = type === 'PLAN_CLOSURE' ? availableBalance : 1000000;

    if (amount < minAmount) {
      return { 
        isValid: false, 
        error: `Minimum withdrawal amount is ${this.formatCurrency(minAmount)}` 
      };
    }

    if (amount > maxAmount) {
      return { 
        isValid: false, 
        error: `Maximum withdrawal amount is ${this.formatCurrency(maxAmount)}` 
      };
    }

    if (amount > availableBalance) {
      return { 
        isValid: false, 
        error: 'Insufficient balance' 
      };
    }

    return { isValid: true };
  }

  estimateProcessingTime(type: string, amount: number): string {
    if (type === 'PLAN_CLOSURE') {
      return 'Instant'; // In-app transfers are instant
    }

    if (amount > 100000) {
      return '1-3 business days'; // Large amounts take longer
    }

    return '24 hours'; // Standard processing time
  }
}

export const withdrawalsService = new WithdrawalsService();
