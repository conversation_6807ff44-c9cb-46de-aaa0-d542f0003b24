"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  FiCalendar, 
  FiTrendingUp, 
  FiTarget, 
  FiPercent,
  FiDollarSign,
  FiClock,
  FiSettings,
  FiCheck,
  FiAlertCircle
} from 'react-icons/fi';
import { Button } from '../ui/Button';
import { Card3D } from '../ui/Card3D';
import { 
  AutomaticWithdrawalRule,
  CreateAutomaticRuleRequest,
  TriggerType,
  WithdrawalAccount
} from '../../types/automaticWithdrawal';
import { automaticWithdrawalService } from '../../services/automaticWithdrawalService';

interface AutomaticWithdrawalSetupProps {
  onRuleCreated: (rule: AutomaticWithdrawalRule) => void;
  onCancel: () => void;
  editingRule?: AutomaticWithdrawalRule;
  withdrawalAccounts: WithdrawalAccount[];
}

export default function AutomaticWithdrawalSetup({
  onRuleCreated,
  onCancel,
  editingRule,
  withdrawalAccounts
}: AutomaticWithdrawalSetupProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form State
  const [formData, setFormData] = useState<CreateAutomaticRuleRequest>({
    name: '',
    description: '',
    triggerType: 'balance_threshold',
    triggerConditions: {},
    withdrawalConfig: {
      withdrawalAccountId: '',
      amountType: 'fixed',
      currency: 'NGN'
    },
    executionSettings: {
      priority: 'medium',
      retryAttempts: 3,
      retryDelay: 5,
      notifyUser: true,
      requireApproval: false
    }
  });

  useEffect(() => {
    if (editingRule) {
      populateEditForm();
    }
  }, [editingRule]);

  const populateEditForm = () => {
    if (!editingRule) return;

    setFormData({
      name: editingRule.name,
      description: editingRule.description || '',
      triggerType: editingRule.triggerType,
      triggerConditions: editingRule.triggerConditions,
      withdrawalConfig: editingRule.withdrawalConfig,
      executionSettings: editingRule.executionSettings
    });
  };

  const triggerTypes = [
    {
      type: 'balance_threshold' as TriggerType,
      icon: FiTrendingUp,
      title: 'Balance Threshold',
      description: 'Withdraw when balance reaches a specific amount'
    },
    {
      type: 'date_based' as TriggerType,
      icon: FiCalendar,
      title: 'Scheduled',
      description: 'Withdraw on specific dates or intervals'
    },
    {
      type: 'interest_earned' as TriggerType,
      icon: FiPercent,
      title: 'Interest Earned',
      description: 'Withdraw when interest earned reaches threshold'
    },
    {
      type: 'goal_reached' as TriggerType,
      icon: FiTarget,
      title: 'Goal Reached',
      description: 'Withdraw when savings goal is achieved'
    }
  ];

  const amountTypes = [
    {
      type: 'fixed',
      title: 'Fixed Amount',
      description: 'Withdraw a specific amount each time'
    },
    {
      type: 'percentage',
      title: 'Percentage',
      description: 'Withdraw a percentage of your balance'
    },
    {
      type: 'excess',
      title: 'Excess Amount',
      description: 'Withdraw amount above the trigger threshold'
    },
    {
      type: 'all',
      title: 'All Available',
      description: 'Withdraw all available funds (minus minimum balance)'
    }
  ];

  const handleSubmit = async () => {
    setLoading(true);
    setError(null);

    try {
      let result: AutomaticWithdrawalRule;
      
      if (editingRule) {
        result = await automaticWithdrawalService.updateAutomaticRule(
          editingRule.id,
          formData
        );
      } else {
        result = await automaticWithdrawalService.createAutomaticRule(formData);
      }

      onRuleCreated(result);
    } catch (error: any) {
      setError(error.message || 'Failed to save automatic withdrawal rule');
    } finally {
      setLoading(false);
    }
  };

  const updateFormData = (updates: Partial<CreateAutomaticRuleRequest>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  };

  const updateTriggerConditions = (updates: any) => {
    setFormData(prev => ({
      ...prev,
      triggerConditions: { ...prev.triggerConditions, ...updates }
    }));
  };

  const updateWithdrawalConfig = (updates: any) => {
    setFormData(prev => ({
      ...prev,
      withdrawalConfig: { ...prev.withdrawalConfig, ...updates }
    }));
  };

  const updateExecutionSettings = (updates: any) => {
    setFormData(prev => ({
      ...prev,
      executionSettings: { ...prev.executionSettings, ...updates }
    }));
  };

  const renderStep1 = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-theme font-inter mb-2">Basic Information</h3>
        <p className="text-theme-secondary font-inter">Give your automatic withdrawal rule a name and description</p>
      </div>

      <div>
        <label className="block text-sm font-medium text-theme mb-2 font-inter">
          Rule Name
        </label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => updateFormData({ name: e.target.value })}
          className="w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter"
          placeholder="e.g., Monthly Interest Withdrawal"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-theme mb-2 font-inter">
          Description (Optional)
        </label>
        <textarea
          value={formData.description}
          onChange={(e) => updateFormData({ description: e.target.value })}
          className="w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter"
          placeholder="Describe when and why this rule should trigger"
          rows={3}
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-theme mb-3 font-inter">
          Trigger Type
        </label>
        <div className="grid grid-cols-1 gap-3">
          {triggerTypes.map((trigger) => {
            const IconComponent = trigger.icon;
            const isSelected = formData.triggerType === trigger.type;
            
            return (
              <Card3D
                key={trigger.type}
                className={`p-4 cursor-pointer transition-all duration-300 ${
                  isSelected 
                    ? 'ring-2 ring-brand bg-brand/5' 
                    : 'hover:bg-theme-secondary/50'
                }`}
                onClick={() => updateFormData({ triggerType: trigger.type })}
                elevation={isSelected ? 2 : 1}
              >
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${
                    isSelected ? 'bg-brand text-white' : 'bg-theme-secondary text-theme-secondary'
                  }`}>
                    <IconComponent className="w-5 h-5" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-theme font-inter">{trigger.title}</h4>
                    <p className="text-sm text-theme-secondary font-inter">{trigger.description}</p>
                  </div>
                  {isSelected && (
                    <FiCheck className="w-5 h-5 text-brand ml-auto" />
                  )}
                </div>
              </Card3D>
            );
          })}
        </div>
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-theme font-inter mb-2">Trigger Conditions</h3>
        <p className="text-theme-secondary font-inter">Configure when this rule should activate</p>
      </div>

      {formData.triggerType === 'balance_threshold' && (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-theme mb-2 font-inter">
              Balance Threshold (₦)
            </label>
            <input
              type="number"
              value={formData.triggerConditions.balanceThreshold || ''}
              onChange={(e) => updateTriggerConditions({ balanceThreshold: Number(e.target.value) })}
              className="w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter"
              placeholder="100000"
              min="1000"
              required
            />
            <p className="text-xs text-theme-secondary mt-1 font-inter">
              Trigger when balance reaches this amount
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-theme mb-2 font-inter">
              Minimum Balance to Keep (₦)
            </label>
            <input
              type="number"
              value={formData.triggerConditions.minimumBalance || ''}
              onChange={(e) => updateTriggerConditions({ minimumBalance: Number(e.target.value) })}
              className="w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter"
              placeholder="10000"
              min="0"
            />
            <p className="text-xs text-theme-secondary mt-1 font-inter">
              Always keep this minimum amount in your account
            </p>
          </div>
        </div>
      )}

      {formData.triggerType === 'date_based' && (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-theme mb-2 font-inter">
              Frequency
            </label>
            <select
              value={formData.triggerConditions.frequency || 'monthly'}
              onChange={(e) => updateTriggerConditions({ frequency: e.target.value })}
              className="w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter"
            >
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
              <option value="quarterly">Quarterly</option>
            </select>
          </div>

          {formData.triggerConditions.frequency === 'monthly' && (
            <div>
              <label className="block text-sm font-medium text-theme mb-2 font-inter">
                Day of Month
              </label>
              <input
                type="number"
                value={formData.triggerConditions.dayOfMonth || ''}
                onChange={(e) => updateTriggerConditions({ dayOfMonth: Number(e.target.value) })}
                className="w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter"
                placeholder="1"
                min="1"
                max="31"
                required
              />
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-theme mb-2 font-inter">
              Time
            </label>
            <input
              type="time"
              value={formData.triggerConditions.time || '09:00'}
              onChange={(e) => updateTriggerConditions({ time: e.target.value })}
              className="w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter"
            />
          </div>
        </div>
      )}

      {formData.triggerType === 'interest_earned' && (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-theme mb-2 font-inter">
              Interest Threshold (₦)
            </label>
            <input
              type="number"
              value={formData.triggerConditions.interestThreshold || ''}
              onChange={(e) => updateTriggerConditions({ interestThreshold: Number(e.target.value) })}
              className="w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter"
              placeholder="5000"
              min="100"
              required
            />
            <p className="text-xs text-theme-secondary mt-1 font-inter">
              Trigger when interest earned reaches this amount
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-theme mb-2 font-inter">
              Interest Period
            </label>
            <select
              value={formData.triggerConditions.interestPeriod || 'monthly'}
              onChange={(e) => updateTriggerConditions({ interestPeriod: e.target.value })}
              className="w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter"
            >
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
            </select>
          </div>
        </div>
      )}
    </div>
  );

  const renderStep3 = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-theme font-inter mb-2">Withdrawal Configuration</h3>
        <p className="text-theme-secondary font-inter">Configure how much to withdraw and where to send it</p>
      </div>

      <div>
        <label className="block text-sm font-medium text-theme mb-2 font-inter">
          Withdrawal Account
        </label>
        <select
          value={formData.withdrawalConfig.withdrawalAccountId}
          onChange={(e) => updateWithdrawalConfig({ withdrawalAccountId: e.target.value })}
          className="w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter"
          required
        >
          <option value="">Select withdrawal account</option>
          {withdrawalAccounts.map((account) => (
            <option key={account.id} value={account.id}>
              {account.type === 'bank_account' && account.bankDetails
                ? `${account.bankDetails.bankName} - ${account.bankDetails.accountNumber.slice(-4)}`
                : `${account.type} account`}
            </option>
          ))}
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-theme mb-3 font-inter">
          Amount Type
        </label>
        <div className="grid grid-cols-1 gap-3">
          {amountTypes.map((amountType) => {
            const isSelected = formData.withdrawalConfig.amountType === amountType.type;
            
            return (
              <Card3D
                key={amountType.type}
                className={`p-4 cursor-pointer transition-all duration-300 ${
                  isSelected 
                    ? 'ring-2 ring-brand bg-brand/5' 
                    : 'hover:bg-theme-secondary/50'
                }`}
                onClick={() => updateWithdrawalConfig({ amountType: amountType.type })}
                elevation={isSelected ? 2 : 1}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-semibold text-theme font-inter">{amountType.title}</h4>
                    <p className="text-sm text-theme-secondary font-inter">{amountType.description}</p>
                  </div>
                  {isSelected && (
                    <FiCheck className="w-5 h-5 text-brand" />
                  )}
                </div>
              </Card3D>
            );
          })}
        </div>
      </div>

      {formData.withdrawalConfig.amountType === 'fixed' && (
        <div>
          <label className="block text-sm font-medium text-theme mb-2 font-inter">
            Fixed Amount (₦)
          </label>
          <input
            type="number"
            value={formData.withdrawalConfig.amount || ''}
            onChange={(e) => updateWithdrawalConfig({ amount: Number(e.target.value) })}
            className="w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter"
            placeholder="50000"
            min="1000"
            required
          />
        </div>
      )}

      {formData.withdrawalConfig.amountType === 'percentage' && (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-theme mb-2 font-inter">
              Percentage (%)
            </label>
            <input
              type="number"
              value={formData.withdrawalConfig.percentage || ''}
              onChange={(e) => updateWithdrawalConfig({ percentage: Number(e.target.value) })}
              className="w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter"
              placeholder="10"
              min="1"
              max="100"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-theme mb-2 font-inter">
              Keep Minimum Balance (₦)
            </label>
            <input
              type="number"
              value={formData.withdrawalConfig.keepMinimumBalance || ''}
              onChange={(e) => updateWithdrawalConfig({ keepMinimumBalance: Number(e.target.value) })}
              className="w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter"
              placeholder="10000"
              min="0"
            />
          </div>
        </div>
      )}

      {(formData.withdrawalConfig.amountType === 'all' || formData.withdrawalConfig.amountType === 'excess') && (
        <div>
          <label className="block text-sm font-medium text-theme mb-2 font-inter">
            Keep Minimum Balance (₦)
          </label>
          <input
            type="number"
            value={formData.withdrawalConfig.keepMinimumBalance || ''}
            onChange={(e) => updateWithdrawalConfig({ keepMinimumBalance: Number(e.target.value) })}
            className="w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter"
            placeholder="10000"
            min="0"
          />
          <p className="text-xs text-theme-secondary mt-1 font-inter">
            Always keep this minimum amount in your account
          </p>
        </div>
      )}
    </div>
  );

  const renderStep4 = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-theme font-inter mb-2">Execution Settings</h3>
        <p className="text-theme-secondary font-inter">Configure how the rule should be executed</p>
      </div>

      <div>
        <label className="block text-sm font-medium text-theme mb-2 font-inter">
          Priority Level
        </label>
        <select
          value={formData.executionSettings.priority}
          onChange={(e) => updateExecutionSettings({ priority: e.target.value })}
          className="w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter"
        >
          <option value="low">Low Priority</option>
          <option value="medium">Medium Priority</option>
          <option value="high">High Priority</option>
        </select>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-theme mb-2 font-inter">
            Retry Attempts
          </label>
          <input
            type="number"
            value={formData.executionSettings.retryAttempts}
            onChange={(e) => updateExecutionSettings({ retryAttempts: Number(e.target.value) })}
            className="w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter"
            min="0"
            max="10"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-theme mb-2 font-inter">
            Retry Delay (minutes)
          </label>
          <input
            type="number"
            value={formData.executionSettings.retryDelay}
            onChange={(e) => updateExecutionSettings({ retryDelay: Number(e.target.value) })}
            className="w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter"
            min="1"
            max="60"
          />
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium text-theme font-inter">Notify User</h4>
            <p className="text-sm text-theme-secondary font-inter">Send notification when rule executes</p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={formData.executionSettings.notifyUser}
              onChange={(e) => updateExecutionSettings({ notifyUser: e.target.checked })}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-theme-secondary peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-brand/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-brand"></div>
          </label>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium text-theme font-inter">Require Approval</h4>
            <p className="text-sm text-theme-secondary font-inter">Require manual approval before execution</p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={formData.executionSettings.requireApproval}
              onChange={(e) => updateExecutionSettings({ requireApproval: e.target.checked })}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-theme-secondary peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-brand/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-brand"></div>
          </label>
        </div>
      </div>
    </div>
  );

  const steps = [
    { number: 1, title: 'Basic Info', component: renderStep1 },
    { number: 2, title: 'Trigger', component: renderStep2 },
    { number: 3, title: 'Amount', component: renderStep3 },
    { number: 4, title: 'Settings', component: renderStep4 }
  ];

  return (
    <div className="space-y-6">
      {/* Progress Steps */}
      <div className="flex items-center justify-between">
        {steps.map((step, index) => (
          <div key={step.number} className="flex items-center">
            <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 transition-colors ${
              currentStep >= step.number
                ? 'bg-brand border-brand text-white'
                : 'border-theme-secondary text-theme-secondary'
            }`}>
              {currentStep > step.number ? (
                <FiCheck className="w-4 h-4" />
              ) : (
                <span className="text-sm font-medium">{step.number}</span>
              )}
            </div>
            <span className={`ml-2 text-sm font-medium ${
              currentStep >= step.number ? 'text-theme' : 'text-theme-secondary'
            } font-inter`}>
              {step.title}
            </span>
            {index < steps.length - 1 && (
              <div className={`w-8 h-0.5 mx-4 ${
                currentStep > step.number ? 'bg-brand' : 'bg-theme-secondary'
              }`} />
            )}
          </div>
        ))}
      </div>

      {/* Step Content */}
      <Card3D className="p-6">
        {steps[currentStep - 1].component()}
      </Card3D>

      {/* Error Message */}
      {error && (
        <div className="flex items-center space-x-2 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
          <FiAlertCircle className="w-5 h-5 text-red-400" />
          <span className="text-red-400 text-sm font-inter">{error}</span>
        </div>
      )}

      {/* Navigation */}
      <div className="flex justify-between">
        <div className="flex space-x-3">
          <Button
            variant="outline"
            onClick={onCancel}
            className="font-inter"
          >
            Cancel
          </Button>
          {currentStep > 1 && (
            <Button
              variant="outline"
              onClick={() => setCurrentStep(prev => prev - 1)}
              className="font-inter"
            >
              Previous
            </Button>
          )}
        </div>

        <div className="flex space-x-3">
          {currentStep < steps.length ? (
            <Button
              onClick={() => setCurrentStep(prev => prev + 1)}
              disabled={!formData.name || !formData.triggerType}
              className="font-inter"
            >
              Next
            </Button>
          ) : (
            <Button
              onClick={handleSubmit}
              loading={loading}
              disabled={!formData.withdrawalConfig.withdrawalAccountId}
              className="font-inter"
            >
              {editingRule ? 'Update Rule' : 'Create Rule'}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
