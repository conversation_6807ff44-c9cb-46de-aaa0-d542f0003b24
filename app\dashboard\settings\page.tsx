"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  FiSettings, 
  FiBell, 
  FiShield, 
  FiEye, 
  FiEyeOff,
  FiMoon,
  FiSun,
  FiGlobe,
  FiLock,
  FiMail,
  FiSmartphone,
  FiSave
} from 'react-icons/fi';
import DashboardLayout from '../../../src/components/dashboard/DashboardLayout';
import { notificationsService, authService } from '../../../src/services';
import { NotificationPreferences } from '../../../src/types';
import { Button } from '../../../src/components/ui/Button';
import { Card } from '../../../src/components/ui/Card';
import { Switch } from '../../../src/components/ui/Switch';
import { Select } from '../../../src/components/ui/Select';
import { Input } from '../../../src/components/ui/Input';
import { toast } from '../../../src/components/ui/Toast';

export default function SettingsPage() {
  const [preferences, setPreferences] = useState<NotificationPreferences | null>(null);
  const [loading, setLoading] = useState(true);
  const [darkMode, setDarkMode] = useState(true);
  const [language, setLanguage] = useState('en');
  const [currency, setCurrency] = useState('NGN');
  
  const [securitySettings, setSecuritySettings] = useState({
    twoFactorEnabled: false,
    emailNotifications: true,
    smsNotifications: true,
    loginAlerts: true
  });

  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
    showPasswords: false
  });

  useEffect(() => {
    loadPreferences();
  }, []);

  const loadPreferences = async () => {
    try {
      const data = await notificationsService.getNotificationPreferences();
      setPreferences(data);
    } catch (error) {
      console.error('Failed to load preferences:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleNotificationToggle = async (type: string, enabled: boolean) => {
    try {
      if (preferences) {
        const updatedPreferences = {
          ...preferences,
          [type]: enabled
        };
        await notificationsService.updateNotificationPreferences(updatedPreferences);
        setPreferences(updatedPreferences);
        toast.success('Notification preferences updated');
      }
    } catch (error) {
      toast.error('Failed to update preferences');
    }
  };

  const handleChannelToggle = async (notificationType: string, channel: string, enabled: boolean) => {
    try {
      if (preferences) {
        const currentChannels = preferences.typePreferences[notificationType] || [];
        let updatedChannels;
        
        if (enabled) {
          updatedChannels = [...currentChannels, channel];
        } else {
          updatedChannels = currentChannels.filter(c => c !== channel);
        }

        await notificationsService.updateChannelPreference(notificationType, updatedChannels as any);
        
        const updatedPreferences = {
          ...preferences,
          typePreferences: {
            ...preferences.typePreferences,
            [notificationType]: updatedChannels
          }
        };
        setPreferences(updatedPreferences);
        toast.success('Channel preferences updated');
      }
    } catch (error) {
      toast.error('Failed to update channel preferences');
    }
  };

  const handlePasswordChange = async () => {
    try {
      if (passwordForm.newPassword !== passwordForm.confirmPassword) {
        toast.error('Passwords do not match');
        return;
      }

      if (passwordForm.newPassword.length < 8) {
        toast.error('Password must be at least 8 characters');
        return;
      }

      await authService.changePassword({
        currentPassword: passwordForm.currentPassword,
        newPassword: passwordForm.newPassword
      });

      setPasswordForm({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
        showPasswords: false
      });

      toast.success('Password changed successfully');
    } catch (error) {
      toast.error('Failed to change password');
    }
  };

  if (loading) {
    return (
      <DashboardLayout title="Settings">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title="Settings">
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-white">Settings</h1>
          <p className="text-gray-400 mt-2">Manage your account preferences and security</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Notification Settings */}
          <Card className="bg-gray-800 border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <FiBell className="mr-2" />
              Notification Preferences
            </h3>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white">Email Notifications</p>
                  <p className="text-sm text-gray-400">Receive notifications via email</p>
                </div>
                <Switch
                  checked={preferences?.emailNotifications || false}
                  onChange={(checked) => handleNotificationToggle('emailNotifications', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white">SMS Notifications</p>
                  <p className="text-sm text-gray-400">Receive notifications via SMS</p>
                </div>
                <Switch
                  checked={preferences?.smsNotifications || false}
                  onChange={(checked) => handleNotificationToggle('smsNotifications', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white">Push Notifications</p>
                  <p className="text-sm text-gray-400">Receive push notifications</p>
                </div>
                <Switch
                  checked={preferences?.pushNotifications || false}
                  onChange={(checked) => handleNotificationToggle('pushNotifications', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white">In-App Notifications</p>
                  <p className="text-sm text-gray-400">Show notifications in the app</p>
                </div>
                <Switch
                  checked={preferences?.inAppNotifications || false}
                  onChange={(checked) => handleNotificationToggle('inAppNotifications', checked)}
                />
              </div>
            </div>

            {/* Notification Types */}
            <div className="mt-6 pt-6 border-t border-gray-700">
              <h4 className="text-md font-semibold text-white mb-4">Notification Types</h4>
              
              <div className="space-y-3">
                {preferences && Object.entries(preferences.typePreferences).slice(0, 6).map(([type, channels]) => (
                  <div key={type} className="space-y-2">
                    <p className="text-sm font-medium text-white capitalize">
                      {type.replace(/_/g, ' ').toLowerCase()}
                    </p>
                    <div className="flex space-x-4 text-sm">
                      <label className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={channels.includes('EMAIL')}
                          onChange={(e) => handleChannelToggle(type, 'EMAIL', e.target.checked)}
                          className="rounded border-gray-600 bg-gray-700 text-green-600"
                        />
                        <span className="text-gray-400">Email</span>
                      </label>
                      <label className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={channels.includes('SMS')}
                          onChange={(e) => handleChannelToggle(type, 'SMS', e.target.checked)}
                          className="rounded border-gray-600 bg-gray-700 text-green-600"
                        />
                        <span className="text-gray-400">SMS</span>
                      </label>
                      <label className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={channels.includes('PUSH')}
                          onChange={(e) => handleChannelToggle(type, 'PUSH', e.target.checked)}
                          className="rounded border-gray-600 bg-gray-700 text-green-600"
                        />
                        <span className="text-gray-400">Push</span>
                      </label>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>

          {/* Security Settings */}
          <Card className="bg-gray-800 border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <FiShield className="mr-2" />
              Security Settings
            </h3>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white">Two-Factor Authentication</p>
                  <p className="text-sm text-gray-400">Add an extra layer of security</p>
                </div>
                <Switch
                  checked={securitySettings.twoFactorEnabled}
                  onChange={(checked) => setSecuritySettings({
                    ...securitySettings,
                    twoFactorEnabled: checked
                  })}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white">Login Alerts</p>
                  <p className="text-sm text-gray-400">Get notified of new logins</p>
                </div>
                <Switch
                  checked={securitySettings.loginAlerts}
                  onChange={(checked) => setSecuritySettings({
                    ...securitySettings,
                    loginAlerts: checked
                  })}
                />
              </div>
            </div>

            {/* Change Password */}
            <div className="mt-6 pt-6 border-t border-gray-700">
              <h4 className="text-md font-semibold text-white mb-4">Change Password</h4>
              
              <div className="space-y-4">
                <div className="relative">
                  <Input
                    label="Current Password"
                    type={passwordForm.showPasswords ? "text" : "password"}
                    value={passwordForm.currentPassword}
                    onChange={(e) => setPasswordForm({
                      ...passwordForm,
                      currentPassword: e.target.value
                    })}
                    placeholder="Enter current password"
                  />
                </div>

                <div className="relative">
                  <Input
                    label="New Password"
                    type={passwordForm.showPasswords ? "text" : "password"}
                    value={passwordForm.newPassword}
                    onChange={(e) => setPasswordForm({
                      ...passwordForm,
                      newPassword: e.target.value
                    })}
                    placeholder="Enter new password"
                  />
                </div>

                <div className="relative">
                  <Input
                    label="Confirm New Password"
                    type={passwordForm.showPasswords ? "text" : "password"}
                    value={passwordForm.confirmPassword}
                    onChange={(e) => setPasswordForm({
                      ...passwordForm,
                      confirmPassword: e.target.value
                    })}
                    placeholder="Confirm new password"
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="showPasswords"
                    checked={passwordForm.showPasswords}
                    onChange={(e) => setPasswordForm({
                      ...passwordForm,
                      showPasswords: e.target.checked
                    })}
                    className="rounded border-gray-600 bg-gray-700 text-green-600"
                  />
                  <label htmlFor="showPasswords" className="text-sm text-gray-400">
                    Show passwords
                  </label>
                </div>

                <Button
                  onClick={handlePasswordChange}
                  className="w-full bg-green-600 hover:bg-green-700"
                  disabled={!passwordForm.currentPassword || !passwordForm.newPassword || !passwordForm.confirmPassword}
                >
                  <FiLock className="mr-2" />
                  Change Password
                </Button>
              </div>
            </div>
          </Card>

          {/* App Preferences */}
          <Card className="bg-gray-800 border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <FiSettings className="mr-2" />
              App Preferences
            </h3>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white">Dark Mode</p>
                  <p className="text-sm text-gray-400">Use dark theme</p>
                </div>
                <Switch
                  checked={darkMode}
                  onChange={setDarkMode}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">
                  Language
                </label>
                <Select
                  value={language}
                  onChange={setLanguage}
                  options={[
                    { value: 'en', label: 'English' },
                    { value: 'yo', label: 'Yoruba' },
                    { value: 'ig', label: 'Igbo' },
                    { value: 'ha', label: 'Hausa' }
                  ]}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">
                  Currency
                </label>
                <Select
                  value={currency}
                  onChange={setCurrency}
                  options={[
                    { value: 'NGN', label: 'Nigerian Naira (₦)' },
                    { value: 'USD', label: 'US Dollar ($)' },
                    { value: 'EUR', label: 'Euro (€)' },
                    { value: 'GBP', label: 'British Pound (£)' }
                  ]}
                />
              </div>
            </div>
          </Card>

          {/* Privacy Settings */}
          <Card className="bg-gray-800 border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <FiEye className="mr-2" />
              Privacy Settings
            </h3>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white">Profile Visibility</p>
                  <p className="text-sm text-gray-400">Make your profile visible to others</p>
                </div>
                <Switch defaultChecked={false} />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white">Activity Status</p>
                  <p className="text-sm text-gray-400">Show when you're active</p>
                </div>
                <Switch defaultChecked={true} />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white">Data Analytics</p>
                  <p className="text-sm text-gray-400">Help improve our services</p>
                </div>
                <Switch defaultChecked={true} />
              </div>
            </div>

            {/* Quiet Hours */}
            <div className="mt-6 pt-6 border-t border-gray-700">
              <h4 className="text-md font-semibold text-white mb-4">Quiet Hours</h4>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-white">Enable Quiet Hours</p>
                    <p className="text-sm text-gray-400">Pause notifications during specified hours</p>
                  </div>
                  <Switch 
                    checked={preferences?.quietHours.enabled || false}
                    onChange={(checked) => {
                      if (preferences) {
                        const updatedPreferences = {
                          ...preferences,
                          quietHours: {
                            ...preferences.quietHours,
                            enabled: checked
                          }
                        };
                        notificationsService.updateNotificationPreferences(updatedPreferences);
                        setPreferences(updatedPreferences);
                      }
                    }}
                  />
                </div>

                {preferences?.quietHours.enabled && (
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-2">
                        Start Time
                      </label>
                      <Input
                        type="time"
                        value={preferences.quietHours.startTime}
                        onChange={(e) => {
                          const updatedPreferences = {
                            ...preferences,
                            quietHours: {
                              ...preferences.quietHours,
                              startTime: e.target.value
                            }
                          };
                          setPreferences(updatedPreferences);
                        }}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-2">
                        End Time
                      </label>
                      <Input
                        type="time"
                        value={preferences.quietHours.endTime}
                        onChange={(e) => {
                          const updatedPreferences = {
                            ...preferences,
                            quietHours: {
                              ...preferences.quietHours,
                              endTime: e.target.value
                            }
                          };
                          setPreferences(updatedPreferences);
                        }}
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </Card>
        </div>

        {/* Save Button */}
        <div className="flex justify-end">
          <Button
            onClick={() => toast.success('Settings saved successfully')}
            className="bg-green-600 hover:bg-green-700"
          >
            <FiSave className="mr-2" />
            Save All Settings
          </Button>
        </div>
      </div>
    </DashboardLayout>
  );
}
