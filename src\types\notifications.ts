export interface Notification {
  id: string;
  userId: string;
  type: 'DEPOSIT' | 'WITHDRAWAL' | 'CONTRIBUTION' | 'PLAN_CREATED' | 'PLAN_COMPLETED' | 'PLAN_PAUSED' | 'GOAL_ACHIEVED' | 'MILESTONE_REACHED' | 'PAYMENT_DUE' | 'PAYMENT_OVERDUE' | 'KYC_APPROVED' | 'KYC_REJECTED' | 'GROUP_INVITATION' | 'GROUP_PAYOUT' | 'SYSTEM' | 'PROMOTIONAL' | 'SECURITY' | 'OTHER';
  title: string;
  message: string;
  description?: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  status: 'UNREAD' | 'READ' | 'ARCHIVED';
  
  // Related entities
  relatedEntityType?: 'PLAN' | 'GOAL' | 'TARGET' | 'GROUP' | 'DEPOSIT' | 'WITHDRAWAL' | 'TRANSACTION' | 'KYC';
  relatedEntityId?: string;
  
  // Action details
  actionRequired: boolean;
  actionUrl?: string;
  actionText?: string;
  
  // Delivery details
  channels: NotificationChannel[];
  deliveryStatus: {
    email?: 'PENDING' | 'SENT' | 'DELIVERED' | 'FAILED';
    sms?: 'PENDING' | 'SENT' | 'DELIVERED' | 'FAILED';
    push?: 'PENDING' | 'SENT' | 'DELIVERED' | 'FAILED';
    inApp: 'PENDING' | 'SENT' | 'DELIVERED' | 'FAILED';
  };
  
  // Metadata
  metadata?: Record<string, any>;
  imageUrl?: string;
  iconUrl?: string;
  
  // Timestamps
  scheduledAt?: string;
  sentAt?: string;
  readAt?: string;
  archivedAt?: string;
  expiresAt?: string;
  createdAt: string;
  updatedAt: string;
}

export type NotificationChannel = 'EMAIL' | 'SMS' | 'PUSH' | 'IN_APP';

export interface CreateNotificationData {
  userId?: string;
  userIds?: string[];
  type: 'DEPOSIT' | 'WITHDRAWAL' | 'CONTRIBUTION' | 'PLAN_CREATED' | 'PLAN_COMPLETED' | 'PLAN_PAUSED' | 'GOAL_ACHIEVED' | 'MILESTONE_REACHED' | 'PAYMENT_DUE' | 'PAYMENT_OVERDUE' | 'KYC_APPROVED' | 'KYC_REJECTED' | 'GROUP_INVITATION' | 'GROUP_PAYOUT' | 'SYSTEM' | 'PROMOTIONAL' | 'SECURITY' | 'OTHER';
  title: string;
  message: string;
  description?: string;
  priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  relatedEntityType?: 'PLAN' | 'GOAL' | 'TARGET' | 'GROUP' | 'DEPOSIT' | 'WITHDRAWAL' | 'TRANSACTION' | 'KYC';
  relatedEntityId?: string;
  actionRequired?: boolean;
  actionUrl?: string;
  actionText?: string;
  channels?: NotificationChannel[];
  metadata?: Record<string, any>;
  imageUrl?: string;
  iconUrl?: string;
  scheduledAt?: string;
  expiresAt?: string;
}

export interface BulkNotificationData {
  userFilters?: {
    role?: 'USER' | 'ADMIN';
    kycStatus?: 'PENDING' | 'APPROVED' | 'REJECTED';
    isActive?: boolean;
    hasActivePlans?: boolean;
    registeredAfter?: string;
    registeredBefore?: string;
  };
  notification: Omit<CreateNotificationData, 'userId' | 'userIds'>;
}

export interface NotificationPreferences {
  userId: string;
  emailNotifications: boolean;
  smsNotifications: boolean;
  pushNotifications: boolean;
  inAppNotifications: boolean;
  
  // Type-specific preferences
  typePreferences: {
    DEPOSIT: NotificationChannel[];
    WITHDRAWAL: NotificationChannel[];
    CONTRIBUTION: NotificationChannel[];
    PLAN_CREATED: NotificationChannel[];
    PLAN_COMPLETED: NotificationChannel[];
    PLAN_PAUSED: NotificationChannel[];
    GOAL_ACHIEVED: NotificationChannel[];
    MILESTONE_REACHED: NotificationChannel[];
    PAYMENT_DUE: NotificationChannel[];
    PAYMENT_OVERDUE: NotificationChannel[];
    KYC_APPROVED: NotificationChannel[];
    KYC_REJECTED: NotificationChannel[];
    GROUP_INVITATION: NotificationChannel[];
    GROUP_PAYOUT: NotificationChannel[];
    SYSTEM: NotificationChannel[];
    PROMOTIONAL: NotificationChannel[];
    SECURITY: NotificationChannel[];
    OTHER: NotificationChannel[];
  };
  
  // Quiet hours
  quietHours: {
    enabled: boolean;
    startTime: string; // HH:mm format
    endTime: string; // HH:mm format
    timezone: string;
  };
  
  updatedAt: string;
}

export interface NotificationStats {
  totalNotifications: number;
  unreadNotifications: number;
  readNotifications: number;
  archivedNotifications: number;
  notificationsThisWeek: number;
  notificationsThisMonth: number;
  
  // Delivery stats
  deliveryStats: {
    email: {
      sent: number;
      delivered: number;
      failed: number;
      deliveryRate: number;
    };
    sms: {
      sent: number;
      delivered: number;
      failed: number;
      deliveryRate: number;
    };
    push: {
      sent: number;
      delivered: number;
      failed: number;
      deliveryRate: number;
    };
    inApp: {
      sent: number;
      delivered: number;
      failed: number;
      deliveryRate: number;
    };
  };
  
  // Type breakdown
  typeBreakdown: {
    type: string;
    count: number;
    percentage: number;
  }[];
}

export interface NotificationSearchFilters {
  search?: string;
  type?: string;
  priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  status?: 'UNREAD' | 'READ' | 'ARCHIVED';
  actionRequired?: boolean;
  dateFrom?: string;
  dateTo?: string;
  userId?: string;
  relatedEntityType?: 'PLAN' | 'GOAL' | 'TARGET' | 'GROUP' | 'DEPOSIT' | 'WITHDRAWAL' | 'TRANSACTION' | 'KYC';
  relatedEntityId?: string;
  sortBy?: 'createdAt' | 'priority' | 'readAt';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface PaginatedNotificationResponse {
  notifications: Notification[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  unreadCount: number;
}
