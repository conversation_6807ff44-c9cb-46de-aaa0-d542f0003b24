"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FiUser, 
  FiSettings, 
  FiShield, 
  FiCreditCard, 
  FiLogOut,
  FiChevronRight,
  FiCheck,
  FiClock
} from 'react-icons/fi';
import { useAuth } from '../../hooks/use-auth';
import { useRouter } from 'next/navigation';

interface ProfileMenuItem {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  action?: () => void;
  href?: string;
  badge?: string;
  status?: 'verified' | 'pending' | 'none';
  divider?: boolean;
}

export default function ProfileDropdown() {
  const [isOpen, setIsOpen] = useState(false);
  const { user, logout } = useAuth();
  const router = useRouter();

  const handleLogout = async () => {
    try {
      await logout();
      router.push('/');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  // Use actual user data or fallback to demo data
  const currentUser = user || {
    firstName: 'Demo',
    lastName: 'User',
    email: '<EMAIL>',
    role: 'USER',
    kycStatus: 'PENDING'
  };

  const menuItems: ProfileMenuItem[] = [
    {
      id: 'profile',
      label: 'View Profile',
      icon: FiUser,
      href: '/dashboard/profile'
    },
    {
      id: 'kyc',
      label: 'Identity Verification',
      icon: FiShield,
      href: '/dashboard/kyc',
      status: currentUser.kycStatus === 'APPROVED' ? 'verified' : 'pending'
    },
    {
      id: 'payment',
      label: 'Payment Methods',
      icon: FiCreditCard,
      href: '/dashboard/payment-methods'
    },
    {
      id: 'settings',
      label: 'Account Settings',
      icon: FiSettings,
      href: '/dashboard/settings',
      divider: true
    },
    {
      id: 'logout',
      label: 'Sign Out',
      icon: FiLogOut,
      action: handleLogout
    }
  ];

  const handleItemClick = (item: ProfileMenuItem) => {
    if (item.action) {
      item.action();
    } else if (item.href) {
      router.push(item.href);
    }
    setIsOpen(false);
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'verified':
        return <FiCheck className="w-3 h-3 text-brand" />;
      case 'pending':
        return <FiClock className="w-3 h-3 text-yellow-500" />;
      default:
        return null;
    }
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'verified':
        return 'bg-brand/10 text-brand';
      case 'pending':
        return 'bg-yellow-500/10 text-yellow-500';
      default:
        return '';
    }
  };

  return (
    <div className="relative">
      <motion.button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 p-1 rounded-lg hover:bg-theme-secondary transition-colors"
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <div className="w-8 h-8 bg-gradient-to-r from-brand to-brand-dark rounded-full flex items-center justify-center shadow-lg">
          <span className="text-white font-semibold text-sm font-inter">
            {currentUser?.firstName?.[0]}{currentUser?.lastName?.[0]}
          </span>
        </div>
        <div className="hidden md:block text-left">
          <p className="text-sm font-medium text-theme font-inter">
            {currentUser?.firstName} {currentUser?.lastName}
          </p>
          <p className="text-xs text-theme-secondary">
            {currentUser?.role}
          </p>
        </div>
      </motion.button>

      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <div
              className="fixed inset-0 z-40"
              onClick={() => setIsOpen(false)}
            />
            
            {/* Dropdown */}
            <motion.div
              initial={{ opacity: 0, y: 10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: 10, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className="absolute right-0 top-full mt-2 w-72 bg-theme border border-theme rounded-xl shadow-2xl z-50 overflow-hidden"
              style={{
                boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(34, 197, 94, 0.1)'
              }}
            >
              {/* Header */}
              <div className="p-4 border-b border-theme bg-theme-secondary">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-gradient-to-r from-brand to-brand-dark rounded-full flex items-center justify-center shadow-lg">
                    <span className="text-white font-bold text-lg font-inter">
                      {currentUser?.firstName?.[0]}{currentUser?.lastName?.[0]}
                    </span>
                  </div>
                  <div className="flex-1">
                    <h3 className="font-inter font-semibold text-theme">
                      {currentUser?.firstName} {currentUser?.lastName}
                    </h3>
                    <p className="text-sm text-theme-secondary font-inter">
                      {currentUser?.email}
                    </p>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className={`inline-block px-2 py-1 text-xs rounded-full font-medium ${
                        currentUser?.role === 'ADMIN'
                          ? 'bg-red-500/20 text-red-400'
                          : 'bg-brand/20 text-brand'
                      }`}>
                        {currentUser?.role}
                      </span>
                      {currentUser?.kycStatus === 'APPROVED' && (
                        <span className="inline-flex items-center space-x-1 px-2 py-1 text-xs rounded-full bg-brand/20 text-brand">
                          <FiCheck className="w-3 h-3" />
                          <span>Verified</span>
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Menu Items */}
              <div className="py-2">
                {menuItems.map((item, index) => (
                  <React.Fragment key={item.id}>
                    {item.divider && index > 0 && (
                      <div className="my-2 border-t border-theme" />
                    )}
                    <motion.button
                      onClick={() => handleItemClick(item)}
                      className="w-full px-4 py-3 flex items-center justify-between hover:bg-theme-secondary transition-colors group"
                      whileHover={{ x: 4 }}
                      transition={{ duration: 0.2 }}
                    >
                      <div className="flex items-center space-x-3">
                        <div className="p-1.5 rounded-lg bg-brand/10 group-hover:bg-brand/20 transition-colors">
                          <item.icon className="w-4 h-4 text-brand" />
                        </div>
                        <span className="font-inter text-sm text-theme group-hover:text-brand transition-colors">
                          {item.label}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        {item.status && (
                          <div className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>
                            <div className="flex items-center space-x-1">
                              {getStatusIcon(item.status)}
                              <span>
                                {item.status === 'verified' ? 'Verified' : 'Pending'}
                              </span>
                            </div>
                          </div>
                        )}
                        {item.badge && (
                          <span className="px-2 py-1 text-xs bg-brand text-white rounded-full">
                            {item.badge}
                          </span>
                        )}
                        <FiChevronRight className="w-4 h-4 text-theme-secondary group-hover:text-brand transition-colors" />
                      </div>
                    </motion.button>
                  </React.Fragment>
                ))}
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
}
