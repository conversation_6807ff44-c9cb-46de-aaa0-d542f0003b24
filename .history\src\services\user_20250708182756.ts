import { 
  UserProfile, 
  UpdateUserProfileData, 
  UserBalance, 
  UserStats,
  AdminUserListItem,
  UserSearchFilters,
  PaginatedUserResponse,
  ApiResponse
} from '../types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

class UserService {
  private getAuthHeaders() {
    const token = localStorage.getItem('auth_token');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` })
    };
  }

  // Current User Profile Management
  async getCurrentUserProfile(): Promise<UserProfile> {
    // Use the backend's /user-info endpoint instead of /user/me
    const response = await fetch(`${API_BASE_URL}/api/auth/user-info`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch user profile');
    }

    // The backend returns { user } so extract user
    const data = await response.json();
    return data.user;
  }

  async updateCurrentUserProfile(data: UpdateUserProfileData): Promise<UserProfile> {
    const response = await fetch(`${API_BASE_URL}/user/me`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update user profile');
    }

    return response.json();
  }

  async uploadProfileImage(file: File): Promise<{ imageUrl: string }> {
    const formData = new FormData();
    formData.append('profileImage', file);

    const token = localStorage.getItem('auth_token');
    const response = await fetch(`${API_BASE_URL}/user/me/profile-image`, {
      method: 'POST',
      headers: {
        ...(token && { Authorization: `Bearer ${token}` })
      },
      body: formData
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to upload profile image');
    }

    return response.json();
  }

  // User Balance Management
  async getUserBalance(userId?: string): Promise<UserBalance> {
    // Use /api/user-info/balance/:userId if userId is provided, else throw error
    if (!userId) throw new Error('User ID is required to fetch balance');
    const response = await fetch(`${API_BASE_URL}/api/userinfo/balance/${userId}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch user balance');
    }

    return response.json();
  }

  async refreshUserBalance(): Promise<UserBalance> {
    const response = await fetch(`${API_BASE_URL}/user/balance/refresh`, {
      method: 'POST',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to refresh user balance');
    }

    return response.json();
  }

  // Bank Account Management
  async addBankAccount(bankData: {
    bankName: string;
    accountNumber: string;
    accountName: string;
    bankCode: string;
  }): Promise<{ bankAccount: any }> {
    const response = await fetch(`${API_BASE_URL}/user/bank-accounts`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(bankData)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to add bank account');
    }

    return response.json();
  }

  async getBankAccounts(): Promise<any[]> {
    const response = await fetch(`${API_BASE_URL}/user/bank-accounts`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch bank accounts');
    }

    return response.json();
  }

  async deleteBankAccount(accountId: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/user/bank-accounts/${accountId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to delete bank account');
    }
  }

  async setDefaultBankAccount(accountId: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/user/bank-accounts/${accountId}/set-default`, {
      method: 'POST',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to set default bank account');
    }
  }

  // Admin User Management
  async getUserById(userId: string): Promise<UserProfile> {
    const response = await fetch(`${API_BASE_URL}/user/${userId}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch user');
    }

    return response.json();
  }

  async getAllUsers(filters?: UserSearchFilters): Promise<PaginatedUserResponse> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/user/all?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch users');
    }

    return response.json();
  }

  async getUserStats(): Promise<UserStats> {
    const response = await fetch(`${API_BASE_URL}/user/stats`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch user statistics');
    }

    return response.json();
  }

  async updateUserStatus(userId: string, isActive: boolean): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/user/${userId}/status`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ isActive })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update user status');
    }
  }

  async resetUserPassword(userId: string): Promise<{ temporaryPassword: string }> {
    const response = await fetch(`${API_BASE_URL}/user/${userId}/reset-password`, {
      method: 'POST',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to reset user password');
    }

    return response.json();
  }

  async getUserActivity(userId: string, filters?: {
    dateFrom?: string;
    dateTo?: string;
    type?: string;
    page?: number;
    limit?: number;
  }): Promise<any> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/user/${userId}/activity?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch user activity');
    }

    return response.json();
  }

  // Utility Methods
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN'
    }).format(amount);
  }

  getInitials(firstName: string, lastName: string): string {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  }

  validatePhoneNumber(phoneNumber: string): boolean {
    const phoneRegex = /^(\+234|234|0)?[789][01]\d{8}$/;
    return phoneRegex.test(phoneNumber);
  }

  validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}

export const userService = new UserService();
