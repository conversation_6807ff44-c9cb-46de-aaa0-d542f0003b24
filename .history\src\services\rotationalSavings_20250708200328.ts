import { RotationalGroupSavings } from '../types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

class RotationalSavingsService {
  private getAuthHeaders() {
    const token = localStorage.getItem('auth_token');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` })
    };
  }

  // Fetch active rotational group savings for the logged-in user
  async getActiveRotationalGroups(): Promise<RotationalGroupSavings[]> {
    const response = await fetch(`${API_BASE_URL}/savings/rotational/active`, {
      headers: this.getAuthHeaders()
    });
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch active rotational group savings');
    }
    const data = await response.json();
    return data.activeRotationalGroups || [];
  }
}

export const rotationalSavingsService = new RotationalSavingsService();
