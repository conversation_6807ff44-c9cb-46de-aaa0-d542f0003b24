export interface Transaction {
  id: string;
  userId: string;
  type: 'DEPOSIT' | 'WITHDRAWAL' | 'CONTRIBUTION' | 'INTEREST' | 'PENALTY' | 'REFUND' | 'TRANSFER' | 'FEE' | 'BONUS';
  subType?: string;
  amount: number;
  currency: string;
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  reference: string;
  externalReference?: string;
  description: string;
  metadata?: Record<string, any>;
  
  // Related entities
  planId?: string;
  plan?: {
    id: string;
    name: string;
    planType: string;
  };
  goalId?: string;
  goal?: {
    id: string;
    title: string;
  };
  targetId?: string;
  target?: {
    id: string;
    title: string;
  };
  groupId?: string;
  group?: {
    id: string;
    name: string;
  };
  depositId?: string;
  withdrawalId?: string;
  
  // Financial details
  balanceBefore: number;
  balanceAfter: number;
  fees: number;
  netAmount: number;
  
  // Payment details
  paymentMethod?: string;
  paymentProvider?: string;
  
  // Timestamps
  initiatedAt: string;
  processedAt?: string;
  completedAt?: string;
  failedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateTransactionData {
  userId: string;
  type: 'DEPOSIT' | 'WITHDRAWAL' | 'CONTRIBUTION' | 'INTEREST' | 'PENALTY' | 'REFUND' | 'TRANSFER' | 'FEE' | 'BONUS';
  subType?: string;
  amount: number;
  currency?: string;
  description: string;
  reference?: string;
  externalReference?: string;
  metadata?: Record<string, any>;
  planId?: string;
  goalId?: string;
  targetId?: string;
  groupId?: string;
  depositId?: string;
  withdrawalId?: string;
  paymentMethod?: string;
  paymentProvider?: string;
  fees?: number;
}

export interface TransactionStats {
  totalTransactions: number;
  totalAmount: number;
  totalDeposits: number;
  totalWithdrawals: number;
  totalContributions: number;
  totalInterest: number;
  totalPenalties: number;
  totalFees: number;
  transactionsThisMonth: number;
  amountThisMonth: number;
  averageTransactionAmount: number;
  successRate: number;
  
  // Breakdown by type
  typeBreakdown: {
    type: string;
    count: number;
    amount: number;
    percentage: number;
  }[];
  
  // Monthly trends
  monthlyTrends: {
    month: string;
    count: number;
    amount: number;
    deposits: number;
    withdrawals: number;
    contributions: number;
  }[];
}

export interface TransactionSearchFilters {
  search?: string;
  type?: 'DEPOSIT' | 'WITHDRAWAL' | 'CONTRIBUTION' | 'INTEREST' | 'PENALTY' | 'REFUND' | 'TRANSFER' | 'FEE' | 'BONUS';
  subType?: string;
  status?: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  minAmount?: number;
  maxAmount?: number;
  dateFrom?: string;
  dateTo?: string;
  userId?: string;
  planId?: string;
  goalId?: string;
  targetId?: string;
  groupId?: string;
  paymentMethod?: string;
  paymentProvider?: string;
  sortBy?: 'createdAt' | 'amount' | 'type' | 'status';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface PaginatedTransactionResponse {
  transactions: Transaction[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  stats: {
    totalAmount: number;
    totalDeposits: number;
    totalWithdrawals: number;
    totalContributions: number;
    successfulCount: number;
    failedCount: number;
    pendingCount: number;
  };
}

export interface TransactionSummary {
  period: 'TODAY' | 'WEEK' | 'MONTH' | 'YEAR';
  totalTransactions: number;
  totalAmount: number;
  deposits: {
    count: number;
    amount: number;
  };
  withdrawals: {
    count: number;
    amount: number;
  };
  contributions: {
    count: number;
    amount: number;
  };
  interest: {
    count: number;
    amount: number;
  };
  penalties: {
    count: number;
    amount: number;
  };
}

export interface TransactionExport {
  format: 'CSV' | 'EXCEL' | 'PDF';
  filters: TransactionSearchFilters;
  includeMetadata: boolean;
  dateRange: {
    from: string;
    to: string;
  };
}
