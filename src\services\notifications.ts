import { 
  Notification, 
  CreateNotificationData,
  BulkNotificationData,
  NotificationPreferences,
  NotificationStats,
  NotificationSearchFilters,
  PaginatedNotificationResponse,
  NotificationChannel,
  ApiResponse
} from '../types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

class NotificationsService {
  private getAuthHeaders() {
    const token = localStorage.getItem('auth_token');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` })
    };
  }

  // User Notifications
  async getUserNotifications(filters?: NotificationSearchFilters): Promise<PaginatedNotificationResponse> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/notifications/user?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch user notifications');
    }

    return response.json();
  }

  async getUnreadNotifications(): Promise<Notification[]> {
    const response = await fetch(`${API_BASE_URL}/notifications/user/unread`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch unread notifications');
    }

    return response.json();
  }

  async getUnreadCount(): Promise<{ count: number }> {
    const response = await fetch(`${API_BASE_URL}/notifications/user/unread/count`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch unread count');
    }

    return response.json();
  }

  async markAsRead(notificationId: string): Promise<Notification> {
    const response = await fetch(`${API_BASE_URL}/notifications/${notificationId}/read`, {
      method: 'PUT',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to mark notification as read');
    }

    return response.json();
  }

  async markAllAsRead(): Promise<{ updated: number }> {
    const response = await fetch(`${API_BASE_URL}/notifications/user/read-all`, {
      method: 'PUT',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to mark all notifications as read');
    }

    return response.json();
  }

  async archiveNotification(notificationId: string): Promise<Notification> {
    const response = await fetch(`${API_BASE_URL}/notifications/${notificationId}/archive`, {
      method: 'PUT',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to archive notification');
    }

    return response.json();
  }

  async deleteNotification(notificationId: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/notifications/${notificationId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to delete notification');
    }
  }

  // Admin Notification Management
  async createNotification(data: CreateNotificationData): Promise<Notification> {
    const response = await fetch(`${API_BASE_URL}/notifications`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to create notification');
    }

    return response.json();
  }

  async sendBulkNotification(data: BulkNotificationData): Promise<{ sent: number; failed: number }> {
    const response = await fetch(`${API_BASE_URL}/notifications/bulk`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to send bulk notification');
    }

    return response.json();
  }

  async getAllNotifications(filters?: NotificationSearchFilters): Promise<PaginatedNotificationResponse> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/notifications/all?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch all notifications');
    }

    return response.json();
  }

  async getNotificationById(notificationId: string): Promise<Notification> {
    const response = await fetch(`${API_BASE_URL}/notifications/${notificationId}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch notification');
    }

    return response.json();
  }

  // Notification Preferences
  async getNotificationPreferences(): Promise<NotificationPreferences> {
    const response = await fetch(`${API_BASE_URL}/notifications/preferences`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch notification preferences');
    }

    return response.json();
  }

  async updateNotificationPreferences(preferences: Partial<NotificationPreferences>): Promise<NotificationPreferences> {
    const response = await fetch(`${API_BASE_URL}/notifications/preferences`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(preferences)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update notification preferences');
    }

    return response.json();
  }

  async updateChannelPreference(
    notificationType: string, 
    channels: NotificationChannel[]
  ): Promise<NotificationPreferences> {
    const response = await fetch(`${API_BASE_URL}/notifications/preferences/channel`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ notificationType, channels })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update channel preference');
    }

    return response.json();
  }

  // Statistics and Analytics
  async getNotificationStats(filters?: {
    dateFrom?: string;
    dateTo?: string;
    userId?: string;
    type?: string;
  }): Promise<NotificationStats> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/notifications/stats?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch notification statistics');
    }

    return response.json();
  }

  async getDeliveryStats(period: 'daily' | 'weekly' | 'monthly' = 'monthly'): Promise<any> {
    const response = await fetch(`${API_BASE_URL}/notifications/delivery-stats?period=${period}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch delivery statistics');
    }

    return response.json();
  }

  // Real-time Notifications (WebSocket/SSE)
  async subscribeToNotifications(callback: (notification: Notification) => void): Promise<() => void> {
    const token = localStorage.getItem('auth_token');
    const eventSource = new EventSource(`${API_BASE_URL}/notifications/stream?token=${token}`);
    
    eventSource.onmessage = (event) => {
      try {
        const notification = JSON.parse(event.data);
        callback(notification);
      } catch (error) {
        console.error('Failed to parse notification:', error);
      }
    };

    eventSource.onerror = (error) => {
      console.error('Notification stream error:', error);
    };

    // Return unsubscribe function
    return () => {
      eventSource.close();
    };
  }

  // Push Notification Registration
  async registerPushToken(token: string, platform: 'web' | 'ios' | 'android'): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/notifications/push/register`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ token, platform })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to register push token');
    }
  }

  async unregisterPushToken(token: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/notifications/push/unregister`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ token })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to unregister push token');
    }
  }

  // Utility Methods
  getNotificationTypeIcon(type: string): string {
    const icons: Record<string, string> = {
      DEPOSIT: '💰',
      WITHDRAWAL: '🏦',
      CONTRIBUTION: '📈',
      PLAN_CREATED: '📋',
      PLAN_COMPLETED: '✅',
      PLAN_PAUSED: '⏸️',
      GOAL_ACHIEVED: '🎯',
      MILESTONE_REACHED: '🏆',
      PAYMENT_DUE: '⏰',
      PAYMENT_OVERDUE: '⚠️',
      KYC_APPROVED: '✅',
      KYC_REJECTED: '❌',
      GROUP_INVITATION: '👥',
      GROUP_PAYOUT: '💸',
      SYSTEM: '⚙️',
      PROMOTIONAL: '🎁',
      SECURITY: '🔒',
      OTHER: '📢'
    };
    
    return icons[type] || '📢';
  }

  getPriorityColor(priority: string): string {
    const colors: Record<string, string> = {
      LOW: '#10B981',
      MEDIUM: '#F59E0B',
      HIGH: '#EF4444',
      URGENT: '#DC2626'
    };
    
    return colors[priority] || '#6B7280';
  }

  formatTimeAgo(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return 'Just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 604800) {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    } else {
      return date.toLocaleDateString();
    }
  }

  truncateMessage(message: string, maxLength: number = 100): string {
    if (message.length <= maxLength) return message;
    return message.substring(0, maxLength) + '...';
  }

  groupNotificationsByDate(notifications: Notification[]): Record<string, Notification[]> {
    return notifications.reduce((groups, notification) => {
      const date = new Date(notification.createdAt).toDateString();
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(notification);
      return groups;
    }, {} as Record<string, Notification[]>);
  }
}

export const notificationsService = new NotificationsService();
