"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Fi<PERSON><PERSON>s, 
  FiSearch, 
  FiFilter,
  FiEdit3,
  FiEye,
  FiUserX,
  FiUserCheck,
  FiDownload,
  FiRefreshCw,
  FiMoreVertical,
  FiMail,
  FiPhone,
  FiCalendar,
  FiDollarSign
} from 'react-icons/fi';
import AdminLayout from '../../../src/components/admin/AdminLayout';
import { adminService } from '../../../src/services';
import { AdminUserListItem, UserSearchFilters } from '../../../src/types';
import { Button } from '../../../src/components/ui/Button';
import { Card } from '../../../src/components/ui/Card';
import { Badge } from '../../../src/components/ui/Badge';
import { Input } from '../../../src/components/ui/Input';
import { Select } from '../../../src/components/ui/Select';
import Modal from '../../../src/components/ui/Modal';
import Table from '../../../src/components/ui/Table';
import { Pagination } from '../../../src/components/ui/Pagination'; // correct as is
import { showToast } from '../../../src/components/ui/Toast';

export default function AdminUsersPage() {
  const [users, setUsers] = useState<AdminUserListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedUser, setSelectedUser] = useState<AdminUserListItem | null>(null);
  const [showUserModal, setShowUserModal] = useState(false);
  const [showStatusModal, setShowStatusModal] = useState(false);
  
  const [filters, setFilters] = useState<UserSearchFilters>({
    search: '',
    status: undefined,
    kycStatus: undefined,
    dateFrom: '',
    dateTo: '',
    sortBy: 'createdAt',
    sortOrder: 'desc',
    page: 1,
    limit: 20
  });

  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 20,
    totalPages: 0
  });

  useEffect(() => {
    loadUsers();
  }, [filters]);

  const loadUsers = async () => {
    try {
      setLoading(true);
      const response = await adminService.getAllUsers(filters);
      // Filter out invalid user objects (null/undefined/missing required fields, and ensure string type)
      const validUsers = (response.users || []).filter((user: any) =>
        user &&
        typeof user === 'object' &&
        typeof user._id === 'string' &&
        typeof user.firstName === 'string' && user.firstName.trim() !== '' &&
        typeof user.lastName === 'string' && user.lastName.trim() !== '' &&
        typeof user.email === 'string' && user.email.trim() !== ''
      );
      setUsers(validUsers);
      setPagination({
        total: response.total,
        page: response.page,
        limit: response.limit,
        totalPages: response.totalPages
      });
    } catch (error) {
      showToast.error('Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const handleUserStatusChange = async (userId: string, isActive: boolean) => {
    try {
      await adminService.updateUserStatus(userId, isActive);
      setUsers(users.map(user => 
        user.id === userId ? { ...user, isActive } : user
      ));
      showToast.success(`User ${isActive ? 'activated' : 'deactivated'} successfully`);
      setShowStatusModal(false);
    } catch (error) {
      showToast.error('Failed to update user status');
    }
  };

  const handleResetPassword = async (userId: string) => {
    try {
      const response = await adminService.resetUserPassword(userId);
      showToast.success(`Password reset. Temporary password: ${response.temporaryPassword}`);
    } catch (error) {
      showToast.error('Failed to reset password');
    }
  };

  const handleExportUsers = async () => {
    try {
      // Implementation for exporting users
      showToast.success('User export started. You will receive an email when ready.');
    } catch (error) {
      showToast.error('Failed to export users');
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-NG');
  };

  const getKYCStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED': return 'success';
      case 'PENDING': return 'warning';
      case 'REJECTED': return 'error';
      default: return 'secondary';
    }
  };

  const getUserStatusColor = (isActive: boolean) => {
    return isActive ? 'success' : 'error';
  };

  const columns = [
    {
      key: 'user',
      label: 'User',
      render: (user: AdminUserListItem) => {
        // Defensive: fallback for missing fields
        const firstName = typeof user.firstName === 'string' ? user.firstName : '';
        const lastName = typeof user.lastName === 'string' ? user.lastName : '';
        const email = typeof user.email === 'string' ? user.email : '';
        return (
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center text-white font-semibold">
              {firstName.charAt(0)}{lastName.charAt(0)}
            </div>
            <div>
              <p className="font-medium text-white">{firstName} {lastName}</p>
              <p className="text-sm text-gray-400">{email}</p>
            </div>
          </div>
        );
      }
    },
    {
      key: 'contact',
      label: 'Contact',
      render: (user: AdminUserListItem) => (
        <div className="space-y-1">
          <div className="flex items-center text-sm text-gray-400">
            <FiMail className="mr-1" />
            {user.email}
          </div>
          {user.phoneNumber && (
            <div className="flex items-center text-sm text-gray-400">
              <FiPhone className="mr-1" />
              {user.phoneNumber}
            </div>
          )}
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (user: AdminUserListItem) => (
        <div className="space-y-1">
          <Badge variant={getUserStatusColor(user.isActive)}>
            {user.isActive ? 'Active' : 'Inactive'}
          </Badge>
          <Badge variant={getKYCStatusColor(user.kycStatus)}>
            KYC {user.kycStatus}
          </Badge>
        </div>
      )
    },
    {
      key: 'balance',
      label: 'Balance',
      render: (user: AdminUserListItem) => (
        <div className="text-right">
          <p className="font-medium text-white">{formatCurrency(user.balance)}</p>
          <p className="text-sm text-gray-400">Savings: {formatCurrency(user.totalSavings)}</p>
        </div>
      )
    },
    {
      key: 'joined',
      label: 'Joined',
      render: (user: AdminUserListItem) => (
        <div className="text-sm text-gray-400">
          <div className="flex items-center">
            <FiCalendar className="mr-1" />
            {formatDate(user.createdAt)}
          </div>
          {user.lastLoginAt && (
            <p className="mt-1">Last: {formatDate(user.lastLoginAt)}</p>
          )}
        </div>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (user: AdminUserListItem) => (
        <div className="flex items-center space-x-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => {
              setSelectedUser(user);
              setShowUserModal(true);
            }}
          >
            <FiEye />
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => {
              setSelectedUser(user);
              setShowStatusModal(true);
            }}
          >
            {user.isActive ? <FiUserX /> : <FiUserCheck />}
          </Button>
        </div>
      )
    }
  ];

  return (
    <AdminLayout title="User Management">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white">User Management</h1>
            <p className="text-gray-400 mt-2">Manage platform users and their accounts</p>
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={handleExportUsers}
            >
              <FiDownload className="mr-2" />
              Export
            </Button>
            <Button
              variant="outline"
              onClick={loadUsers}
            >
              <FiRefreshCw className="mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card className="bg-gray-800 border-gray-700 p-6">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <Input
              placeholder="Search users..."
              value={filters.search}
              onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              icon={<FiSearch />}
            />

            <Select
              value={filters.status || ''}
              onChange={(value) => setFilters({ ...filters, status: value || undefined })}
              options={[
                { value: '', label: 'All Status' },
                { value: 'ACTIVE', label: 'Active' },
                { value: 'INACTIVE', label: 'Inactive' }
              ]}
            />

            <Select
              value={filters.kycStatus || ''}
              onChange={(value) => setFilters({ ...filters, kycStatus: value || undefined })}
              options={[
                { value: '', label: 'All KYC Status' },
                { value: 'PENDING', label: 'KYC Pending' },
                { value: 'APPROVED', label: 'KYC Approved' },
                { value: 'REJECTED', label: 'KYC Rejected' }
              ]}
            />

            <Input
              type="date"
              value={filters.dateFrom}
              onChange={(e) => setFilters({ ...filters, dateFrom: e.target.value })}
              placeholder="From Date"
            />

            <Input
              type="date"
              value={filters.dateTo}
              onChange={(e) => setFilters({ ...filters, dateTo: e.target.value })}
              placeholder="To Date"
            />
          </div>
        </Card>

        {/* Users Table */}
        <Card className="bg-gray-800 border-gray-700">
          <div className="p-6 border-b border-gray-700">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold text-white">
                Users ({(pagination.total ?? 0).toLocaleString()})
              </h3>
              <div className="flex items-center space-x-2">
                <Select
                  value={filters.sortBy}
                  onChange={(value) => setFilters({ ...filters, sortBy: value as any })}
                  options={[
                    { value: 'createdAt', label: 'Date Joined' },
                    { value: 'firstName', label: 'Name' },
                    { value: 'balance', label: 'Balance' },
                    { value: 'lastLoginAt', label: 'Last Login' }
                  ]}
                />
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setFilters({ 
                    ...filters, 
                    sortOrder: filters.sortOrder === 'asc' ? 'desc' : 'asc' 
                  })}
                >
                  {filters.sortOrder === 'asc' ? '↑' : '↓'}
                </Button>
              </div>
            </div>
          </div>

          <Table
            columns={columns}
            data={users.filter(user =>
              user &&
              typeof user === 'object' &&
              typeof user.firstName === 'string' && user.firstName.trim() !== '' &&
              typeof user.lastName === 'string' && user.lastName.trim() !== '' &&
              typeof user.email === 'string' && user.email.trim() !== ''
            )}
            loading={loading}
            emptyMessage="No users found"
          />

          <div className="p-6 border-t border-gray-700">
            <Pagination
              currentPage={pagination.page}
              totalPages={pagination.totalPages}
              onPageChange={(page) => setFilters({ ...filters, page })}
              showInfo={true}
              totalItems={pagination.total}
              itemsPerPage={pagination.limit}
            />
          </div>
        </Card>
      </div>

      {/* User Details Modal */}
      <Modal
        isOpen={showUserModal}
        onClose={() => setShowUserModal(false)}
        title="User Details"
        size="lg"
      >
        {selectedUser && (
          <div className="space-y-6">
            {/* User Info */}
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center text-white text-xl font-bold">
                {selectedUser.firstName.charAt(0)}{selectedUser.lastName.charAt(0)}
              </div>
              <div>
                <h3 className="text-xl font-semibold text-white">
                  {selectedUser.firstName} {selectedUser.lastName}
                </h3>
                <p className="text-gray-400">{selectedUser.email}</p>
                <div className="flex space-x-2 mt-2">
                  <Badge variant={getUserStatusColor(selectedUser.isActive)}>
                    {selectedUser.isActive ? 'Active' : 'Inactive'}
                  </Badge>
                  <Badge variant={getKYCStatusColor(selectedUser.kycStatus)}>
                    KYC {selectedUser.kycStatus}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Financial Info */}
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center p-4 bg-gray-700 rounded-lg">
                <p className="text-gray-400 text-sm">Balance</p>
                <p className="text-lg font-semibold text-white">
                  {formatCurrency(selectedUser.balance)}
                </p>
              </div>
              <div className="text-center p-4 bg-gray-700 rounded-lg">
                <p className="text-gray-400 text-sm">Total Savings</p>
                <p className="text-lg font-semibold text-white">
                  {formatCurrency(selectedUser.totalSavings)}
                </p>
              </div>
              <div className="text-center p-4 bg-gray-700 rounded-lg">
                <p className="text-gray-400 text-sm">Total Earnings</p>
                <p className="text-lg font-semibold text-white">
                  {formatCurrency(selectedUser.totalEarnings)}
                </p>
              </div>
            </div>

            {/* Account Details */}
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Phone Number
                  </label>
                  <p className="text-white">{selectedUser.phoneNumber || 'Not provided'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Date Joined
                  </label>
                  <p className="text-white">{formatDate(selectedUser.createdAt)}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Last Login
                  </label>
                  <p className="text-white">
                    {selectedUser.lastLoginAt ? formatDate(selectedUser.lastLoginAt) : 'Never'}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Email Verified
                  </label>
                  <Badge variant={selectedUser.isVerified ? 'success' : 'warning'}>
                    {selectedUser.isVerified ? 'Verified' : 'Unverified'}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-700">
              <Button
                variant="outline"
                onClick={() => handleResetPassword(selectedUser.id)}
              >
                Reset Password
              </Button>
              <Button
                variant={selectedUser.isActive ? 'error' : 'success'}
                onClick={() => {
                  setShowUserModal(false);
                  setShowStatusModal(true);
                }}
              >
                {selectedUser.isActive ? 'Deactivate User' : 'Activate User'}
              </Button>
            </div>
          </div>
        )}
      </Modal>

      {/* Status Change Confirmation Modal */}
      <Modal
        isOpen={showStatusModal}
        onClose={() => setShowStatusModal(false)}
        title="Confirm Status Change"
      >
        {selectedUser && (
          <div className="space-y-4">
            <p className="text-gray-300">
              Are you sure you want to {selectedUser.isActive ? 'deactivate' : 'activate'} this user?
            </p>
            <p className="text-sm text-gray-400">
              User: {selectedUser.firstName} {selectedUser.lastName} ({selectedUser.email})
            </p>
            
            <div className="flex justify-end space-x-3 pt-4">
              <Button
                variant="outline"
                onClick={() => setShowStatusModal(false)}
              >
                Cancel
              </Button>
              <Button
                variant={selectedUser.isActive ? 'error' : 'success'}
                onClick={() => handleUserStatusChange(selectedUser.id, !selectedUser.isActive)}
              >
                {selectedUser.isActive ? 'Deactivate' : 'Activate'}
              </Button>
            </div>
          </div>
        )}
      </Modal>
    </AdminLayout>
  );
}
