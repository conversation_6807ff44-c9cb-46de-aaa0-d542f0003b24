const express = require('express');
const User = require('../models/user');
const router = express.Router();
const KYC = require('../models/kyc');
const Withdraw = require('../models/withdraw');
const SavingsPlan = require('../models/savingsPlan');
const GroupSavingsPlan = require('../models/groupSavingsPlan');
const RotationalGroupSavings = require('../models/rotationalGroupSavings');
const TargetSavings = require('../models/targetSavings');

const { authenticateToken, requireAdmin } = require('../middleware/authMiddleware');

// GET /api/admin/dashboard/logs (protected) - returns recent system and audit logs for dashboard activity feed
router.get('/dashboard/logs', authenticateToken, requireAdmin, async (req, res) => {
  try {
    // For demo, combine a few from system logs and audit logs (mocked)
    const now = new Date();
    const logs = [
      {
        type: 'user',
        message: 'New user registration: <EMAIL>',
        time: '2 minutes ago',
        level: 'info',
        icon: 'FiUsers',
      },
      {
        type: 'withdrawal',
        message: 'Withdrawal request pending approval: ₦50,000',
        time: '5 minutes ago',
        level: 'warn',
        icon: 'FiCreditCard',
      },
      {
        type: 'kyc',
        message: 'KYC document submitted for review',
        time: '10 minutes ago',
        level: 'info',
        icon: 'FiShield',
      },
      {
        type: 'system',
        message: 'System backup completed successfully',
        time: '15 minutes ago',
        level: 'success',
        icon: 'FiCheckCircle',
      },
      {
        type: 'alert',
        message: 'High transaction volume detected',
        time: '20 minutes ago',
        level: 'error',
        icon: 'FiAlertTriangle',
      },
    ];
    res.json({ logs });
  } catch (err) {
    res.status(500).json({ error: 'Failed to load dashboard logs', details: err.message });
  }
});
// GET /api/admin/dashboard/stats (protected, supports ?period=week|month|year)
router.get('/dashboard/stats', authenticateToken, requireAdmin, async (req, res) => {
  try {
    // Parse period (default: month)
    const period = req.query.period || 'month';
    let dateFrom;
    const now = new Date();
    if (period === 'week') {
      dateFrom = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7);
    } else if (period === 'year') {
      dateFrom = new Date(now.getFullYear(), 0, 1);
    } else { // month
      dateFrom = new Date(now.getFullYear(), now.getMonth(), 1);
    }

    // USERS
    const totalUsers = await User.countDocuments();
    const activeUsers = await User.countDocuments({ lastLogin: { $gte: dateFrom } });
    const newThisMonth = await User.countDocuments({ createdAt: { $gte: dateFrom } });
    const kycPending = await User.countDocuments({ kycStatus: 'PENDING' });
    const kycApproved = await User.countDocuments({ kycStatus: 'APPROVED' });
    const kycRejected = await User.countDocuments({ kycStatus: 'REJECTED' });

    // SAVINGS
    const totalPlans = await SavingsPlan.countDocuments();
    const activePlans = await SavingsPlan.countDocuments({}); // TODO: add status if available
    const completedPlans = 0; // Not tracked in schema
    const totalSavingsAmount = await SavingsPlan.aggregate([{ $group: { _id: null, total: { $sum: '$savedAmount' } } }]);
    const totalInterestPaid = 0; // Not tracked in schema
    const averagePlanValue = totalSavingsAmount[0]?.total ? totalSavingsAmount[0].total / (totalPlans || 1) : 0;

    // GROUPS
    const totalGroups = await GroupSavingsPlan.countDocuments();
    const activeGroups = await GroupSavingsPlan.countDocuments({ status: 'ACTIVE' });
    const groupMembersAgg = await GroupSavingsPlan.aggregate([{ $project: { membersCount: { $size: { $ifNull: ['$members', []] } } } }, { $group: { _id: null, total: { $sum: '$membersCount' } } }]);
    const totalMembers = groupMembersAgg[0]?.total || 0;
    const totalContributions = await GroupSavingsPlan.aggregate([{ $group: { _id: null, total: { $sum: '$savedAmount' } } }]);
    const completionRate = 0; // Not tracked in schema

    // TRANSACTIONS
    // For demo, treat Withdraw as withdrawals, SavingsPlan as deposits
    const totalTransactions = await Withdraw.countDocuments({ createdAt: { $gte: dateFrom } });
    const totalVolumeAgg = await Withdraw.aggregate([{ $match: { createdAt: { $gte: dateFrom } } }, { $group: { _id: null, total: { $sum: '$amount' } } }]);
    const totalVolume = totalVolumeAgg[0]?.total || 0;
    const depositsThisMonth = await SavingsPlan.aggregate([{ $match: { createdAt: { $gte: dateFrom } } }, { $group: { _id: null, total: { $sum: '$savedAmount' } } }]);
    const withdrawalsThisMonth = await Withdraw.aggregate([{ $match: { createdAt: { $gte: dateFrom } } }, { $group: { _id: null, total: { $sum: '$amount' } } }]);
    const successRate = 100; // Not tracked in schema
    const averageTransactionValue = totalVolume / (totalTransactions || 1);

    // DEPOSITS
    const totalDeposits = await SavingsPlan.countDocuments();
    const totalDepositAmountAgg = await SavingsPlan.aggregate([{ $group: { _id: null, total: { $sum: '$savedAmount' } } }]);
    const totalDepositAmount = totalDepositAmountAgg[0]?.total || 0;
    const pendingDeposits = 0; // Not tracked in schema
    const failedDeposits = 0; // Not tracked in schema
    const depositSuccessRate = 100; // Not tracked in schema
    const averageDepositAmount = totalDepositAmount / (totalDeposits || 1);

    // WITHDRAWALS
    const totalWithdrawals = await Withdraw.countDocuments();
    const totalWithdrawalAmountAgg = await Withdraw.aggregate([{ $group: { _id: null, total: { $sum: '$amount' } } }]);
    const totalWithdrawalAmount = totalWithdrawalAmountAgg[0]?.total || 0;
    const pendingWithdrawals = await Withdraw.countDocuments({ paystackStatus: 'pending' });
    const pendingWithdrawalAmountAgg = await Withdraw.aggregate([{ $match: { paystackStatus: 'pending' } }, { $group: { _id: null, total: { $sum: '$amount' } } }]);
    const pendingWithdrawalAmount = pendingWithdrawalAmountAgg[0]?.total || 0;
    const totalPenalties = 0; // Not tracked in schema
    const averageProcessingTime = 0; // Not tracked in schema

    // KYC
    const totalSubmissions = await User.countDocuments({ kycStatus: { $ne: 'NOT_SUBMITTED' } });
    const pendingReview = kycPending;
    const approvedThisMonth = await User.countDocuments({ kycStatus: 'APPROVED', updatedAt: { $gte: dateFrom } });
    const rejectedThisMonth = await User.countDocuments({ kycStatus: 'REJECTED', updatedAt: { $gte: dateFrom } });
    const averageReviewTime = 0; // Not tracked in schema

    // NOTIFICATIONS (not tracked)
    const notifications = {
      totalSent: 0,
      sentThisMonth: 0,
      deliveryRate: 0,
      unreadCount: 0,
    };

    res.json({
      users: {
        total: totalUsers,
        active: activeUsers,
        newThisMonth,
        kycPending,
        kycApproved,
        kycRejected,
      },
      savings: {
        totalPlans,
        activePlans,
        completedPlans,
        totalSavingsAmount: totalSavingsAmount[0]?.total || 0,
        totalInterestPaid,
        averagePlanValue,
      },
      groups: {
        totalGroups,
        activeGroups,
        totalMembers,
        totalContributions: totalContributions[0]?.total || 0,
        completionRate,
      },
      transactions: {
        totalTransactions,
        totalVolume,
        depositsThisMonth: depositsThisMonth[0]?.total || 0,
        withdrawalsThisMonth: withdrawalsThisMonth[0]?.total || 0,
        successRate,
        averageTransactionValue,
      },
      deposits: {
        totalDeposits,
        totalAmount: totalDepositAmount,
        pendingDeposits,
        failedDeposits,
        successRate: depositSuccessRate,
        averageDepositAmount,
      },
      withdrawals: {
        totalWithdrawals,
        totalAmount: totalWithdrawalAmount,
        pendingWithdrawals,
        pendingAmount: pendingWithdrawalAmount,
        totalPenalties,
        averageProcessingTime,
      },
      kyc: {
        totalSubmissions,
        pendingReview,
        approvedThisMonth,
        rejectedThisMonth,
        averageReviewTime,
      },
      notifications,
    });
  } catch (err) {
    res.status(500).json({ error: 'Failed to load admin dashboard stats', details: err.message });
  }
});

module.exports = router;
