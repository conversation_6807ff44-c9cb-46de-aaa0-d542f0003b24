"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function SavingsPlansPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the new savings plans page in dashboard
    router.push("/dashboard/savings-plans");
  }, [router]);

  return (
    <div className="min-h-screen bg-black flex items-center justify-center">
      <div className="text-center">
        <p className="text-green-400 text-lg">
          Redirecting to Savings Plans...
        </p>
      </div>
    </div>
  );
}
