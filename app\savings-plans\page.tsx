"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "../../src/hooks/use-auth";
import SavingsPlans from "../../src/pages/user/SavingsPlans";
import { LoadingLogo } from "../../components/LoadingLogo";

export default function SavingsPlansPage() {
  const { isAuthenticated, isLoading, user } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading) {
      if (!isAuthenticated) {
        router.push('/login');
        return;
      }

      // Redirect admin users to admin dashboard
      if (user?.role === 'ADMIN') {
        router.push('/admin/dashboard');
        return;
      }
    }
  }, [isAuthenticated, isLoading, user, router]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <LoadingLogo size={64} showPulse={true} />
          <p className="text-green-400 mt-4 text-lg">Loading Savings Plans...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect in useEffect
  }

  if (user?.role === 'ADMIN') {
    return null; // Will redirect in useEffect
  }

  return <SavingsPlans />;
}
