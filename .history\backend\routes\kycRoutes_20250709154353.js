const express = require('express');
const KYC = require('../models/kyc');
const Notification = require('../models/notification');
const multer = require('multer');
const path = require('path');
const bodyParser = require('body-parser'); // Add body-parser for parsing form fields
const { authenticateToken, requireAdmin } = require('../middleware/authMiddleware');
const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, path.join(__dirname, '../../public/lovable-uploads'));
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = `${Date.now()}-${Math.round(Math.random() * 1e9)}`;
    cb(null, `${uniqueSuffix}-${file.originalname}`);
  },
});

const upload = multer({ storage });

// Add middleware to parse form fields
router.use(bodyParser.urlencoded({ extended: true }));
router.use(bodyParser.json());

// Submit KYC information
router.post('/submit', authenticateToken, upload.fields([
  { name: 'idFront', maxCount: 1 },
  { name: 'idBack', maxCount: 1 },
  { name: 'addressProof', maxCount: 1 },
  { name: 'selfie', maxCount: 1 },
]), (req, res, next) => {
  console.log('--- Raw Incoming Request ---');
  console.log('Headers:', req.headers);
  console.log('Body:', req.body);

  // Normalize form data
  if (req.body.gender) {
    req.body.gender = req.body.gender.charAt(0).toUpperCase() + req.body.gender.slice(1).toLowerCase();
  }
  if (req.body.idType) {
    const idTypeMap = {
      'national_id': 'National ID',
      'international_passport': 'International Passport',
      'drivers_license': "Driver's License",
      'voters_card': "Voter's Card",
    };
    req.body.idType = idTypeMap[req.body.idType.toLowerCase()] || req.body.idType;
  }
  if (req.body.nationality) {
    req.body.nationality = req.body.nationality.charAt(0).toUpperCase() + req.body.nationality.slice(1).toLowerCase();
  }

  next();
}, async (req, res) => {
  try {
    console.log('--- Incoming KYC Submission Request ---');
    console.log('Headers:', req.headers);
    console.log('Body:', req.body);
    console.log('Files:', req.files);

    const kycData = req.body;

    // Add file paths to the KYC data
    if (req.files.idFront) {
      kycData.idFront = `/lovable-uploads/${req.files.idFront[0].filename}`;
    }
    if (req.files.idBack) {
      kycData.idBack = `/lovable-uploads/${req.files.idBack[0].filename}`;
    }
    if (req.files.addressProof) {
      kycData.addressProof = `/lovable-uploads/${req.files.addressProof[0].filename}`;
    }
    if (req.files.selfie) {
      kycData.selfie = `/lovable-uploads/${req.files.selfie[0].filename}`;
    }

    // Always use the authenticated user's ID
    kycData.userId = req.user.id;

    // Validate required fields
    const requiredFields = ['firstName', 'lastName', 'dob', 'gender', 'idType', 'idNumber', 'nationality', 'address'];
    for (const field of requiredFields) {
      if (!req.body[field]) {
        return res.status(400).json({ error: `Field '${field}' is required.` });
      }
    }
    // Validate file uploads
    if (!req.files || !req.files.idFront || !req.files.idBack || !req.files.addressProof || !req.files.selfie) {
      return res.status(400).json({ error: 'All KYC images (ID front, ID back, address proof, selfie) are required.' });
    }

    console.log('Final KYC Data:', kycData);

    const newKYC = new KYC(kycData);
    const savedKYC = await newKYC.save();
    // Notify user of KYC submission
    await Notification.create({
      userId: savedKYC.userId,
      type: 'kyc_requested',
      title: 'KYC Submitted',
      message: 'Your KYC submission has been received and is pending review.',
    });
    res.status(201).json(savedKYC);
  } catch (error) {
    res.status(500).json({ error: 'Failed to submit KYC', details: error.message });
  }
});

// Get KYC status
router.get('/status/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const kycRecord = await KYC.findOne({ userId });

    if (!kycRecord) {
      return res.status(404).json({ error: 'KYC record not found' });
    }

    res.status(200).json({
      userId: kycRecord.userId,
      kycStatus: kycRecord.kycStatus,
      message: 'KYC status retrieved successfully',
    });
  } catch (error) {
    console.error('Error fetching KYC status:', error);
    res.status(500).json({ error: 'Failed to fetch KYC status', details: error.message });
  }
});

// Get KYC status for the logged-in user (for sidebar badge)
router.get('/status', authenticateToken, async (req, res) => {
  try {
    if (!req.user || !req.user.id) {
      return res.status(401).json({ error: 'User authentication failed. Please log in again.' });
    }
    const userId = req.user.id;
    const kyc = await require('../models/kyc').findOne({ userId });
    if (!kyc) {
      return res.status(200).json({ status: 'not_submitted' });
    }
    res.status(200).json({ status: kyc.kycStatus });
  } catch (error) {
    console.error('Error fetching KYC status:', error);
    res.status(500).json({ error: 'Failed to fetch KYC status', details: error.message });
  }
});

// Update KYC status
router.put('/status/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const { kycStatus } = req.body;
    const updatedKYC = await KYC.findOneAndUpdate(
      { userId },
      { kycStatus },
      { new: true }
    );
    if (!updatedKYC) {
      return res.status(404).json({ error: 'KYC record not found' });
    }
    res.status(200).json(updatedKYC);
  } catch (error) {
    console.error('Error updating KYC status:', error);
    res.status(500).json({ error: 'Failed to update KYC status', details: error.message });
  }
});

// Admin: Get all KYC submissions
router.get('/admin/kyc', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '' } = req.query;


    // Convert page and limit to integers
    const pageNumber = parseInt(page, 10);
    const limitNumber = parseInt(limit, 10);

    // Build the search query
    const searchQuery = search
      ? {
          $or: [
            { userId: { $regex: search, $options: 'i' } },
            { kycStatus: { $regex: search, $options: 'i' } },
          ],
        }
      : {};

    // Fetch KYC submissions with pagination and search
    const kycSubmissions = await KYC.find(searchQuery)
      .skip((pageNumber - 1) * limitNumber)
      .limit(limitNumber);

    // Get total count for pagination
    const totalSubmissions = await KYC.countDocuments(searchQuery);


    res.json({
      kycSubmissions,
      totalSubmissions,
      totalPages: Math.ceil(totalSubmissions / limitNumber),
      currentPage: pageNumber,
    });
  } catch (error) {
    console.error('Error fetching KYC submissions:', error);
    res.status(500).json({ error: 'Failed to fetch KYC submissions' });
  }
});

// Admin: Delete a KYC submission
router.delete('/admin/kyc/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const deletedKYC = await KYC.findByIdAndDelete(id);

    if (!deletedKYC) {
      return res.status(404).json({ error: 'KYC submission not found' });
    }

    res.status(200).json({ message: 'KYC submission deleted successfully' });
  } catch (error) {
    console.error('Error deleting KYC submission:', error);
    res.status(500).json({ error: 'Failed to delete KYC submission' });
  }
});

// Admin: Update a KYC submission
router.put('/admin/kyc/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    const updatedKYC = await KYC.findByIdAndUpdate(id, updates, { new: true });

    if (!updatedKYC) {
      return res.status(404).json({ error: 'KYC submission not found' });
    }

    res.status(200).json(updatedKYC);
  } catch (error) {
    console.error('Error updating KYC submission:', error);
    res.status(500).json({ error: 'Failed to update KYC submission' });
  }
});

// Admin: Approve a KYC submission
const User = require('../models/user');
router.put('/admin/kyc/:id/approve', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const updatedKYC = await KYC.findByIdAndUpdate(
      id,
      { 
        kycStatus: 'APPROVED',
        reviewedAt: new Date(),
        reviewedBy: req.user?._id
      },
      { new: true }
    );
    if (!updatedKYC) {
      return res.status(404).json({ error: 'KYC submission not found' });
    }
    // Update user KYC status and type
    await User.findByIdAndUpdate(
      updatedKYC.userId,
      {
        kycStatus: 'APPROVED',
        kycType: updatedKYC.idType === 'National ID' || updatedKYC.idType === 'NIN' ? 'NIN' : 'BVN',
        kycResult: updatedKYC,
      }
    );
    // Notify user of KYC approval
    await Notification.create({
      userId: updatedKYC.userId,
      type: 'kyc_approved',
      title: 'KYC Approved',
      message: 'Your KYC verification has been approved. You now have full access to all features.',
    });
    res.status(200).json(updatedKYC);
  } catch (error) {
    res.status(500).json({ error: 'Failed to approve KYC submission', details: error.message });
  }
});

// Admin: Reject a KYC submission
const User = require('../models/user'); // Ensure User model is available
router.put('/admin/kyc/:id/reject', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;
    if (!reason) {
      return res.status(400).json({ error: 'Rejection reason is required' });
    }
    const updatedKYC = await KYC.findByIdAndUpdate(
      id,
      { 
        kycStatus: 'REJECTED',
        rejectionReason: reason,
        reviewedAt: new Date(),
        reviewedBy: req.user?._id
      },
      { new: true }
    );
    if (!updatedKYC) {
      return res.status(404).json({ error: 'KYC submission not found' });
    }
    // Update user KYC status to REJECTED
    await User.findByIdAndUpdate(
      updatedKYC.userId,
      {
        kycStatus: 'REJECTED',
        kycType: null,
        kycResult: updatedKYC,
      }
    );
    // Notify user of KYC rejection
    await Notification.create({
      userId: updatedKYC.userId,
      type: 'kyc_rejected',
      title: 'KYC Rejected',
      message: `Your KYC verification was rejected. Reason: ${reason}`,
    });
    res.status(200).json(updatedKYC);
  } catch (error) {
    res.status(500).json({ error: 'Failed to reject KYC submission', details: error.message });
  }
});

// Admin: Get count of all pending KYC verifications
router.get('/pending-count', async (req, res) => {
  try {
    const count = await KYC.countDocuments({ status: 'pending' });
    res.json({ count });
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch pending KYC count' });
  }
});

module.exports = router;
