"use client";

import React, { forwardRef } from 'react';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  variant?: 'default' | 'filled' | 'outline';
}

export const Input = forwardRef<HTMLInputElement, InputProps>(({
  label,
  error,
  helperText,
  leftIcon,
  rightIcon,
  variant = 'default',
  className = '',
  ...props
}, ref) => {
  const baseClasses = `
    w-full px-4 py-3 rounded-lg transition-all duration-200
    focus:outline-none focus:ring-2 focus:ring-green-500/50
    disabled:opacity-50 disabled:cursor-not-allowed
    ${leftIcon ? 'pl-12' : ''}
    ${rightIcon ? 'pr-12' : ''}
  `;

  const variantClasses = {
    default: `
      bg-gray-800 border border-gray-700 text-white
      placeholder-gray-400 hover:border-gray-600
      focus:border-green-500
    `,
    filled: `
      bg-gray-700 border-0 text-white
      placeholder-gray-400 hover:bg-gray-600
    `,
    outline: `
      bg-transparent border-2 border-gray-600 text-white
      placeholder-gray-400 hover:border-gray-500
      focus:border-green-500
    `
  };

  const inputClasses = `${baseClasses} ${variantClasses[variant]} ${className} ${
    error ? 'border-red-500 focus:border-red-500 focus:ring-red-500/50' : ''
  }`;

  return (
    <div className="w-full">
      {label && (
        <label className="block text-sm font-medium text-gray-300 mb-2">
          {label}
        </label>
      )}
      
      <div className="relative">
        {leftIcon && (
          <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400">
            {leftIcon}
          </div>
        )}
        
        <input
          ref={ref}
          className={inputClasses}
          {...props}
        />
        
        {rightIcon && (
          <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400">
            {rightIcon}
          </div>
        )}
      </div>
      
      {error && (
        <p className="mt-1 text-sm text-red-400">
          {error}
        </p>
      )}
      
      {helperText && !error && (
        <p className="mt-1 text-sm text-gray-500">
          {helperText}
        </p>
      )}
    </div>
  );
});

Input.displayName = 'Input';

export default Input;
