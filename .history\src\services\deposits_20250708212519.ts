import { 
  Deposit, 
  InitiateDepositData,
  InitiateDepositResponse,
  VerifyDepositData,
  VerifyDepositResponse,
  DepositStats,
  DepositSearchFilters,
  PaginatedDepositResponse,
  PaystackWebhookData,
  ApiResponse
} from '../types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

class DepositsService {
  private getAuthHeaders() {
    const token = localStorage.getItem('auth_token');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` })
    };
  }

  // Deposit Initiation
  async initiateDeposit(data: InitiateDepositData): Promise<InitiateDepositResponse> {
    const response = await fetch(`${API_BASE_URL}/api/deposit/initiate`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to initiate deposit');
    }

    return response.json();
  }

  async verifyDeposit(data: VerifyDepositData): Promise<VerifyDepositResponse> {
    const response = await fetch(`${API_BASE_URL}/api/deposit/verify`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to verify deposit');
    }

    return response.json();
  }

  // Deposit Management
  async getUserDeposits(filters?: DepositSearchFilters): Promise<PaginatedDepositResponse> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/api/deposit/user?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch user deposits');
    }

    return response.json();
  }

  async getDepositById(depositId: string): Promise<Deposit> {
    const response = await fetch(`${API_BASE_URL}/api/deposit/${depositId}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch deposit');
    }

    return response.json();
  }

  async cancelDeposit(depositId: string): Promise<Deposit> {
    const response = await fetch(`${API_BASE_URL}/api/deposit/${depositId}/cancel`, {
      method: 'POST',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to cancel deposit');
    }

    return response.json();
  }

  // Admin Deposit Management
  async getAllDeposits(filters?: DepositSearchFilters): Promise<PaginatedDepositResponse> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/api/deposit/all?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch all deposits');
    }

    return response.json();
  }

  async updateDepositStatus(
    depositId: string, 
    status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'CANCELLED',
    adminNotes?: string
  ): Promise<Deposit> {
    const response = await fetch(`${API_BASE_URL}/api/deposit/${depositId}/status`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ status, adminNotes })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update deposit status');
    }

    return response.json();
  }

  // Statistics and Analytics
  async getDepositStats(filters?: {
    dateFrom?: string;
    dateTo?: string;
    userId?: string;
    paymentMethod?: string;
  }): Promise<DepositStats> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/api/deposit/stats?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch deposit statistics');
    }

    return response.json();
  }

  async getDepositAnalytics(period: 'daily' | 'weekly' | 'monthly' | 'yearly' = 'monthly'): Promise<any> {
    const response = await fetch(`${API_BASE_URL}/api/deposit/analytics?period=${period}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch deposit analytics');
    }

    return response.json();
  }

  // Payment Methods
  async getPaymentMethods(): Promise<any[]> {
    const response = await fetch(`${API_BASE_URL}/api/deposit/payment-methods`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch payment methods');
    }

    return response.json();
  }

  async getBanks(): Promise<any[]> {
    const response = await fetch(`${API_BASE_URL}/api/deposit/banks`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch banks');
    }

    return response.json();
  }

  // Webhook Handling (for admin use)
  async handlePaystackWebhook(webhookData: PaystackWebhookData): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/api/deposit/webhook/paystack`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(webhookData)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to handle webhook');
    }
  }

  // Utility Methods
  calculateFees(amount: number, paymentMethod: string): { fees: number; netAmount: number } {
    // Default fee structure - should be configurable
    const feeRates: Record<string, { percentage: number; fixed: number; cap?: number }> = {
      CARD: { percentage: 1.5, fixed: 100, cap: 2000 },
      BANK_TRANSFER: { percentage: 0.5, fixed: 50, cap: 1000 },
      USSD: { percentage: 1.0, fixed: 50 },
      QR_CODE: { percentage: 1.0, fixed: 50 },
      MOBILE_MONEY: { percentage: 1.5, fixed: 100 }
    };

    const feeStructure = feeRates[paymentMethod] || feeRates.CARD;
    let fees = (amount * feeStructure.percentage / 100) + feeStructure.fixed;
    
    if (feeStructure.cap && fees > feeStructure.cap) {
      fees = feeStructure.cap;
    }

    return {
      fees: Math.round(fees),
      netAmount: amount - Math.round(fees)
    };
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN'
    }).format(amount);
  }

  getPaymentMethodIcon(method: string): string {
    const icons: Record<string, string> = {
      CARD: '💳',
      BANK_TRANSFER: '🏦',
      USSD: '📱',
      QR_CODE: '📱',
      MOBILE_MONEY: '📱'
    };
    
    return icons[method] || '💳';
  }

  getStatusColor(status: string): string {
    const colors: Record<string, string> = {
      PENDING: '#F59E0B',
      PROCESSING: '#3B82F6',
      COMPLETED: '#10B981',
      FAILED: '#EF4444',
      CANCELLED: '#6B7280'
    };
    
    return colors[status] || '#6B7280';
  }

  generateReference(): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8);
    return `DEP_${timestamp}_${random}`.toUpperCase();
  }

  validateAmount(amount: number, paymentMethod: string): { isValid: boolean; error?: string } {
    const limits: Record<string, { min: number; max: number }> = {
      CARD: { min: 100, max: 1000000 },
      BANK_TRANSFER: { min: 100, max: 5000000 },
      USSD: { min: 100, max: 100000 },
      QR_CODE: { min: 100, max: 100000 },
      MOBILE_MONEY: { min: 100, max: 500000 }
    };

    const limit = limits[paymentMethod] || limits.CARD;

    if (amount < limit.min) {
      return { 
        isValid: false, 
        error: `Minimum amount for ${paymentMethod} is ${this.formatCurrency(limit.min)}` 
      };
    }

    if (amount > limit.max) {
      return { 
        isValid: false, 
        error: `Maximum amount for ${paymentMethod} is ${this.formatCurrency(limit.max)}` 
      };
    }

    return { isValid: true };
  }

  // Get total successful deposits for a user
  async getUserTotalDeposits(userId: string): Promise<number> {
    const response = await fetch(`${API_BASE_URL}/api/deposit/user/${userId}/total`, {
      headers: this.getAuthHeaders(),
    });
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch total deposits');
    }
    const data = await response.json();
    return data.total ?? 0;
  }
}

export const depositsService = new DepositsService();
