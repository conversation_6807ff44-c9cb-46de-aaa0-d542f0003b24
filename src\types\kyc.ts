export interface KYC {
  id: string;
  userId: string;
  user?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber?: string;
  };
  status: 'PENDING' | 'UNDER_REVIEW' | 'APPROVED' | 'REJECTED' | 'EXPIRED';
  level: 'BASIC' | 'INTERMEDIATE' | 'ADVANCED';
  
  // Personal Information
  personalInfo: {
    firstName: string;
    lastName: string;
    middleName?: string;
    dateOfBirth: string;
    gender: 'MALE' | 'FEMALE' | 'OTHER';
    nationality: string;
    placeOfBirth?: string;
    maritalStatus?: 'SINGLE' | 'MARRIED' | 'DIVORCED' | 'WIDOWED';
    occupation?: string;
    employer?: string;
    monthlyIncome?: number;
    sourceOfIncome?: string;
  };
  
  // Address Information
  addressInfo: {
    street: string;
    city: string;
    state: string;
    country: string;
    postalCode?: string;
    isResidential: boolean;
    residenceDuration?: number; // in months
  };
  
  // Contact Information
  contactInfo: {
    phoneNumber: string;
    alternatePhoneNumber?: string;
    email: string;
    alternateEmail?: string;
  };
  
  // Documents
  documents: KYCDocument[];
  
  // Next of Kin
  nextOfKin?: {
    firstName: string;
    lastName: string;
    relationship: string;
    phoneNumber: string;
    email?: string;
    address: string;
  };
  
  // Review Information
  reviewedBy?: string;
  reviewer?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  reviewNotes?: string;
  rejectionReason?: string;
  
  // Timestamps
  submittedAt?: string;
  reviewedAt?: string;
  approvedAt?: string;
  rejectedAt?: string;
  expiresAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface KYCDocument {
  id: string;
  kycId: string;
  type: 'NATIONAL_ID' | 'PASSPORT' | 'DRIVERS_LICENSE' | 'VOTERS_CARD' | 'UTILITY_BILL' | 'BANK_STATEMENT' | 'SELFIE' | 'SIGNATURE' | 'OTHER';
  name: string;
  description?: string;
  fileUrl: string;
  fileName: string;
  fileSize: number;
  mimeType: string;
  status: 'PENDING' | 'VERIFIED' | 'REJECTED';
  verificationNotes?: string;
  isRequired: boolean;
  expiryDate?: string;
  uploadedAt: string;
  verifiedAt?: string;
}

export interface CreateKYCData {
  personalInfo: {
    firstName: string;
    lastName: string;
    middleName?: string;
    dateOfBirth: string;
    gender: 'MALE' | 'FEMALE' | 'OTHER';
    nationality: string;
    placeOfBirth?: string;
    maritalStatus?: 'SINGLE' | 'MARRIED' | 'DIVORCED' | 'WIDOWED';
    occupation?: string;
    employer?: string;
    monthlyIncome?: number;
    sourceOfIncome?: string;
  };
  
  addressInfo: {
    street: string;
    city: string;
    state: string;
    country: string;
    postalCode?: string;
    isResidential: boolean;
    residenceDuration?: number;
  };
  
  contactInfo: {
    phoneNumber: string;
    alternatePhoneNumber?: string;
    email: string;
    alternateEmail?: string;
  };
  
  nextOfKin?: {
    firstName: string;
    lastName: string;
    relationship: string;
    phoneNumber: string;
    email?: string;
    address: string;
  };
  
  level?: 'BASIC' | 'INTERMEDIATE' | 'ADVANCED';
}

export interface UpdateKYCData {
  personalInfo?: Partial<CreateKYCData['personalInfo']>;
  addressInfo?: Partial<CreateKYCData['addressInfo']>;
  contactInfo?: Partial<CreateKYCData['contactInfo']>;
  nextOfKin?: Partial<CreateKYCData['nextOfKin']>;
}

export interface UploadKYCDocumentData {
  kycId: string;
  type: 'NATIONAL_ID' | 'PASSPORT' | 'DRIVERS_LICENSE' | 'VOTERS_CARD' | 'UTILITY_BILL' | 'BANK_STATEMENT' | 'SELFIE' | 'SIGNATURE' | 'OTHER';
  name: string;
  description?: string;
  file: File;
  expiryDate?: string;
}

export interface ReviewKYCData {
  kycId: string;
  status: 'APPROVED' | 'REJECTED';
  reviewNotes?: string;
  rejectionReason?: string;
  documentReviews?: {
    documentId: string;
    status: 'VERIFIED' | 'REJECTED';
    verificationNotes?: string;
  }[];
}

export interface KYCStats {
  totalSubmissions: number;
  pendingReview: number;
  underReview: number;
  approved: number;
  rejected: number;
  expired: number;
  approvalRate: number;
  averageReviewTime: number; // in hours
  submissionsThisMonth: number;
  approvalsThisMonth: number;
  rejectionsThisMonth: number;
  
  // Level breakdown
  levelBreakdown: {
    level: string;
    count: number;
    percentage: number;
  }[];
  
  // Document type stats
  documentStats: {
    type: string;
    submitted: number;
    verified: number;
    rejected: number;
    verificationRate: number;
  }[];
}

export interface KYCSearchFilters {
  search?: string;
  status?: 'PENDING' | 'UNDER_REVIEW' | 'APPROVED' | 'REJECTED' | 'EXPIRED';
  level?: 'BASIC' | 'INTERMEDIATE' | 'ADVANCED';
  dateFrom?: string;
  dateTo?: string;
  userId?: string;
  reviewedBy?: string;
  nationality?: string;
  sortBy?: 'createdAt' | 'submittedAt' | 'reviewedAt' | 'status';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface PaginatedKYCResponse {
  kycs: KYC[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  stats: {
    pendingCount: number;
    underReviewCount: number;
    approvedCount: number;
    rejectedCount: number;
    expiredCount: number;
  };
}

export interface KYCRequirements {
  level: 'BASIC' | 'INTERMEDIATE' | 'ADVANCED';
  requiredDocuments: {
    type: string;
    name: string;
    description: string;
    isRequired: boolean;
    acceptedFormats: string[];
    maxFileSize: number;
    examples?: string[];
  }[];
  limits: {
    dailyTransactionLimit: number;
    monthlyTransactionLimit: number;
    maxBalance: number;
    withdrawalLimit: number;
  };
  features: string[];
}
