"use client";

import { useEffect } from "react";
import { motion } from "framer-motion";
import AOS from "aos";
import "aos/dist/aos.css";
import Link from "next/link";
import {
  FiSave,
  FiArrowLeft,
  FiTarget,
  FiHeart,
  FiUsers,
  FiAward,
  FiTrendingUp,
  FiShield,
  FiGlobe,
  FiInfo,
} from "react-icons/fi";
import PageLayout from "../../components/PageLayout";
import EnhancedHero from "../../components/EnhancedHero";
import { CardImage3D, FeatureImage3D } from "../../src/components/ui/Image3D";
import { PrimaryButton } from "../../src/components/ui/AnimatedButton";

export default function AboutPage() {
  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
      easing: "ease-out-cubic",
    });
  }, []);

  const values = [
    {
      icon: <FiTarget className="w-8 h-8" data-oid="e-tkdx2" />,
      title: "Mission-Driven",
      description:
        "We're committed to making financial wellness accessible to everyone, regardless of their background or income level.",
    },
    {
      icon: <FiHeart className="w-8 h-8" data-oid="gvepwu3" />,
      title: "Customer-Centric",
      description:
        "Every feature we build and decision we make is centered around improving our users' financial lives.",
    },
    {
      icon: <FiShield className="w-8 h-8" data-oid="0t7wxl4" />,
      title: "Trust & Security",
      description:
        "We prioritize the security and privacy of your financial data above everything else.",
    },
    {
      icon: <FiTrendingUp className="w-8 h-8" data-oid="3nlepql" />,
      title: "Innovation",
      description:
        "We continuously innovate to provide cutting-edge financial tools and insights.",
    },
  ];

  const team = [
    {
      name: "Adebayo Johnson",
      role: "CEO & Founder",
      description:
        "Former Goldman Sachs analyst with 10+ years in fintech. Passionate about financial inclusion in Africa.",
      image: "/ChatGPT Image Jul 8, 2025, 03_09_19 PM.png",
    },
    {
      name: "Kemi Okafor",
      role: "CTO",
      description:
        "Ex-Google engineer specializing in financial technology and mobile applications. MIT Computer Science graduate.",
      image: "/ChatGPT Image Jul 8, 2025, 03_09_42 PM.png",
    },
    {
      name: "Tunde Adebisi",
      role: "Head of Product",
      description:
        "Product strategist with experience at Paystack and Flutterwave. Expert in user experience design.",
      image: "/ChatGPT Image Jul 8, 2025, 03_27_34 PM.png",
    },
    {
      name: "Funmi Adesanya",
      role: "Head of Operations",
      description:
        "Operations expert with background in banking and financial services. Ensures smooth platform operations.",
      image: "/Celebrating with her iPhone 14 Pro.png",
    },
  ];

  const milestones = [
    {
      year: "2022",
      title: "Company Founded",
      description:
        "Koja Save was founded with the vision of democratizing savings and investment in Nigeria.",
    },
    {
      year: "2023",
      title: "Beta Launch",
      description:
        "Launched our beta platform with 1,000 early users and received overwhelming positive feedback.",
    },
    {
      year: "2024",
      title: "Official Launch",
      description:
        "Officially launched Koja Save to the public with advanced features and investment options.",
    },
    {
      year: "2024",
      title: "Series A Funding",
      description:
        "Raised Series A funding to expand our services and reach more users across Nigeria.",
    },
  ];

  const stats = [
    {
      icon: <FiUsers className="w-8 h-8" data-oid="0f:.rv6" />,
      number: "10,000+",
      label: "Active Users",
    },
    {
      icon: <FiSave className="w-8 h-8" data-oid="02f_b2a" />,
      number: "₦500M+",
      label: "Total Savings",
    },
    {
      icon: <FiAward className="w-8 h-8" data-oid=":377e4t" />,
      number: "4.9/5",
      label: "User Rating",
    },
    {
      icon: <FiGlobe className="w-8 h-8" data-oid="6ecl_x2" />,
      number: "6",
      label: "States Covered",
    },
  ];

  return (
    <PageLayout>
      {/* Enhanced Hero Section */}
      <EnhancedHero
        icon={<FiInfo className="w-12 h-12 text-white" />}
        title="About"
        highlightText="Koja Save"
        description="We're on a mission to democratize financial wellness and make smart saving accessible to every Nigerian. Our story is one of innovation, trust, and unwavering commitment to your financial success."
        primaryButton={{
          text: "Join Our Mission",
          href: "/signup"
        }}
        secondaryButton={{
          text: "Contact Us",
          href: "/contact"
        }}
        features={[
          { icon: <FiTarget />, label: "Mission-Driven" },
          { icon: <FiHeart />, label: "Customer-Centric" },
          { icon: <FiShield />, label: "Secure & Trusted" },
          { icon: <FiTrendingUp />, label: "Innovation First" }
        ]}
        stats={[
          { value: "2020", label: "Founded" },
          { value: "10K+", label: "Users" },
          { value: "₦50M+", label: "Saved" },
          { value: "6", label: "States" }
        ]}
      />

      {/* Our Story */}
      <section className="relative z-10 px-6 py-20" data-oid="46jw-fw">
        <div className="max-w-7xl mx-auto" data-oid="o6md5gi">
          <div
            className="grid lg:grid-cols-2 gap-12 items-center"
            data-oid="p2_13n."
          >
            <div data-aos="fade-right" data-oid="m61:ekv">
              <h2
                className="text-4xl font-bold mb-6 text-white"
                data-oid=":dcw258"
              >
                Our Story
              </h2>
              <div className="space-y-4 text-gray-300" data-oid="pfie3jc">
                <p data-oid="snw-l:p">
                  Koja Save was born from a simple observation: too many
                  Nigerians struggle with saving money, not because they don't
                  want to, but because traditional banking systems don't make it
                  easy or rewarding.
                </p>
                <p data-oid="n:4d63-">
                  Our founders, having worked in top financial institutions
                  globally, saw an opportunity to leverage technology to create
                  a savings platform that truly works for the Nigerian market.
                  We combine international best practices with local insights to
                  deliver a world-class experience.
                </p>
                <p data-oid="s1xeq84">
                  Today, we're proud to be helping thousands of Nigerians build
                  better financial habits and achieve their savings goals
                  through our innovative platform.
                </p>
              </div>
            </div>
            <div data-aos="fade-left" data-oid=":9e.b3p">
              <div className="relative">
                <FeatureImage3D
                  src="/Entering Better Interest Office (1).png"
                  alt="BetterInterest office - Where innovation meets financial wellness"
                  width={600}
                  height={400}
                  className="w-full h-auto rounded-xl"
                />
                <div className="absolute bottom-4 left-4 bg-black/70 backdrop-blur-sm rounded-lg p-3 z-20">
                  <h4 className="text-white font-semibold text-sm">Our Office</h4>
                  <p className="text-green-400 text-xs">Innovation Hub</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats */}
      <section className="relative z-10 px-6 py-20" data-oid="u43m6ex">
        <div className="max-w-7xl mx-auto" data-oid="x5i8i_.">
          <div
            className="text-center mb-16"
            data-aos="fade-up"
            data-oid="sn9zy.p"
          >
            <h2
              className="text-4xl md:text-5xl font-bold mb-6 text-white"
              data-oid="ycytlji"
            >
              Our{" "}
              <span className="text-green-400" data-oid="9.m9ucw">
                Impact
              </span>
            </h2>
          </div>

          <div
            className="grid grid-cols-2 lg:grid-cols-4 gap-8"
            data-oid="q7m2ax0"
          >
            {stats.map((stat, index) => (
              <div
                key={index}
                data-aos="fade-up"
                data-aos-delay={index * 100}
                className="app-card hover-lift-advanced p-6 text-center card-stack"
                data-oid=":4b7s.a"
              >
                <div
                  className="text-green-400 mb-4 flex justify-center group-hover:animate-pulse-green transition-all"
                  data-oid="jv890nn"
                >
                  {stat.icon}
                </div>
                <div
                  className="text-3xl font-bold text-white mb-2"
                  data-oid="n2fr2i9"
                >
                  {stat.number}
                </div>
                <div className="text-gray-300" data-oid="jfc12ko">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Values */}
      <section className="relative z-10 px-6 py-20" data-oid="x.m59mm">
        <div className="max-w-7xl mx-auto" data-oid="blb2h-6">
          <div
            className="text-center mb-16"
            data-aos="fade-up"
            data-oid="eb.a4am"
          >
            <h2
              className="text-4xl md:text-5xl font-bold mb-6 text-white"
              data-oid="fcylme3"
            >
              Our{" "}
              <span className="text-green-400" data-oid="g42.-az">
                Values
              </span>
            </h2>
            <p
              className="text-xl text-gray-300 max-w-3xl mx-auto"
              data-oid="9cy9u3c"
            >
              These core values guide everything we do and shape how we serve
              our users.
            </p>
          </div>

          <div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
            data-oid="w3q0cb:"
          >
            {values.map((value, index) => (
              <div
                key={index}
                data-aos="fade-up"
                data-aos-delay={index * 100}
                className="app-card hover-lift-advanced p-6 text-center card-stack"
                data-oid="o_v3qq:"
              >
                <div
                  className="text-green-400 mb-4 flex justify-center group-hover:animate-pulse-green transition-all"
                  data-oid="tex7493"
                >
                  {value.icon}
                </div>
                <h3
                  className="text-xl font-semibold mb-3 text-white"
                  data-oid="-170bqr"
                >
                  {value.title}
                </h3>
                <p className="text-gray-300" data-oid="g1iaiug">
                  {value.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team */}
      <section className="relative z-10 px-6 py-20" data-oid="omqh-d8">
        <div className="max-w-7xl mx-auto" data-oid="ovqeg2d">
          <div
            className="text-center mb-16"
            data-aos="fade-up"
            data-oid="yq8r8.n"
          >
            <h2
              className="text-4xl md:text-5xl font-bold mb-6 text-white"
              data-oid="anwj6l2"
            >
              Meet Our{" "}
              <span className="text-green-400" data-oid="71j0imv">
                Team
              </span>
            </h2>
            <p
              className="text-xl text-gray-300 max-w-3xl mx-auto"
              data-oid="0wxloq-"
            >
              Our diverse team brings together expertise from finance,
              technology, and user experience.
            </p>
          </div>

          <div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
            data-oid="b0kyf.g"
          >
            {team.map((member, index) => (
              <div
                key={index}
                data-aos="fade-up"
                data-aos-delay={index * 100}
                className="app-card hover-lift-advanced p-6 text-center card-stack"
                data-oid="r4t-bct"
              >
                <div className="w-24 h-24 mx-auto mb-4">
                  <CardImage3D
                    src={member.image}
                    alt={`${member.name} - ${member.role}`}
                    width={96}
                    height={96}
                    variant="testimonial"
                    className="w-full h-full border-2 border-green-400/30"
                  />
                </div>
                <h3
                  className="text-xl font-semibold mb-2 text-white"
                  data-oid=":lgtoo."
                >
                  {member.name}
                </h3>
                <p
                  className="text-green-400 mb-3 font-medium"
                  data-oid="mftm6vh"
                >
                  {member.role}
                </p>
                <p className="text-gray-300 text-sm" data-oid="wkpjqkb">
                  {member.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Timeline */}
      <section className="relative z-10 px-6 py-20" data-oid="f98pw:h">
        <div className="max-w-4xl mx-auto" data-oid="3mbcixz">
          <div
            className="text-center mb-16"
            data-aos="fade-up"
            data-oid="p4cqwar"
          >
            <h2
              className="text-4xl md:text-5xl font-bold mb-6 text-white"
              data-oid="02khyq-"
            >
              Our{" "}
              <span className="text-green-400" data-oid="-iwia9h">
                Journey
              </span>
            </h2>
          </div>

          <div className="space-y-8" data-oid="oz6l8t-">
            {milestones.map((milestone, index) => (
              <div
                key={index}
                data-aos="fade-up"
                data-aos-delay={index * 100}
                className="flex items-start gap-6"
                data-oid="diqf6l0"
              >
                <div
                  className="flex-shrink-0 w-16 h-16 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center text-white font-bold"
                  data-oid="uewdhfb"
                >
                  {milestone.year}
                </div>
                <div
                  className="app-card hover-lift-advanced p-6 flex-1"
                  data-oid="i--lcbq"
                >
                  <h3
                    className="text-xl font-semibold mb-2 text-white"
                    data-oid="ston2mf"
                  >
                    {milestone.title}
                  </h3>
                  <p className="text-gray-300" data-oid="-ydc-an">
                    {milestone.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Success Stories */}
      <section className="relative z-10 px-6 py-20">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16" data-aos="fade-up">
            <h2 className="text-4xl md:text-5xl font-inter font-bold mb-6 text-white text-shadow">
              Success{" "}
              <span className="text-green-400">
                Stories
              </span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Real people achieving real financial success with BetterInterest.
              These are the stories that drive our mission forward.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                image: "/Celebrating with Her New iPhone.png",
                name: "Sarah Adebayo",
                achievement: "Saved ₦2M in 12 months",
                story: "Started with small daily contributions and built an emergency fund that changed her life.",
                category: "Emergency Fund"
              },
              {
                image: "/Celebrating with Her New iPhone (1).png",
                name: "Michael Okonkwo",
                achievement: "Achieved 18% returns",
                story: "Used our investment features to grow his savings beyond traditional bank rates.",
                category: "Investment Growth"
              },
              {
                image: "/suite.png",
                name: "BetterInterest Platform",
                achievement: "10,000+ Users Served",
                story: "Our comprehensive suite has helped thousands achieve their financial goals.",
                category: "Platform Success"
              }
            ].map((story, index) => (
              <div
                key={index}
                data-aos="fade-up"
                data-aos-delay={index * 100}
                className="app-card hover-lift-advanced p-6 text-center"
              >
                <div className="mb-6 flex justify-center">
                  <CardImage3D
                    src={story.image}
                    alt={story.name}
                    width={280}
                    height={200}
                    className="w-full h-auto rounded-lg mx-auto"
                  />
                </div>

                <div className="mb-2">
                  <span className="inline-block bg-green-500/20 text-green-400 px-3 py-1 rounded-full text-xs font-semibold">
                    {story.category}
                  </span>
                </div>

                <h3 className="text-xl font-semibold mb-2 text-white">
                  {story.name}
                </h3>

                <p className="text-green-400 mb-3 font-medium">
                  {story.achievement}
                </p>

                <p className="text-gray-300 text-sm">
                  {story.story}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative z-10 px-6 py-20" data-oid="3g0da34">
        <div
          className="max-w-4xl mx-auto text-center"
          data-aos="fade-up"
          data-oid="b5kwbv9"
        >
          <div
            className="glass-advanced rounded-2xl p-12 app-card-premium"
            data-oid="qt2rnx1"
          >
            <h2
              className="text-4xl md:text-5xl font-bold mb-6 text-white"
              data-oid="f917uqp"
            >
              Join Our{" "}
              <span className="text-green-400" data-oid="naheppg">
                Mission
              </span>
            </h2>
            <p className="text-xl text-gray-300 mb-8" data-oid="w89eia-">
              Be part of the financial revolution. Start your savings journey
              with Koja Save today.
            </p>
            <div
              className="flex flex-col sm:flex-row gap-4 justify-center"
              data-oid="e053g:m"
            >
              <Link
                href="/signup"
                className="btn-advanced px-8 py-4 rounded-lg text-lg font-semibold text-white hover-lift"
                data-oid="27a8xls"
              >
                Start Saving Now
              </Link>
              <Link
                href="/contact"
                className="px-8 py-4 border border-green-400 rounded-lg text-lg font-semibold text-white hover:bg-green-400 hover:text-black transition-all hover-lift magnetic"
                data-oid="6w5-wxo"
              >
                Get in Touch
              </Link>
            </div>
          </div>
        </div>
      </section>

    </PageLayout>
  );
}
