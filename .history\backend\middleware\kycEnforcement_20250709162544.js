// backend/middleware/kycEnforcement.js
// Middleware to enforce KYC approval before allowing deposits or plan creation
const User = require('../models/user');

const enforceKYCApproved = async (req, res, next) => {
  try {
    const user = req.user;
    // If user is not loaded, fetch from DB
    let userData = user;
    if (!user.kycStatus && user.id) {
      userData = await User.findById(user.id);
    }
    if (!userData || userData.kycStatus !== 'APPROVED') {
      return res.status(403).json({
        error: 'KYC not approved. Please complete your KYC to access this feature.',
        kycStatus: userData ? userData.kycStatus : 'NOT_SUBMITTED',
      });
    }
    next();
  } catch (err) {
    return res.status(500).json({ error: 'KYC enforcement failed', details: err.message });
  }
};

module.exports = { enforceKYCApproved };
