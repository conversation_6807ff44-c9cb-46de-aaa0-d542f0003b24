import { depositsService } from '../deposits';
import { ApiError, ValidationError } from '../errorHandler';

// Mock fetch globally
global.fetch = jest.fn();

const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

describe('DepositsService', () => {
  beforeEach(() => {
    mockFetch.mockClear();
    localStorage.clear();
    localStorage.setItem('auth_token', 'mock-jwt-token');
  });

  describe('initiateDeposit', () => {
    it('should initiate deposit successfully', async () => {
      const depositData = {
        amount: 10000,
        currency: 'NGN',
        paymentMethod: 'CARD' as const,
        description: 'Test deposit'
      };

      const mockResponse = {
        deposit: {
          id: 'dep-1',
          userId: '1',
          amount: 10000,
          currency: 'NGN',
          status: 'PENDING',
          paymentMethod: 'CARD',
          reference: 'DEP_123456789_ABC123',
          description: 'Test deposit',
          fees: 150,
          netAmount: 9850,
          initiatedAt: '2023-01-01T00:00:00Z',
          createdAt: '2023-01-01T00:00:00Z',
          updatedAt: '2023-01-01T00:00:00Z'
        },
        paymentData: {
          authorizationUrl: 'https://checkout.paystack.com/abc123',
          accessCode: 'abc123',
          reference: 'DEP_123456789_ABC123'
        }
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
        headers: new Headers({ 'content-type': 'application/json' })
      } as Response);

      const result = await depositsService.initiateDeposit(depositData);

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/deposit/initiate',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(depositData)
        })
      );

      expect(result).toEqual(mockResponse);
    });

    it('should throw ValidationError for invalid amount', async () => {
      const mockErrorResponse = {
        success: false,
        message: 'Validation failed',
        errors: {
          amount: ['Amount must be at least ₦100']
        }
      };

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 422,
        json: async () => mockErrorResponse,
        headers: new Headers({ 'content-type': 'application/json' })
      } as Response);

      const depositData = {
        amount: 50,
        paymentMethod: 'CARD' as const
      };

      await expect(depositsService.initiateDeposit(depositData)).rejects.toThrow(ValidationError);
    });
  });

  describe('verifyDeposit', () => {
    it('should verify deposit successfully', async () => {
      const verifyData = {
        reference: 'DEP_123456789_ABC123',
        paystackReference: 'paystack_ref_123'
      };

      const mockResponse = {
        deposit: {
          id: 'dep-1',
          userId: '1',
          amount: 10000,
          currency: 'NGN',
          status: 'COMPLETED',
          paymentMethod: 'CARD',
          reference: 'DEP_123456789_ABC123',
          paystackReference: 'paystack_ref_123',
          fees: 150,
          netAmount: 9850,
          completedAt: '2023-01-01T00:05:00Z',
          createdAt: '2023-01-01T00:00:00Z',
          updatedAt: '2023-01-01T00:05:00Z'
        },
        isSuccessful: true,
        message: 'Deposit verified successfully'
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
        headers: new Headers({ 'content-type': 'application/json' })
      } as Response);

      const result = await depositsService.verifyDeposit(verifyData);

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/deposit/verify',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(verifyData)
        })
      );

      expect(result).toEqual(mockResponse);
    });

    it('should handle failed verification', async () => {
      const verifyData = {
        reference: 'DEP_123456789_ABC123',
        paystackReference: 'paystack_ref_123'
      };

      const mockResponse = {
        deposit: {
          id: 'dep-1',
          userId: '1',
          amount: 10000,
          status: 'FAILED',
          failureReason: 'Payment declined by bank',
          createdAt: '2023-01-01T00:00:00Z',
          updatedAt: '2023-01-01T00:05:00Z'
        },
        isSuccessful: false,
        message: 'Payment verification failed'
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
        headers: new Headers({ 'content-type': 'application/json' })
      } as Response);

      const result = await depositsService.verifyDeposit(verifyData);

      expect(result.isSuccessful).toBe(false);
      expect(result.deposit.status).toBe('FAILED');
    });
  });

  describe('getUserDeposits', () => {
    it('should get user deposits successfully', async () => {
      const filters = {
        status: 'COMPLETED' as const,
        dateFrom: '2023-01-01',
        dateTo: '2023-01-31',
        page: 1,
        limit: 10
      };

      const mockResponse = {
        deposits: [
          {
            id: 'dep-1',
            userId: '1',
            amount: 10000,
            status: 'COMPLETED',
            paymentMethod: 'CARD',
            reference: 'DEP_123456789_ABC123',
            completedAt: '2023-01-01T00:05:00Z',
            createdAt: '2023-01-01T00:00:00Z'
          },
          {
            id: 'dep-2',
            userId: '1',
            amount: 25000,
            status: 'COMPLETED',
            paymentMethod: 'BANK_TRANSFER',
            reference: 'DEP_123456789_DEF456',
            completedAt: '2023-01-15T00:05:00Z',
            createdAt: '2023-01-15T00:00:00Z'
          }
        ],
        total: 2,
        page: 1,
        limit: 10,
        totalPages: 1,
        stats: {
          totalAmount: 35000,
          successfulCount: 2,
          failedCount: 0,
          pendingCount: 0
        }
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
        headers: new Headers({ 'content-type': 'application/json' })
      } as Response);

      const result = await depositsService.getUserDeposits(filters);

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('http://localhost:8000/api/deposit/user?'),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': 'Bearer mock-jwt-token'
          })
        })
      );

      expect(result).toEqual(mockResponse);
    });
  });

  describe('getDepositStats', () => {
    it('should get deposit statistics successfully', async () => {
      const filters = {
        dateFrom: '2023-01-01',
        dateTo: '2023-01-31'
      };

      const mockStats = {
        totalDeposits: 150,
        totalAmount: 5000000,
        successfulDeposits: 145,
        failedDeposits: 5,
        pendingDeposits: 0,
        averageDepositAmount: 33333.33,
        depositsThisMonth: 50,
        amountThisMonth: 1500000,
        topPaymentMethods: [
          {
            method: 'CARD',
            count: 80,
            amount: 2500000,
            percentage: 50
          },
          {
            method: 'BANK_TRANSFER',
            count: 65,
            amount: 2000000,
            percentage: 40
          }
        ]
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockStats,
        headers: new Headers({ 'content-type': 'application/json' })
      } as Response);

      const result = await depositsService.getDepositStats(filters);

      expect(result).toEqual(mockStats);
    });
  });

  describe('utility methods', () => {
    it('should calculate fees correctly', () => {
      const cardFees = depositsService.calculateFees(10000, 'CARD');
      expect(cardFees.fees).toBe(250); // 1.5% + 100, capped at 2000
      expect(cardFees.netAmount).toBe(9750);

      const bankTransferFees = depositsService.calculateFees(10000, 'BANK_TRANSFER');
      expect(bankTransferFees.fees).toBe(100); // 0.5% + 50, capped at 1000
      expect(bankTransferFees.netAmount).toBe(9900);

      // Test fee cap
      const largeFees = depositsService.calculateFees(1000000, 'CARD');
      expect(largeFees.fees).toBe(2000); // Should be capped at 2000
    });

    it('should validate amounts correctly', () => {
      const validAmount = depositsService.validateAmount(10000, 'CARD');
      expect(validAmount.isValid).toBe(true);

      const tooSmall = depositsService.validateAmount(50, 'CARD');
      expect(tooSmall.isValid).toBe(false);
      expect(tooSmall.error).toContain('Minimum amount');

      const tooLarge = depositsService.validateAmount(2000000, 'CARD');
      expect(tooLarge.isValid).toBe(false);
      expect(tooLarge.error).toContain('Maximum amount');
    });

    it('should format currency correctly', () => {
      expect(depositsService.formatCurrency(10000)).toBe('₦10,000.00');
      expect(depositsService.formatCurrency(1234.56)).toBe('₦1,234.56');
    });

    it('should get payment method icons correctly', () => {
      expect(depositsService.getPaymentMethodIcon('CARD')).toBe('💳');
      expect(depositsService.getPaymentMethodIcon('BANK_TRANSFER')).toBe('🏦');
      expect(depositsService.getPaymentMethodIcon('USSD')).toBe('📱');
    });

    it('should get status colors correctly', () => {
      expect(depositsService.getStatusColor('PENDING')).toBe('#F59E0B');
      expect(depositsService.getStatusColor('COMPLETED')).toBe('#10B981');
      expect(depositsService.getStatusColor('FAILED')).toBe('#EF4444');
    });

    it('should generate references correctly', () => {
      const reference = depositsService.generateReference();
      expect(reference).toMatch(/^DEP_\d+_[A-Z0-9]+$/);
    });
  });
});
