"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme, getThemeClasses } from '../../contexts/ThemeContext';
import { ThemeToggleButton } from '../ui/ThemedButton';
import { useAuth } from '../../hooks/use-auth';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  FiMenu, 
  FiX, 
  FiHome, 
  FiPieChart, 
  FiCreditCard, 
  FiUsers, 
  FiSettings, 
  FiBell, 
  FiUser,
  FiLogOut,
  FiShield,
  FiDollarSign,
  FiTrendingUp,
  FiFileText,
  FiUserCheck,
  FiChevronDown,
  FiChevronRight
} from 'react-icons/fi';

interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<any>;
  badge?: string;
  children?: NavItem[];
}

interface CascadeSidebarProps {
  children: React.ReactNode;
  title?: string;
}

const userNavItems: NavItem[] = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: FiHome
  },
  {
    name: 'Individual Savings',
    href: '/dashboard/savings-plans',
    icon: FiPieChart,
    children: [
      { name: 'My Plans', href: '/dashboard/savings-plans', icon: FiPieChart },
      { name: 'Create Plan', href: '/dashboard/savings-plans/create', icon: FiPieChart },
      { name: 'Analytics', href: '/dashboard/savings-analytics', icon: FiTrendingUp },
    ]
  },
  {
    name: 'Group Savings',
    href: '/dashboard/group-savings',
    icon: FiUsers,
    children: [
      { name: 'My Groups', href: '/dashboard/group-savings', icon: FiUsers },
      { name: 'Join Group', href: '/dashboard/group-savings/join', icon: FiUsers },
      { name: 'Create Group', href: '/dashboard/group-savings/create', icon: FiUsers },
    ]
  },
  {
    name: 'Investment',
    href: '/dashboard/investment',
    icon: FiTrendingUp
  },
  {
    name: 'Target Savings',
    href: '/dashboard/target-savings',
    icon: FiPieChart
  },
  {
    name: 'Payments',
    href: '/dashboard/payments',
    icon: FiCreditCard,
    children: [
      { name: 'Payment History', href: '/dashboard/payments', icon: FiCreditCard },
      { name: 'Make Payment', href: '/dashboard/payments/new', icon: FiCreditCard }
    ]
  },
  {
    name: 'KYC Verification',
    href: '/dashboard/kyc',
    icon: FiUserCheck
  },
  {
    name: 'Profile',
    href: '/dashboard/profile',
    icon: FiUser
  },
  {
    name: 'Settings',
    href: '/dashboard/settings',
    icon: FiSettings
  },
];

const adminNavItems: NavItem[] = [
  { name: 'Admin Dashboard', href: '/admin/dashboard', icon: FiShield },
  { name: 'User Management', href: '/admin/users', icon: FiUsers },
  { name: 'Payment Management', href: '/admin/payments', icon: FiDollarSign },
  { name: 'KYC Management', href: '/admin/kyc', icon: FiUserCheck },
  { name: 'Analytics', href: '/admin/analytics', icon: FiTrendingUp },
  { name: 'Reports', href: '/admin/reports', icon: FiFileText },
  { name: 'Settings', href: '/admin/settings', icon: FiSettings },
];

function NavItemComponent({ 
  item, 
  level = 0, 
  isActive, 
  onItemClick 
}: { 
  item: NavItem; 
  level?: number; 
  isActive: boolean;
  onItemClick: () => void;
}) {
  const { theme } = useTheme();
  const themeClasses = getThemeClasses(theme);
  const [isExpanded, setIsExpanded] = useState(false);
  const hasChildren = item.children && item.children.length > 0;

  const handleClick = () => {
    if (hasChildren) {
      setIsExpanded(!isExpanded);
    } else {
      onItemClick();
    }
  };

  const itemClasses = `
    flex items-center justify-between w-full px-4 py-3 rounded-lg
    transition-all duration-200 group relative
    ${level > 0 ? 'ml-4 pl-8' : ''}
    ${isActive 
      ? `${themeClasses.button.primary} shadow-lg` 
      : `hover:${themeClasses.bg.tertiary} ${themeClasses.text.secondary} hover:${themeClasses.text.primary}`
    }
  `;

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3, delay: level * 0.1 }}
    >
      {hasChildren ? (
        <button onClick={handleClick} className={itemClasses}>
          <div className="flex items-center space-x-3">
            <item.icon className="w-5 h-5" />
            <span className="font-medium">{item.name}</span>
            {item.badge && (
              <span className="px-2 py-1 text-xs bg-red-500 text-white rounded-full">
                {item.badge}
              </span>
            )}
          </div>
          <motion.div
            animate={{ rotate: isExpanded ? 90 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <FiChevronRight className="w-4 h-4" />
          </motion.div>
        </button>
      ) : (
        <Link href={item.href} onClick={handleClick} className={itemClasses}>
          <div className="flex items-center space-x-3">
            <item.icon className="w-5 h-5" />
            <span className="font-medium">{item.name}</span>
            {item.badge && (
              <span className="px-2 py-1 text-xs bg-red-500 text-white rounded-full">
                {item.badge}
              </span>
            )}
          </div>
        </Link>
      )}

      <AnimatePresence>
        {hasChildren && isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="mt-2 space-y-1"
          >
            {item.children?.map((child, index) => (
              <NavItemComponent
                key={child.href}
                item={child}
                level={level + 1}
                isActive={isActive && child.href === item.href}
                onItemClick={onItemClick}
              />
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}

export default function CascadeSidebar({ children, title }: CascadeSidebarProps) {
  const { theme } = useTheme();
  const themeClasses = getThemeClasses(theme);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const pathname = usePathname();
  const { user, logout } = useAuth();

  const handleLogout = async () => {
    try {
      await logout();
      window.location.href = '/';
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  // Use actual user data or fallback to demo data
  const currentUser = user || {
    firstName: 'Demo',
    lastName: 'User',
    email: '<EMAIL>',
    role: 'USER'
  };

  const navItems = currentUser?.role === 'ADMIN' ? adminNavItems : userNavItems;

  return (
    <div className={`min-h-screen ${themeClasses.bg.primary} ${themeClasses.text.primary}`}>
      {/* Mobile sidebar backdrop */}
      <AnimatePresence>
        {sidebarOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-40 bg-black/50 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <motion.div
        initial={false}
        animate={{
          x: sidebarOpen ? 0 : -320,
        }}
        className={`fixed inset-y-0 left-0 z-50 w-80 ${themeClasses.bg.sidebar} ${themeClasses.border.primary} border-r lg:translate-x-0 lg:static lg:inset-0`}
      >
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className={`flex items-center justify-between h-16 px-6 ${themeClasses.border.primary} border-b`}>
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-green-400 to-green-600 rounded-lg flex items-center justify-center">
                <span className="text-black font-bold text-sm">B</span>
              </div>
              <span className="text-xl font-bold">BetterInterest</span>
            </div>
            <div className="flex items-center space-x-2">
              <ThemeToggleButton />
              <button
                onClick={() => setSidebarOpen(false)}
                className="lg:hidden p-2 rounded-lg hover:bg-gray-800 transition-colors"
              >
                <FiX className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* User info */}
          <div className={`p-6 ${themeClasses.border.primary} border-b`}>
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center">
                <span className="text-black font-semibold">
                  {currentUser?.firstName?.[0]}{currentUser?.lastName?.[0]}
                </span>
              </div>
              <div>
                <p className={`font-medium ${themeClasses.text.primary}`}>
                  {currentUser?.firstName} {currentUser?.lastName}
                </p>
                <p className={`text-sm ${themeClasses.text.secondary}`}>
                  {currentUser?.email}
                </p>
                <span className={`inline-block px-2 py-1 text-xs rounded-full mt-1 ${
                  currentUser?.role === 'ADMIN' 
                    ? 'bg-red-500/20 text-red-400' 
                    : 'bg-green-500/20 text-green-400'
                }`}>
                  {currentUser?.role}
                </span>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-6 py-6 space-y-2 overflow-y-auto">
            {navItems.map((item, index) => (
              <NavItemComponent
                key={item.href}
                item={item}
                isActive={pathname === item.href}
                onItemClick={() => setSidebarOpen(false)}
              />
            ))}
          </nav>

          {/* Logout */}
          <div className={`p-6 ${themeClasses.border.primary} border-t`}>
            <button
              onClick={handleLogout}
              className="flex items-center space-x-3 w-full px-4 py-3 rounded-lg hover:bg-red-500/20 hover:text-red-400 transition-colors group"
            >
              <FiLogOut className="w-5 h-5" />
              <span>Logout</span>
            </button>
          </div>
        </div>
      </motion.div>

      {/* Main content */}
      <div className="lg:pl-80">
        {/* Top bar */}
        <header className={`h-16 ${themeClasses.bg.header} ${themeClasses.border.primary} border-b flex items-center justify-between px-6 backdrop-blur-sm`}>
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setSidebarOpen(true)}
              className="lg:hidden p-2 rounded-lg hover:bg-gray-800 transition-colors"
            >
              <FiMenu className="w-5 h-5" />
            </button>
            {title && (
              <h1 className={`text-xl font-semibold ${themeClasses.text.primary}`}>{title}</h1>
            )}
          </div>

          <div className="flex items-center space-x-4">
            {/* Notifications */}
            <button className={`p-2 rounded-lg hover:${themeClasses.bg.tertiary} transition-colors relative`}>
              <FiBell className="w-5 h-5" />
              <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
            </button>

            {/* Profile dropdown */}
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center">
                <span className="text-black font-semibold text-sm">
                  {currentUser?.firstName?.[0]}{currentUser?.lastName?.[0]}
                </span>
              </div>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="p-6">
          {children}
        </main>
      </div>
    </div>
  );
}
