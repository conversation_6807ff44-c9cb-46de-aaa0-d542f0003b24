"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { IconButton } from '../ui/Button';
import { FiBell, FiCheck, FiX, FiDollarSign, FiShield, FiTrendingUp } from 'react-icons/fi';

interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  timestamp: Date;
  read: boolean;
  icon?: React.ComponentType<any>;
}

const mockNotifications: Notification[] = [
  {
    id: '1',
    title: 'Savings Goal Achieved!',
    message: 'Congratulations! You\'ve reached your Emergency Fund goal of ₦500,000.',
    type: 'success',
    timestamp: new Date(Date.now() - 5 * 60 * 1000),
    read: false,
    icon: FiTrendingUp
  },
  {
    id: '2',
    title: 'Monthly Contribution Due',
    message: 'Your monthly contribution of ₦25,000 is due in 3 days.',
    type: 'warning',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
    read: false,
    icon: FiDollarSign
  },
  {
    id: '3',
    title: 'KYC Verification Complete',
    message: 'Your identity verification has been approved. You now have full access.',
    type: 'success',
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
    read: true,
    icon: FiShield
  }
];

export default function NotificationDropdown() {
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState(mockNotifications);

  const unreadCount = notifications.filter(n => !n.read).length;

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'success': return 'text-brand bg-brand/10';
      case 'warning': return 'text-yellow-500 bg-yellow-500/10';
      case 'error': return 'text-red-500 bg-red-500/10';
      default: return 'text-blue-500 bg-blue-500/10';
    }
  };

  const formatTime = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  return (
    <div className="relative">
      <div className="relative">
        <IconButton
          icon={FiBell}
          onClick={() => setIsOpen(!isOpen)}
          className="relative"
          tooltip="Notifications"
        />
        {unreadCount > 0 && (
          <motion.span
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="absolute -top-1 -right-1 w-5 h-5 bg-brand text-white text-xs rounded-full flex items-center justify-center font-bold shadow-lg"
          >
            {unreadCount > 9 ? '9+' : unreadCount}
          </motion.span>
        )}
      </div>

      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <div
              className="fixed inset-0 z-40"
              onClick={() => setIsOpen(false)}
            />
            
            {/* Dropdown */}
            <motion.div
              initial={{ opacity: 0, y: 10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: 10, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className="absolute right-0 top-full mt-2 w-96 bg-theme border border-theme rounded-xl shadow-2xl z-50 overflow-hidden"
              style={{
                boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(34, 197, 94, 0.1)'
              }}
            >
              {/* Header */}
              <div className="p-4 border-b border-theme bg-theme-secondary">
                <div className="flex items-center justify-between">
                  <h3 className="font-inter font-semibold text-theme">Notifications</h3>
                  {unreadCount > 0 && (
                    <button
                      onClick={markAllAsRead}
                      className="text-brand hover:text-brand-dark text-sm font-medium transition-colors"
                    >
                      Mark all read
                    </button>
                  )}
                </div>
              </div>

              {/* Notifications List */}
              <div className="max-h-96 overflow-y-auto">
                {notifications.length === 0 ? (
                  <div className="p-8 text-center">
                    <FiBell className="w-12 h-12 text-theme-secondary mx-auto mb-3" />
                    <p className="text-theme-secondary font-inter">No notifications</p>
                  </div>
                ) : (
                  <div className="divide-y divide-theme">
                    {notifications.map((notification) => {
                      const IconComponent = notification.icon || FiBell;
                      return (
                        <motion.div
                          key={notification.id}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          className={`p-4 hover:bg-theme-secondary transition-colors cursor-pointer group ${
                            !notification.read ? 'bg-brand/5' : ''
                          }`}
                          onClick={() => markAsRead(notification.id)}
                        >
                          <div className="flex items-start space-x-3">
                            <div className={`p-2 rounded-lg ${getTypeColor(notification.type)}`}>
                              <IconComponent className="w-4 h-4" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-start justify-between">
                                <h4 className={`font-medium text-sm font-inter ${
                                  !notification.read ? 'text-theme' : 'text-theme-secondary'
                                }`}>
                                  {notification.title}
                                </h4>
                                <div className="flex items-center space-x-1 ml-2">
                                  <span className="text-xs text-theme-secondary whitespace-nowrap">
                                    {formatTime(notification.timestamp)}
                                  </span>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      removeNotification(notification.id);
                                    }}
                                    className="opacity-0 group-hover:opacity-100 p-1 hover:bg-red-500/10 rounded transition-all"
                                  >
                                    <FiX className="w-3 h-3 text-red-500" />
                                  </button>
                                </div>
                              </div>
                              <p className="text-xs text-theme-secondary mt-1 font-inter">
                                {notification.message}
                              </p>
                              {!notification.read && (
                                <div className="w-2 h-2 bg-brand rounded-full mt-2"></div>
                              )}
                            </div>
                          </div>
                        </motion.div>
                      );
                    })}
                  </div>
                )}
              </div>

              {/* Footer */}
              {notifications.length > 0 && (
                <div className="p-3 border-t border-theme bg-theme-secondary">
                  <button className="w-full text-center text-brand hover:text-brand-dark text-sm font-medium transition-colors font-inter">
                    View all notifications
                  </button>
                </div>
              )}
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
}
