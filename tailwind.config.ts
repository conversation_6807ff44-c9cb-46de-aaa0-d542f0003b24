import type { Config } from 'tailwindcss';

const config: Config = {
    darkMode: ['class'],
    content: [
        './pages/**/*.{js,ts,jsx,tsx,mdx}',
        './components/**/*.{js,ts,jsx,tsx,mdx}',
        './app/**/*.{js,ts,jsx,tsx,mdx}',
    ],
    theme: {
        extend: {
            fontFamily: {
                'sans': ['var(--font-inter)', 'Inter', 'system-ui', 'sans-serif'],
                'display': ['var(--font-fugaz)', 'Fugaz One', 'cursive'],
                'inter': ['var(--font-inter)', 'Inter', 'sans-serif'],
                'fugaz': ['var(--font-fugaz)', 'Fugaz One', 'cursive'],
            },
            backgroundImage: {
                'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
                'gradient-conic':
                    'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
            },
            borderRadius: {
                lg: 'var(--radius)',
                md: 'calc(var(--radius) - 2px)',
                sm: 'calc(var(--radius) - 4px)',
            },
            colors: {
                background: 'hsl(var(--background))',
                foreground: 'hsl(var(--foreground))',
                card: {
                    DEFAULT: 'hsl(var(--card))',
                    foreground: 'hsl(var(--card-foreground))',
                },
                popover: {
                    DEFAULT: 'hsl(var(--popover))',
                    foreground: 'hsl(var(--popover-foreground))',
                },
                primary: {
                    DEFAULT: 'hsl(var(--primary))',
                    foreground: 'hsl(var(--primary-foreground))',
                },
                secondary: {
                    DEFAULT: 'hsl(var(--secondary))',
                    foreground: 'hsl(var(--secondary-foreground))',
                },
                muted: {
                    DEFAULT: 'hsl(var(--muted))',
                    foreground: 'hsl(var(--muted-foreground))',
                },
                accent: {
                    DEFAULT: 'hsl(var(--accent))',
                    foreground: 'hsl(var(--accent-foreground))',
                },
                destructive: {
                    DEFAULT: 'hsl(var(--destructive))',
                    foreground: 'hsl(var(--destructive-foreground))',
                },
                border: 'hsl(var(--border))',
                input: 'hsl(var(--input))',
                ring: 'hsl(var(--ring))',
                chart: {
                    '1': 'hsl(var(--chart-1))',
                    '2': 'hsl(var(--chart-2))',
                    '3': 'hsl(var(--chart-3))',
                    '4': 'hsl(var(--chart-4))',
                    '5': 'hsl(var(--chart-5))',
                },
            },
            textShadow: {
                'sm': '0 1px 2px rgba(0, 0, 0, 0.05)',
                'DEFAULT': '0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06)',
                'md': '0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06)',
                'lg': '0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05)',
                'xl': '0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04)',
                '2xl': '0 25px 50px rgba(0, 0, 0, 0.25)',
                'none': 'none',
                '3d': '0 1px 0 #ccc, 0 2px 0 #c9c9c9, 0 3px 0 #bbb, 0 4px 0 #b9b9b9, 0 5px 0 #aaa, 0 6px 1px rgba(0,0,0,.1), 0 0 5px rgba(0,0,0,.1), 0 1px 3px rgba(0,0,0,.3), 0 3px 5px rgba(0,0,0,.2), 0 5px 10px rgba(0,0,0,.25)',
                '3d-green': '0 1px 0 #22c55e, 0 2px 0 #16a34a, 0 3px 0 #15803d, 0 4px 0 #166534, 0 5px 0 #14532d, 0 6px 1px rgba(0,0,0,.1), 0 0 5px rgba(34,197,94,.3), 0 1px 3px rgba(0,0,0,.3), 0 3px 5px rgba(0,0,0,.2), 0 5px 10px rgba(0,0,0,.25)',
                '3d-light': '0 1px 0 rgba(255,255,255,0.8), 0 2px 0 rgba(255,255,255,0.6), 0 3px 0 rgba(255,255,255,0.4), 0 4px 0 rgba(255,255,255,0.2), 0 5px 0 rgba(255,255,255,0.1), 0 6px 1px rgba(0,0,0,.05), 0 0 5px rgba(0,0,0,.05), 0 1px 3px rgba(0,0,0,.1), 0 3px 5px rgba(0,0,0,.1), 0 5px 10px rgba(0,0,0,.1)',
            },
        },
    },
    plugins: [
        require('tailwindcss-animate'),
        function({ addUtilities }: any) {
            const newUtilities = {
                '.text-shadow-sm': {
                    textShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
                },
                '.text-shadow': {
                    textShadow: '0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06)',
                },
                '.text-shadow-md': {
                    textShadow: '0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06)',
                },
                '.text-shadow-lg': {
                    textShadow: '0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05)',
                },
                '.text-shadow-xl': {
                    textShadow: '0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04)',
                },
                '.text-shadow-2xl': {
                    textShadow: '0 25px 50px rgba(0, 0, 0, 0.25)',
                },
                '.text-shadow-none': {
                    textShadow: 'none',
                },
                '.text-shadow-3d': {
                    textShadow: '0 1px 0 #ccc, 0 2px 0 #c9c9c9, 0 3px 0 #bbb, 0 4px 0 #b9b9b9, 0 5px 0 #aaa, 0 6px 1px rgba(0,0,0,.1), 0 0 5px rgba(0,0,0,.1), 0 1px 3px rgba(0,0,0,.3), 0 3px 5px rgba(0,0,0,.2), 0 5px 10px rgba(0,0,0,.25)',
                },
                '.text-shadow-3d-green': {
                    textShadow: '0 1px 0 #22c55e, 0 2px 0 #16a34a, 0 3px 0 #15803d, 0 4px 0 #166534, 0 5px 0 #14532d, 0 6px 1px rgba(0,0,0,.1), 0 0 5px rgba(34,197,94,.3), 0 1px 3px rgba(0,0,0,.3), 0 3px 5px rgba(0,0,0,.2), 0 5px 10px rgba(0,0,0,.25)',
                },
                '.text-shadow-3d-light': {
                    textShadow: '0 1px 0 rgba(255,255,255,0.8), 0 2px 0 rgba(255,255,255,0.6), 0 3px 0 rgba(255,255,255,0.4), 0 4px 0 rgba(255,255,255,0.2), 0 5px 0 rgba(255,255,255,0.1), 0 6px 1px rgba(0,0,0,.05), 0 0 5px rgba(0,0,0,.05), 0 1px 3px rgba(0,0,0,.1), 0 3px 5px rgba(0,0,0,.1), 0 5px 10px rgba(0,0,0,.1)',
                },
            }
            addUtilities(newUtilities)
        }
    ],
};
export default config;
