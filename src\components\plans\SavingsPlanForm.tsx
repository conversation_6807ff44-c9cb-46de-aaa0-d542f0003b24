"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  FiX, 
  FiDollarSign, 
  FiCalendar, 
  FiTrendingUp,
  FiTarget,
  FiClock,
  FiRepeat
} from 'react-icons/fi';
import { SavingsPlanFormData, CreateSavingsPlanData } from '../../types/savings';
import { savingsService } from '../../services/savings';

interface SavingsPlanFormProps {
  onClose: () => void;
  onSuccess: () => void;
  editPlan?: any; // Will be properly typed later
}

export default function SavingsPlanForm({ onClose, onSuccess, editPlan }: SavingsPlanFormProps) {
  const [formData, setFormData] = useState<SavingsPlanFormData>({
    name: editPlan?.name || '',
    description: editPlan?.description || '',
    planType: editPlan?.planType || 'INDIVIDUAL',
    targetAmount: editPlan?.targetAmount?.toString() || '',
    contributionAmount: editPlan?.contributionAmount?.toString() || '',
    frequency: editPlan?.frequency || 'MONTHLY',
    duration: editPlan?.duration?.toString() || '',
    autoDebit: editPlan?.autoDebit || false,
    startDate: editPlan?.startDate || new Date().toISOString().split('T')[0],
  });

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [interestProjection, setInterestProjection] = useState<any>(null);

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = 'Plan name is required';
    }

    if (!formData.targetAmount || parseFloat(formData.targetAmount) <= 0) {
      errors.targetAmount = 'Target amount must be greater than 0';
    }

    if (!formData.contributionAmount || parseFloat(formData.contributionAmount) <= 0) {
      errors.contributionAmount = 'Contribution amount must be greater than 0';
    }

    if (!formData.duration || parseInt(formData.duration) <= 0) {
      errors.duration = 'Duration must be greater than 0';
    }

    if (!formData.startDate) {
      errors.startDate = 'Start date is required';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const calculateProjection = async () => {
    if (formData.targetAmount && formData.contributionAmount && formData.duration) {
      try {
        const principal = parseFloat(formData.contributionAmount);
        const months = parseInt(formData.duration);
        const rate = 12; // Default 12% annual rate
        
        const projection = await savingsService.calculateInterest(
          principal,
          rate,
          months,
          12
        );
        setInterestProjection(projection);
      } catch (error) {
        console.error('Failed to calculate projection:', error);
      }
    }
  };

  React.useEffect(() => {
    calculateProjection();
  }, [formData.contributionAmount, formData.duration]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      setIsLoading(true);
      setError(null);

      const planData: CreateSavingsPlanData = {
        name: formData.name,
        description: formData.description,
        planType: formData.planType,
        targetAmount: parseFloat(formData.targetAmount),
        contributionAmount: parseFloat(formData.contributionAmount),
        frequency: formData.frequency,
        duration: parseInt(formData.duration),
        autoDebit: formData.autoDebit,
        startDate: formData.startDate,
      };

      if (editPlan) {
        await savingsService.updateSavingsPlan(editPlan.id, planData);
      } else {
        await savingsService.createSavingsPlan(planData);
      }

      onSuccess();
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to save plan');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-gray-900 border border-gray-800 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-800">
          <h2 className="text-xl font-bold text-white">
            {editPlan ? 'Edit Savings Plan' : 'Create New Savings Plan'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <FiX size={24} />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Error Display */}
          {error && (
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          )}

          {/* Plan Name */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Plan Name</label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:border-green-500 focus:outline-none text-white"
              placeholder="e.g., Emergency Fund, Vacation Savings"
            />
            {validationErrors.name && (
              <p className="text-red-400 text-xs mt-1">{validationErrors.name}</p>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Description (Optional)</label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={3}
              className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:border-green-500 focus:outline-none text-white resize-none"
              placeholder="Describe your savings goal..."
            />
          </div>

          {/* Plan Type */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Plan Type</label>
            <div className="grid grid-cols-3 gap-4">
              {[
                { value: 'INDIVIDUAL', label: 'Individual', icon: FiTrendingUp, color: 'green' },
                { value: 'TARGET', label: 'Target', icon: FiTarget, color: 'blue' },
                { value: 'GOAL', label: 'Goal', icon: FiDollarSign, color: 'purple' }
              ].map((type) => (
                <label
                  key={type.value}
                  className={`relative flex flex-col items-center p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                    formData.planType === type.value
                      ? `border-${type.color}-500 bg-${type.color}-500/10`
                      : 'border-gray-700 hover:border-gray-600'
                  }`}
                >
                  <input
                    type="radio"
                    name="planType"
                    value={type.value}
                    checked={formData.planType === type.value}
                    onChange={handleInputChange}
                    className="sr-only"
                  />
                  <type.icon className={`text-2xl mb-2 ${
                    formData.planType === type.value ? `text-${type.color}-400` : 'text-gray-400'
                  }`} />
                  <span className="text-sm font-medium text-white">{type.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Target Amount & Contribution */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-white mb-2">Target Amount (₦)</label>
              <div className="relative">
                <FiDollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="number"
                  name="targetAmount"
                  value={formData.targetAmount}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:border-green-500 focus:outline-none text-white"
                  placeholder="100000"
                  min="0"
                  step="1000"
                />
              </div>
              {validationErrors.targetAmount && (
                <p className="text-red-400 text-xs mt-1">{validationErrors.targetAmount}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-white mb-2">Contribution Amount (₦)</label>
              <div className="relative">
                <FiDollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="number"
                  name="contributionAmount"
                  value={formData.contributionAmount}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:border-green-500 focus:outline-none text-white"
                  placeholder="5000"
                  min="0"
                  step="100"
                />
              </div>
              {validationErrors.contributionAmount && (
                <p className="text-red-400 text-xs mt-1">{validationErrors.contributionAmount}</p>
              )}
            </div>
          </div>

          {/* Frequency & Duration */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-white mb-2">Frequency</label>
              <div className="relative">
                <FiRepeat className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <select
                  name="frequency"
                  value={formData.frequency}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:border-green-500 focus:outline-none text-white appearance-none"
                >
                  <option value="DAILY">Daily</option>
                  <option value="WEEKLY">Weekly</option>
                  <option value="MONTHLY">Monthly</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-white mb-2">Duration (Months)</label>
              <div className="relative">
                <FiClock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="number"
                  name="duration"
                  value={formData.duration}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:border-green-500 focus:outline-none text-white"
                  placeholder="12"
                  min="1"
                  max="120"
                />
              </div>
              {validationErrors.duration && (
                <p className="text-red-400 text-xs mt-1">{validationErrors.duration}</p>
              )}
            </div>
          </div>

          {/* Start Date */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Start Date</label>
            <div className="relative">
              <FiCalendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="date"
                name="startDate"
                value={formData.startDate}
                onChange={handleInputChange}
                className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:border-green-500 focus:outline-none text-white"
              />
            </div>
            {validationErrors.startDate && (
              <p className="text-red-400 text-xs mt-1">{validationErrors.startDate}</p>
            )}
          </div>

          {/* Auto Debit */}
          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              name="autoDebit"
              checked={formData.autoDebit}
              onChange={handleInputChange}
              className="w-4 h-4 text-green-600 bg-gray-800 border-gray-700 rounded focus:ring-green-500"
            />
            <label className="text-sm text-white">
              Enable automatic debit for contributions
            </label>
          </div>

          {/* Interest Projection */}
          {interestProjection && (
            <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
              <h4 className="text-green-400 font-medium mb-2">Projected Returns</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-400">Total Contributions:</span>
                  <p className="text-white font-medium">
                    {savingsService.formatCurrency(interestProjection.principal)}
                  </p>
                </div>
                <div>
                  <span className="text-gray-400">Interest Earned:</span>
                  <p className="text-green-400 font-medium">
                    {savingsService.formatCurrency(interestProjection.compoundInterest)}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex space-x-4 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-3 border border-gray-700 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="flex-1 px-4 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors disabled:opacity-50"
            >
              {isLoading ? 'Saving...' : editPlan ? 'Update Plan' : 'Create Plan'}
            </button>
          </div>
        </form>
      </motion.div>
    </div>
  );
}
