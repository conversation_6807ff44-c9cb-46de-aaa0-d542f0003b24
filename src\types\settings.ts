export interface AppSettings {
  id: string;
  key: string;
  value: string | number | boolean | object;
  type: 'STRING' | 'NUMBER' | 'BOOLEAN' | 'JSON' | 'ARRAY';
  category: 'GENERAL' | 'FINANCIAL' | 'SECURITY' | 'NOTIFICATIONS' | 'FEATURES' | 'LIMITS' | 'PENALTIES';
  name: string;
  description: string;
  isEditable: boolean;
  isPublic: boolean;
  isRequired: boolean;
  
  validationRules?: {
    min?: number;
    max?: number;
    pattern?: string;
    required?: boolean;
    options?: string[];
    customValidator?: string;
  };
  
  displayOrder: number;
  group?: string;
  dependsOn?: string[];
  
  updatedBy?: string;
  updatedAt: string;
  createdAt: string;
}

export interface FinancialSettings {
  // Interest Rates
  defaultInterestRate: number;
  minimumInterestRate: number;
  maximumInterestRate: number;
  compoundingFrequency: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'ANNUALLY';
  
  // Penalties
  earlyWithdrawalPenaltyRate: number;
  planClosurePenaltyRate: number;
  missedContributionPenaltyRate: number;
  latePenaltyGracePeriod: number; // in days
  maximumPenaltyAmount: number;
  
  // Fees
  withdrawalFeeRate: number;
  minimumWithdrawalFee: number;
  maximumWithdrawalFee: number;
  depositFeeRate: number;
  minimumDepositFee: number;
  maximumDepositFee: number;
  
  // Limits
  minimumDepositAmount: number;
  maximumDepositAmount: number;
  minimumWithdrawalAmount: number;
  maximumWithdrawalAmount: number;
  dailyTransactionLimit: number;
  monthlyTransactionLimit: number;
  
  // Plan Limits
  minimumPlanAmount: number;
  maximumPlanAmount: number;
  minimumPlanDuration: number; // in days
  maximumPlanDuration: number; // in days
  maximumActivePlansPerUser: number;
  
  // Group Savings
  minimumGroupSize: number;
  maximumGroupSize: number;
  minimumGroupContribution: number;
  maximumGroupContribution: number;
  groupCreationFee: number;
}

export interface SecuritySettings {
  // Authentication
  passwordMinLength: number;
  passwordRequireUppercase: boolean;
  passwordRequireLowercase: boolean;
  passwordRequireNumbers: boolean;
  passwordRequireSpecialChars: boolean;
  passwordExpiryDays: number;
  maxLoginAttempts: number;
  accountLockoutDuration: number; // in minutes
  
  // Session Management
  sessionTimeoutMinutes: number;
  maxConcurrentSessions: number;
  requireReauthForSensitiveActions: boolean;
  
  // Two-Factor Authentication
  twoFactorRequired: boolean;
  twoFactorMethods: ('SMS' | 'EMAIL' | 'AUTHENTICATOR')[];
  
  // KYC Requirements
  kycRequiredForDeposits: boolean;
  kycRequiredForWithdrawals: boolean;
  kycRequiredAmount: number;
  kycExpiryDays: number;
  
  // Transaction Security
  requireOtpForWithdrawals: boolean;
  requireOtpForLargeTransactions: boolean;
  largeTransactionThreshold: number;
  suspiciousActivityThreshold: number;
  
  // API Security
  rateLimitPerMinute: number;
  rateLimitPerHour: number;
  allowedOrigins: string[];
  requireApiKey: boolean;
}

export interface NotificationSettings {
  // Email Settings
  emailEnabled: boolean;
  emailProvider: 'SENDGRID' | 'MAILGUN' | 'SES' | 'SMTP';
  emailFromAddress: string;
  emailFromName: string;
  emailReplyTo: string;
  
  // SMS Settings
  smsEnabled: boolean;
  smsProvider: 'TWILIO' | 'NEXMO' | 'TERMII' | 'CUSTOM';
  smsSenderId: string;
  
  // Push Notifications
  pushEnabled: boolean;
  pushProvider: 'FCM' | 'APNS' | 'ONESIGNAL';
  
  // Notification Types
  notificationTypes: {
    type: string;
    enabled: boolean;
    channels: ('EMAIL' | 'SMS' | 'PUSH' | 'IN_APP')[];
    template: string;
    priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  }[];
  
  // Delivery Settings
  maxRetryAttempts: number;
  retryDelayMinutes: number;
  batchSize: number;
  quietHoursStart: string;
  quietHoursEnd: string;
  respectUserPreferences: boolean;
}

export interface FeatureSettings {
  // Core Features
  savingsPlansEnabled: boolean;
  groupSavingsEnabled: boolean;
  targetSavingsEnabled: boolean;
  autoContributionsEnabled: boolean;
  interestCalculationEnabled: boolean;
  
  // Payment Features
  paystackEnabled: boolean;
  bankTransferEnabled: boolean;
  cardPaymentsEnabled: boolean;
  ussdPaymentsEnabled: boolean;
  mobileMoneyEnabled: boolean;
  
  // Advanced Features
  referralProgramEnabled: boolean;
  loyaltyPointsEnabled: boolean;
  gamificationEnabled: boolean;
  socialFeaturesEnabled: boolean;
  analyticsEnabled: boolean;
  
  // Admin Features
  bulkOperationsEnabled: boolean;
  dataExportEnabled: boolean;
  auditLoggingEnabled: boolean;
  systemHealthMonitoring: boolean;
  
  // User Features
  profileCustomizationEnabled: boolean;
  notificationPreferencesEnabled: boolean;
  darkModeEnabled: boolean;
  multiLanguageEnabled: boolean;
  accessibilityFeaturesEnabled: boolean;
}

export interface UpdateSettingData {
  key: string;
  value: string | number | boolean | object;
  updatedBy?: string;
}

export interface SettingsCategory {
  name: string;
  description: string;
  icon?: string;
  settings: AppSettings[];
}

export interface SettingsValidationResult {
  isValid: boolean;
  errors: {
    key: string;
    message: string;
  }[];
  warnings: {
    key: string;
    message: string;
  }[];
}

export interface SettingsBackup {
  id: string;
  name: string;
  description?: string;
  settings: AppSettings[];
  createdBy: string;
  createdAt: string;
  restoredAt?: string;
  restoredBy?: string;
}

export interface CreateSettingsBackupData {
  name: string;
  description?: string;
  includeCategories?: string[];
  excludeKeys?: string[];
}

export interface RestoreSettingsData {
  backupId: string;
  includeCategories?: string[];
  excludeKeys?: string[];
  confirmRestore: boolean;
}
