"use client";

import { AnimatePresence, motion } from 'framer-motion';
import React, { useState } from 'react';
import {
    <PERSON>Book,
    FiExternalLink,
    FiHelpCircle,
    FiMail,
    FiMessageCircle,
    FiPhone,
    FiX
} from 'react-icons/fi';
import { useIntercom } from './IntercomWidget';

interface HelpOption {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  action: () => void;
  color: string;
}

export default function HelpButton() {
  const [isOpen, setIsOpen] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const { showSupport, showNewMessage, trackEvent } = useIntercom();

  useEffect(() => {
    setIsClient(true);
  }, []);

  const helpOptions: HelpOption[] = [
    {
      id: 'chat',
      title: 'Live Chat',
      description: 'Chat with our support team',
      icon: FiMessageCircle,
      action: () => {
        showSupport();
        trackEvent('help_chat_opened');
        setIsOpen(false);
      },
      color: 'text-green-400'
    },
    {
      id: 'message',
      title: 'Send Message',
      description: 'Send us a detailed message',
      icon: FiMail,
      action: () => {
        showNewMessage('Hi! I need help with...');
        trackEvent('help_message_opened');
        setIsOpen(false);
      },
      color: 'text-blue-400'
    },
    {
      id: 'faq',
      title: 'FAQ',
      description: 'Browse frequently asked questions',
      icon: FiBook,
      action: () => {
        window.open('/faq', '_blank');
        trackEvent('help_faq_opened');
        setIsOpen(false);
      },
      color: 'text-purple-400'
    },
    {
      id: 'phone',
      title: 'Call Support',
      description: '+234 (0) ************',
      icon: FiPhone,
      action: () => {
        window.open('tel:+2341234567890');
        trackEvent('help_phone_clicked');
        setIsOpen(false);
      },
      color: 'text-orange-400'
    }
  ];

  if (!isClient) {
    return null;
  }

  return (
    <>
      {/* Help Button */}
      <motion.button
        onClick={() => setIsOpen(!isOpen)}
        className="fixed bottom-6 right-6 z-50 w-14 h-14 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-full shadow-lg flex items-center justify-center transition-all duration-200 hover:scale-110"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.95 }}
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 1 }}
      >
        <motion.div
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.2 }}
        >
          {isOpen ? <FiX size={24} /> : <FiHelpCircle size={24} />}
        </motion.div>
      </motion.button>

      {/* Help Options Menu */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-40 bg-black/20 backdrop-blur-sm"
              onClick={() => setIsOpen(false)}
            />

            {/* Help Menu */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.8, y: 20 }}
              className="fixed bottom-24 right-6 z-50 w-80 bg-gray-900/95 backdrop-blur-lg border border-gray-700 rounded-2xl shadow-2xl overflow-hidden"
            >
              {/* Header */}
              <div className="p-4 border-b border-gray-700">
                <h3 className="text-lg font-semibold text-white flex items-center">
                  <FiHelpCircle className="mr-2 text-green-400" />
                  How can we help?
                </h3>
                <p className="text-sm text-gray-400 mt-1">
                  Choose an option below to get support
                </p>
              </div>

              {/* Help Options */}
              <div className="p-2">
                {helpOptions.map((option, index) => {
                  const Icon = option.icon;
                  return (
                    <motion.button
                      key={option.id}
                      onClick={option.action}
                      className="w-full p-3 rounded-lg hover:bg-gray-800/50 transition-colors text-left group"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      whileHover={{ x: 4 }}
                    >
                      <div className="flex items-center">
                        <div className={`p-2 rounded-lg bg-gray-800 group-hover:bg-gray-700 transition-colors`}>
                          <Icon className={`${option.color} text-lg`} />
                        </div>
                        <div className="ml-3 flex-1">
                          <div className="text-white font-medium text-sm">
                            {option.title}
                          </div>
                          <div className="text-gray-400 text-xs">
                            {option.description}
                          </div>
                        </div>
                        <FiExternalLink className="text-gray-500 text-sm opacity-0 group-hover:opacity-100 transition-opacity" />
                      </div>
                    </motion.button>
                  );
                })}
              </div>

              {/* Footer */}
              <div className="p-4 border-t border-gray-700 bg-gray-800/50">
                <div className="text-center">
                  <p className="text-xs text-gray-400">
                    Available 24/7 • Average response time: 2 minutes
                  </p>
                  <div className="flex items-center justify-center mt-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                    <span className="text-xs text-green-400">Support team online</span>
                  </div>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
}

// Floating Help Button for specific contexts
export function FloatingHelpButton({ 
  message, 
  position = 'bottom-right' 
}: { 
  message?: string;
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
}) {
  const { showNewMessage, trackEvent } = useIntercom();

  const positionClasses = {
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4'
  };

  const handleClick = () => {
    if (message) {
      showNewMessage(message);
    } else {
      showNewMessage();
    }
    trackEvent('floating_help_clicked', { position, message });
  };

  return (
    <motion.button
      onClick={handleClick}
      className={`fixed ${positionClasses[position]} z-40 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-full shadow-lg flex items-center transition-all duration-200 hover:scale-105`}
      initial={{ opacity: 0, scale: 0 }}
      animate={{ opacity: 1, scale: 1 }}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
    >
      <FiHelpCircle className="mr-2" size={16} />
      Need help?
    </motion.button>
  );
}

// Quick Help Tooltip
export function QuickHelpTooltip({ 
  children, 
  helpText, 
  helpAction 
}: { 
  children: React.ReactNode;
  helpText: string;
  helpAction?: () => void;
}) {
  const [isVisible, setIsVisible] = useState(false);
  const { showNewMessage, trackEvent } = useIntercom();

  const handleHelpClick = () => {
    if (helpAction) {
      helpAction();
    } else {
      showNewMessage(`I need help with: ${helpText}`);
    }
    trackEvent('quick_help_used', { helpText });
    setIsVisible(false);
  };

  return (
    <div className="relative inline-block">
      <div
        onMouseEnter={() => setIsVisible(true)}
        onMouseLeave={() => setIsVisible(false)}
      >
        {children}
      </div>
      
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-50"
          >
            <div className="bg-gray-900 text-white text-xs rounded-lg p-2 shadow-lg border border-gray-700 whitespace-nowrap">
              <div className="flex items-center">
                <span className="mr-2">{helpText}</span>
                <button
                  onClick={handleHelpClick}
                  className="text-blue-400 hover:text-blue-300 ml-1"
                >
                  <FiHelpCircle size={12} />
                </button>
              </div>
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
