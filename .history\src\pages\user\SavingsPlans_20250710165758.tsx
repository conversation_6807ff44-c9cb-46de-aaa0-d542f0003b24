"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  FiPlus, 
  FiTrendingUp, 
  FiTarget, 
  FiCalendar,
  FiDollarSign,
  FiPause,
  FiPlay,
  FiEdit,
  FiTrash2,
  FiEye
} from 'react-icons/fi';
import { SavingsPlan, SavingsStats } from '../../types/savings';
import { savingsService } from '../../services/savings';
import { useAuth } from '../../hooks/use-auth';
import UserLayout from '../../components/UserLayout';
import { useKYCEnforcement } from '../../hooks/use-kyc-enforcement';
import SavingsPlanForm from '../../components/plans/SavingsPlanForm';
import SavingsGoalModal from '../../components/savings/SavingsGoalModal';
import TargetSavingsModal from '../../components/savings/TargetSavingsModal';

export default function SavingsPlans() {
  const { user } = useAuth();
  const kycApproved = useKYCEnforcement({ redirect: true });
  const [plans, setPlans] = useState<SavingsPlan[]>([]);
  const [stats, setStats] = useState<SavingsStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showGoalModal, setShowGoalModal] = useState(false);
  const [showTargetModal, setShowTargetModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<SavingsPlan | null>(null);

  useEffect(() => {
    loadSavingsData();
  }, []);

  const loadSavingsData = async () => {
    try {
      setIsLoading(true);
      const [plansData, statsData] = await Promise.all([
        savingsService.getSavingsPlans(),
        savingsService.getSavingsStats()
      ]);
      setPlans(plansData);
      setStats(statsData);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to load savings data');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePausePlan = async (planId: string) => {
    try {
      await savingsService.pauseSavingsPlan(planId);
      await loadSavingsData();
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to pause plan');
    }
  };

  const handleResumePlan = async (planId: string) => {
    try {
      await savingsService.resumeSavingsPlan(planId);
      await loadSavingsData();
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to resume plan');
    }
  };

  const handleDeletePlan = async (planId: string) => {
    if (!confirm('Are you sure you want to delete this savings plan?')) return;
    
    try {
      await savingsService.deleteSavingsPlan(planId);
      await loadSavingsData();
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to delete plan');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'text-green-400 bg-green-400/10';
      case 'PAUSED': return 'text-yellow-400 bg-yellow-400/10';
      case 'COMPLETED': return 'text-blue-400 bg-blue-400/10';
      case 'CANCELLED': return 'text-red-400 bg-red-400/10';
      default: return 'text-gray-400 bg-gray-400/10';
    }
  };

  const getPlanTypeIcon = (type: string) => {
    switch (type) {
      case 'INDIVIDUAL': return <FiTrendingUp className="text-green-400" />;
      case 'TARGET': return <FiTarget className="text-blue-400" />;
      case 'GOAL': return <FiDollarSign className="text-purple-400" />;
      default: return <FiTrendingUp className="text-green-400" />;
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-black text-white p-6">
        <div className="max-w-7xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-800 rounded w-64 mb-6"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-800 rounded-lg"></div>
              ))}
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-64 bg-gray-800 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <UserLayout>
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold mb-2">Savings Plans</h1>
            <p className="text-gray-400">Manage your savings goals and track progress</p>
          </div>
          
          <div className="flex space-x-4">
            <button
              onClick={() => setShowGoalModal(true)}
              className="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
            >
              <FiTarget />
              <span>Set Goal</span>
            </button>
            <button
              onClick={() => setShowTargetModal(true)}
              className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
            >
              <FiDollarSign />
              <span>Target Savings</span>
            </button>
            <button
              onClick={() => setShowCreateForm(true)}
              className="bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
            >
              <FiPlus />
              <span>New Plan</span>
            </button>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-6"
          >
            <p className="text-red-400">{error}</p>
            <button
              onClick={() => setError(null)}
              className="text-red-300 hover:text-red-200 text-sm mt-2"
            >
              Dismiss
            </button>
          </motion.div>
        )}

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-gray-900/50 border border-gray-800 rounded-lg p-6"
            >
              <div className="flex items-center justify-between mb-4">
                <FiDollarSign className="text-green-400 text-2xl" />
                <span className="text-green-400 text-sm font-medium">Total Saved</span>
              </div>
              <p className="text-2xl font-bold">{savingsService.formatCurrency(stats.totalSaved)}</p>
              <p className="text-gray-400 text-sm">+{stats.savingsRate}% this month</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-gray-900/50 border border-gray-800 rounded-lg p-6"
            >
              <div className="flex items-center justify-between mb-4">
                <FiTrendingUp className="text-blue-400 text-2xl" />
                <span className="text-blue-400 text-sm font-medium">Interest Earned</span>
              </div>
              <p className="text-2xl font-bold">{savingsService.formatCurrency(stats.totalInterestEarned)}</p>
              <p className="text-gray-400 text-sm">Projected: {savingsService.formatCurrency(stats.projectedEarnings)}</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-gray-900/50 border border-gray-800 rounded-lg p-6"
            >
              <div className="flex items-center justify-between mb-4">
                <FiTarget className="text-purple-400 text-2xl" />
                <span className="text-purple-400 text-sm font-medium">Active Plans</span>
              </div>
              <p className="text-2xl font-bold">{stats.activePlans}</p>
              <p className="text-gray-400 text-sm">{stats.completedGoals} goals completed</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-gray-900/50 border border-gray-800 rounded-lg p-6"
            >
              <div className="flex items-center justify-between mb-4">
                <FiCalendar className="text-orange-400 text-2xl" />
                <span className="text-orange-400 text-sm font-medium">Monthly</span>
              </div>
              <p className="text-2xl font-bold">{savingsService.formatCurrency(stats.monthlyContributions)}</p>
              <p className="text-gray-400 text-sm">Average contribution</p>
            </motion.div>
          </div>
        )}

        {/* Savings Plans Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {plans.map((plan, index) => (
            <motion.div
              key={plan.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-gray-900/50 border border-gray-800 rounded-lg p-6 hover:border-green-500/30 transition-colors"
            >
              {/* Plan Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  {getPlanTypeIcon(plan.planType)}
                  <div>
                    <h3 className="font-semibold text-lg">{plan.name}</h3>
                    <p className="text-gray-400 text-sm">{plan.planType.toLowerCase()} savings</p>
                  </div>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(plan.status)}`}>
                  {plan.status}
                </span>
              </div>

              {/* Progress */}
              <div className="mb-4">
                <div className="flex justify-between text-sm mb-2">
                  <span className="text-gray-400">Progress</span>
                  <span className="text-white">
                    {savingsService.calculateProgress(plan.currentAmount, plan.targetAmount).toFixed(1)}%
                  </span>
                </div>
                <div className="w-full bg-gray-800 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-green-500 to-green-600 h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${Math.min(savingsService.calculateProgress(plan.currentAmount, plan.targetAmount), 100)}%`
                    }}
                  ></div>
                </div>
              </div>

              {/* Amounts */}
              <div className="space-y-2 mb-4">
                <div className="flex justify-between">
                  <span className="text-gray-400">Current</span>
                  <span className="text-white font-medium">
                    {savingsService.formatCurrency(plan.currentAmount)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Target</span>
                  <span className="text-green-400 font-medium">
                    {savingsService.formatCurrency(plan.targetAmount)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Interest Rate</span>
                  <span className="text-blue-400 font-medium">{plan.interestRate}% p.a.</span>
                </div>
              </div>

              {/* Actions */}
              <div className="flex space-x-2">
                <button
                  onClick={() => setSelectedPlan(plan)}
                  className="flex-1 bg-green-600 hover:bg-green-700 px-3 py-2 rounded text-sm transition-colors flex items-center justify-center space-x-1"
                >
                  <FiEye size={14} />
                  <span>View</span>
                </button>
                
                {plan.status === 'ACTIVE' ? (
                  <button
                    onClick={() => handlePausePlan(plan.id)}
                    className="bg-yellow-600 hover:bg-yellow-700 px-3 py-2 rounded text-sm transition-colors"
                  >
                    <FiPause size={14} />
                  </button>
                ) : plan.status === 'PAUSED' ? (
                  <button
                    onClick={() => handleResumePlan(plan.id)}
                    className="bg-green-600 hover:bg-green-700 px-3 py-2 rounded text-sm transition-colors"
                  >
                    <FiPlay size={14} />
                  </button>
                ) : null}
                
                <button
                  onClick={() => handleDeletePlan(plan.id)}
                  className="bg-red-600 hover:bg-red-700 px-3 py-2 rounded text-sm transition-colors"
                >
                  <FiTrash2 size={14} />
                </button>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Empty State */}
        {plans.length === 0 && !isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-16"
          >
            <FiTarget className="text-6xl text-gray-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">No Savings Plans Yet</h3>
            <p className="text-gray-400 mb-6">Create your first savings plan to start building wealth</p>
            <button
              onClick={() => setShowCreateForm(true)}
              className="bg-green-600 hover:bg-green-700 px-6 py-3 rounded-lg flex items-center space-x-2 mx-auto transition-colors"
            >
              <FiPlus />
              <span>Create Your First Plan</span>
            </button>
          </motion.div>
        )}
      </div>

      {/* Modals */}
      {showCreateForm && (
        <SavingsPlanForm
          onClose={() => setShowCreateForm(false)}
          onSuccess={() => {
            setShowCreateForm(false);
            loadSavingsData();
          }}
        />
      )}

      {showGoalModal && (
        <SavingsGoalModal
          onClose={() => setShowGoalModal(false)}
          onSuccess={() => {
            setShowGoalModal(false);
            loadSavingsData();
          }}
        />
      )}

      {showTargetModal && (
        <TargetSavingsModal
          onClose={() => setShowTargetModal(false)}
          onSuccess={() => {
            setShowTargetModal(false);
            loadSavingsData();
          }}
        />
      )}
    </UserLayout>
  );
}
