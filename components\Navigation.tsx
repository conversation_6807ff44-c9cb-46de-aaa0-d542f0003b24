"use client";

import { useState, useEffect } from "react";
import { Button } from "./ui/button";
import { motion } from "framer-motion";
import { FiMenu, FiX, FiUser, FiMail } from "react-icons/fi";
import KojaSaveLogo from "./KojaSaveLogo";

export default function Navigation() {
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <motion.nav
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        scrolled
          ? "bg-gray-900/95 backdrop-blur-md border-b border-gray-800"
          : "bg-transparent"
      }`}
      data-oid="nav-main"
    >
      <div
        className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"
        data-oid="ykjmwu2"
      >
        <div
          className="flex justify-between items-center h-16"
          data-oid="-ut509i"
        >
          {/* Logo */}
          <motion.div
            className="flex items-center"
            whileHover={{ scale: 1.05 }}
            data-oid="nav-logo"
          >
            <KojaSaveLogo
              size={32}
              showText={true}
              textClassName="text-xl"
            />
          </motion.div>

          {/* Desktop Navigation */}
          <div
            className="hidden md:flex items-center space-x-8"
            data-oid="nav-desktop"
          >
            <a
              href="#features"
              className="text-gray-300 hover:text-green-400 transition-colors duration-300"
              data-oid="__e0kf:"
            >
              Features
            </a>
            <a
              href="#about"
              className="text-gray-300 hover:text-green-400 transition-colors duration-300"
              data-oid="z..opr0"
            >
              About
            </a>
            <a
              href="#contact"
              className="text-gray-300 hover:text-green-400 transition-colors duration-300"
              data-oid="8zt1ss4"
            >
              Contact
            </a>
            <Button variant="outline" size="sm" data-oid="aknbu0n">
              <FiUser className="mr-2" data-oid="j:pz72a" />
              Login
            </Button>
            <Button variant="primary" size="sm" data-oid="l5kuxln">
              Sign Up
            </Button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden" data-oid="nav-mobile-toggle">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="text-gray-300 hover:text-green-400 transition-colors duration-300"
              data-oid="ni4brh3"
            >
              {isOpen ? (
                <FiX size={24} data-oid="iot.ad4" />
              ) : (
                <FiMenu size={24} data-oid="qrnha3y" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="md:hidden bg-gray-900/95 backdrop-blur-md border-t border-gray-800"
            data-oid="nav-mobile-menu"
          >
            <div className="px-2 pt-2 pb-3 space-y-1" data-oid="2k-_n8h">
              <a
                href="#features"
                className="block px-3 py-2 text-gray-300 hover:text-green-400 transition-colors duration-300"
                onClick={() => setIsOpen(false)}
                data-oid="u6sixtf"
              >
                Features
              </a>
              <a
                href="#about"
                className="block px-3 py-2 text-gray-300 hover:text-green-400 transition-colors duration-300"
                onClick={() => setIsOpen(false)}
                data-oid="lipuebz"
              >
                About
              </a>
              <a
                href="#contact"
                className="block px-3 py-2 text-gray-300 hover:text-green-400 transition-colors duration-300"
                onClick={() => setIsOpen(false)}
                data-oid="34q1.d7"
              >
                Contact
              </a>
              <div
                className="flex flex-col space-y-2 px-3 pt-2"
                data-oid="e8esvsg"
              >
                <Button variant="outline" size="sm" data-oid="g:q-3t:">
                  <FiUser className="mr-2" data-oid="t5pf-kn" />
                  Login
                </Button>
                <Button variant="primary" size="sm" data-oid="wfaeyah">
                  Sign Up
                </Button>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </motion.nav>
  );
}
