"use client";

import {
  Shield,
  TrendingUp,
  Smartphone,
  DollarSign,
  Target,
  PiggyBank,
} from "lucide-react";

const features = [
  {
    icon: <PiggyBank className="w-8 h-8" data-oid="vjsi0iy" />,
    title: "Smart Savings",
    description:
      "Automated savings that adapt to your spending patterns and help you reach your goals faster.",
  },
  {
    icon: <Shield className="w-8 h-8" data-oid="28:0jks" />,
    title: "Bank-Level Security",
    description:
      "Your money is protected with 256-bit encryption and FDIC insurance up to $250,000.",
  },
  {
    icon: <TrendingUp className="w-8 h-8" data-oid="ma-bsg5" />,
    title: "High-Yield Returns",
    description:
      "Earn up to 4.5% APY on your savings - significantly higher than traditional banks.",
  },
  {
    icon: <Target className="w-8 h-8" data-oid="v9ns424" />,
    title: "Goal Tracking",
    description:
      "Set and track multiple savings goals with visual progress indicators and smart recommendations.",
  },
  {
    icon: <Smartphone className="w-8 h-8" data-oid="bks61z7" />,
    title: "Mobile First",
    description:
      "Manage your savings on-the-go with our intuitive mobile app available on iOS and Android.",
  },
  {
    icon: <DollarSign className="w-8 h-8" data-oid="mhm:o74" />,
    title: "No Hidden Fees",
    description:
      "Transparent pricing with no monthly fees, no minimum balance requirements, and no surprises.",
  },
];

export const FeaturesSection = () => {
  return (
    <section id="features" className="py-20 px-4 relative" data-oid="cvaer8j">
      <div
        className="absolute inset-0 bg-gradient-to-b from-transparent via-green-900/10 to-transparent"
        data-oid="1iqfk0z"
      ></div>
      <div className="max-w-7xl mx-auto relative z-10" data-oid="0j1mvwx">
        <div className="text-center mb-16" data-oid=".79z0rx">
          <h2
            className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-white to-green-400 bg-clip-text text-transparent"
            data-oid="3ppkykz"
          >
            Why Choose Koja Save?
          </h2>
          <p
            className="text-xl text-gray-300 max-w-3xl mx-auto"
            data-oid="l:7vqkg"
          >
            Discover the features that make Koja Save the smartest choice for
            your financial future
          </p>
        </div>

        <div
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
          data-oid="48kwure"
        >
          {features.map((feature, index) => (
            <div
              key={index}
              className="group bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-green-500/20 rounded-2xl p-8 hover:border-green-500/40 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl hover:shadow-green-500/10 stagger-animation"
              style={{ "--stagger": index } as React.CSSProperties}
              data-oid="vb2:x5n"
            >
              <div
                className="text-green-400 mb-4 group-hover:scale-110 transition-transform duration-300"
                data-oid="u8y_id6"
              >
                {feature.icon}
              </div>
              <h3
                className="text-xl font-semibold mb-3 text-white group-hover:text-green-400 transition-colors"
                data-oid=".biehp4"
              >
                {feature.title}
              </h3>
              <p className="text-gray-300 leading-relaxed" data-oid="w_08ib9">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};
