{"name": "betterinterest-app", "version": "1.0.0", "private": true, "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "next dev --turbo", "dev:backend": "cd backend && npm run dev", "build": "next build && cd backend && npm run build", "start": "concurrently \"npm run start:frontend\" \"npm run start:backend\"", "start:frontend": "next start", "start:backend": "cd backend && npm start", "lint": "next lint", "format": "prettier --write .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:services": "jest src/services", "test:components": "jest src/components", "test:e2e": "playwright test", "type-check": "tsc --noEmit", "setup:backend": "cd backend && npm install"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@radix-ui/react-slot": "^1.1.0", "@react-three/drei": "^9.88.0", "@react-three/fiber": "^8.15.0", "@use-gesture/react": "^10.3.1", "aos": "^2.3.4", "bcryptjs": "^3.0.2", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "framer-motion": "^12.23.0", "jsonwebtoken": "^9.0.2", "lottie-react": "^2.4.1", "lucide-react": "^0.525.0", "mongoose": "^8.16.3", "next": "^14.2.30", "nodemon": "^3.1.10", "react": "^18", "react-chartjs-2": "^5.3.0", "react-dom": "^18", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-spring": "^10.0.1", "recharts": "^3.0.2", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "three": "^0.160.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.8", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "babel-jest": "^29.7.0", "concurrently": "^9.2.0", "eslint": "^8", "eslint-config-next": "^15.1.6", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "prettier": "^3.3.3", "tailwindcss": "^3.4.1", "typescript": "^5"}}