"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../../../src/hooks/use-auth';
import { useRouter } from 'next/navigation';
import DashboardLayout from '../../../src/components/dashboard/DashboardLayout';
import { StatCard, ActionCard } from '../../../src/components/dashboard/DashboardCard';
import { FormModal } from '../../../src/components/ui/Modal';
import { FormInput, SelectInput, Textarea } from '../../../src/components/ui/FormInput';
import { Button } from '../../../src/components/ui/Button';
import { Card3D } from '../../../src/components/ui/Card3D';
import { 
  FiPlus, 
  FiUsers, 
  FiDollarSign, 
  FiTarget,
  FiCalendar,
  FiShare2,
  FiUserPlus,
  FiEye,
  FiCopy
} from 'react-icons/fi';
import { groupSavingsService } from '../../../src/services/groupSavings';

interface GroupSaving {
  id: string;
  name: string;
  description: string;
  targetAmount: number;
  currentAmount: number;
  memberCount: number;
  maxMembers: number;
  contributionAmount: number;
  frequency: 'WEEKLY' | 'MONTHLY';
  status: 'RECRUITING' | 'ACTIVE' | 'COMPLETED';
  isPublic: boolean;
  createdBy: string;
  startDate: string;
  endDate: string;
  inviteCode: string;
  isOwner: boolean;
  progress: number;




export default function GroupSavingsPage() {
// Map backend group object to GroupSaving type for frontend
const mapGroup = (g: any, user?: any): GroupSaving => ({
  id: g._id || g.id,
  name: g.name || g.title || '',
  description: g.description || '',
  targetAmount: g.targetAmount ?? g.target_amount ?? 0,
  currentAmount: g.currentAmount ?? g.savedAmount ?? 0,
  memberCount: g.members ? g.members.length : g.memberCount || 0,
  maxMembers: g.maxMembers || g.max_members || 0,
  contributionAmount: g.contributionAmount || g.depositAmount || 0,
  frequency: (g.frequency || g.depositFrequency || 'MONTHLY').toUpperCase(),
  status: g.status ? g.status.toUpperCase() : 'ACTIVE',
  isPublic: g.isPublic || false,
  createdBy: g.creator?.name || '',
  startDate: g.startDate || g.createdAt || '',
  endDate: g.endDate || g.targetDate || '',
  inviteCode: g.inviteCode || '',
  isOwner: user && ((g.owner === user.id || g.owner === user._id) || (g.creatorId === user.id || g.creatorId === user._id)),
  progress: (g.targetAmount ?? g.target_amount) > 0 ? Math.round(((g.currentAmount ?? g.savedAmount ?? 0) / (g.targetAmount ?? g.target_amount)) * 100) : 0,
});

export default function GroupSavingsPage() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const [groupSavings, setGroupSavings] = useState<GroupSaving[]>([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showJoinModal, setShowJoinModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'my-groups' | 'discover'>('my-groups');
  const [joinCode, setJoinCode] = useState('');
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    targetAmount: '',
    maxMembers: '10',
    contributionAmount: '',
    frequency: 'MONTHLY',
    isPublic: false,
    duration: '12',
  });

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login');
      return;
    }
    if (!isLoading && user?.role === 'ADMIN') {
      router.push('/admin/dashboard');
      return;
    }
    // Fetch user's groups and public groups from backend
    const fetchGroups = async () => {
      try {
        setLoading(true);
        // Fetch user's groups
        const myGroupsRes = await groupSavingsService.getUserGroups();
        // Fetch all public groups (for discover tab)
        const allGroupsRes = await groupSavingsService.getAllGroups();
        // allGroupsRes is now an array, not .groups
        setGroupSavings([
          ...myGroupsRes.map(g => mapGroup(g, user)),
          ...allGroupsRes.filter((g: any) => !myGroupsRes.some((mg: any) => (mg._id || mg.id) === (g._id || g.id))).map(g => mapGroup(g, user))
        ]);
      } catch (error) {
        console.error('Failed to fetch group savings:', error);
      } finally {
        setLoading(false);
      }
    };
    fetchGroups();
  }, [isAuthenticated, isLoading, user, router]);

  const handleCreateGroup = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      // Add required fields for backend
      const payload = {
        name: formData.name,
        description: formData.description,
        targetAmount: parseInt(formData.targetAmount),
        maxMembers: parseInt(formData.maxMembers),
        contributionAmount: parseInt(formData.contributionAmount),
        frequency: formData.frequency as 'WEEKLY' | 'MONTHLY',
        isPublic: formData.isPublic,
        duration: parseInt(formData.duration),
        startDate: new Date().toISOString(),
        category: 'GENERAL',
      };
      // Ensure category is a valid enum value
      const created = await groupSavingsService.createGroup({ ...payload, category: 'GENERAL' });
      // Use the top-level mapGroup function
      const newGroup = mapGroup(created, user);
      setGroupSavings([...groupSavings, newGroup]);
      setShowCreateModal(false);
      setFormData({
        name: '',
        description: '',
        targetAmount: '',
        maxMembers: '10',
        contributionAmount: '',
        frequency: 'MONTHLY',
        isPublic: false,
        duration: '12',
      });
    } catch (error) {
      console.error('Failed to create group:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleJoinGroup = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      const data = { inviteCode: joinCode };
      // joinGroup expects groupId and inviteCode, but only inviteCode is available from UI
      // We'll try to find the groupId from the public groups or user's groups
      const allGroups = await groupSavingsService.getAllGroups();
      const groupToJoin = allGroups.find((g: any) => g.inviteCode === joinCode || g.inviteCode === joinCode.toUpperCase());
      if (!groupToJoin) throw new Error('Group not found');
      await groupSavingsService.joinGroup({ groupId: groupToJoin.id, inviteCode: joinCode });
      // Optionally refetch groups after joining
      const myGroupsRes = await groupSavingsService.getUserGroups();
      const allGroupsRes = await groupSavingsService.getAllGroups();
      setGroupSavings([
        ...myGroupsRes.map(g => mapGroup(g, user)),
        ...allGroupsRes.filter((g: any) => !myGroupsRes.some((mg: any) => (mg.id || mg._id) === (g.id || g._id))).map(g => mapGroup(g, user))
      ]);
      setShowJoinModal(false);
      setJoinCode('');
    } catch (error) {
      alert('Invalid invite code or group is full');
      console.error('Failed to join group:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({ 
      ...prev, 
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value 
    }));
  };

  const copyInviteCode = (code: string) => {
    navigator.clipboard.writeText(code);
    // You could add a toast notification here
  };

  const myGroups = groupSavings.filter(group => group.isOwner || group.memberCount > 0);
  const publicGroups = groupSavings.filter(group => group.isPublic && group.status === 'RECRUITING');

  const totalContributions = myGroups.reduce((sum, group) => sum + group.currentAmount, 0);
  const activeGroups = myGroups.filter(group => group.status === 'ACTIVE').length;

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto mb-4"></div>
            <p className="text-gray-400">Loading group savings...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!isAuthenticated || user?.role === 'ADMIN') {
    return null;
  }

  return (
    <DashboardLayout title="Group Savings">
      <div className="space-y-6">
        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <StatCard
            title="Total Contributions"
            value={`₦${totalContributions.toLocaleString()}`}
            subtitle="Across all groups"
            icon={FiDollarSign}
            color="green"
          />
          <StatCard
            title="Active Groups"
            value={activeGroups}
            subtitle={`${myGroups.length} total joined`}
            icon={FiUsers}
            color="blue"
          />
          <StatCard
            title="Monthly Commitment"
            value="₦95,000"
            subtitle="Total scheduled"
            icon={FiTarget}
            color="purple"
          />
        </div>

        {/* Actions */}
        <div className="flex justify-between items-center">
          <div className="flex space-x-4">
            <button
              onClick={() => setActiveTab('my-groups')}
              className={`px-4 py-2 rounded-lg transition-colors ${
                activeTab === 'my-groups' 
                  ? 'bg-green-600 text-white' 
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              My Groups
            </button>
            <button
              onClick={() => setActiveTab('discover')}
              className={`px-4 py-2 rounded-lg transition-colors ${
                activeTab === 'discover' 
                  ? 'bg-green-600 text-white' 
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              Discover
            </button>
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={() => setShowJoinModal(true)}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              <FiUserPlus className="w-4 h-4" />
              <span>Join Group</span>
            </button>
            <button
              onClick={() => setShowCreateModal(true)}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
            >
              <FiPlus className="w-4 h-4" />
              <span>Create Group</span>
            </button>
          </div>
        </div>

        {/* Groups Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {(activeTab === 'my-groups' ? myGroups : publicGroups).map((group) => (
            <motion.div
              key={group.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-gray-900/50 border border-gray-800 rounded-lg p-6 hover:border-gray-700 transition-colors"
            >
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-1">{group.name}</h3>
                  <p className="text-gray-400 text-sm">{group.description}</p>
                </div>
                <div className={`px-2 py-1 rounded-full text-xs ${
                  group.status === 'ACTIVE' ? 'bg-green-500/20 text-green-400' :
                  group.status === 'RECRUITING' ? 'bg-yellow-500/20 text-yellow-400' :
                  'bg-blue-500/20 text-blue-400'
                }`}>
                  {group.status}
                </div>
              </div>

              <div className="space-y-3 mb-4">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">Progress</span>
                  <span className="text-green-400">{group.progress}%</span>
                </div>
                <div className="w-full bg-gray-800 rounded-full h-2">
                  <div 
                    className="bg-green-500 h-2 rounded-full transition-all duration-300" 
                    style={{ width: `${group.progress}%` }}
                  />
                </div>
                <div className="flex justify-between text-sm text-gray-400">
                  <span>₦{group.currentAmount.toLocaleString()}</span>
                  <span>₦{group.targetAmount.toLocaleString()}</span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                <div>
                  <p className="text-gray-400">Members</p>
                  <p className="text-white">{group.memberCount}/{group.maxMembers}</p>
                </div>
                <div>
                  <p className="text-gray-400">Contribution</p>
                  <p className="text-white">₦{group.contributionAmount.toLocaleString()}</p>
                </div>
              </div>

              <div className="flex space-x-2">
                <button className="flex-1 px-3 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-lg transition-colors flex items-center justify-center space-x-1">
                  <FiEye className="w-4 h-4" />
                  <span>View</span>
                </button>
                {group.isOwner && (
                  <button 
                    onClick={() => copyInviteCode(group.inviteCode)}
                    className="px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors flex items-center space-x-1"
                  >
                    <FiCopy className="w-4 h-4" />
                    <span>{group.inviteCode}</span>
                  </button>
                )}
              </div>
            </motion.div>
          ))}
        </div>

        {/* Create Group Modal */}
        <FormModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          title="Create Group Savings"
          onSubmit={handleCreateGroup}
          submitText="Create Group"
          isLoading={loading}
          size="lg"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormInput
              label="Group Name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="e.g., Family Emergency Fund"
              required
            />

            <FormInput
              label="Max Members"
              name="maxMembers"
              type="number"
              value={formData.maxMembers}
              onChange={handleInputChange}
              placeholder="10"
              required
            />

            <div className="md:col-span-2">
              <Textarea
                label="Description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Describe the purpose of this group savings..."
                rows={3}
                required
              />
            </div>

            <FormInput
              label="Target Amount"
              name="targetAmount"
              type="number"
              value={formData.targetAmount}
              onChange={handleInputChange}
              placeholder="5000000"
              required
            />

            <FormInput
              label="Contribution Amount"
              name="contributionAmount"
              type="number"
              value={formData.contributionAmount}
              onChange={handleInputChange}
              placeholder="50000"
              required
            />

            <SelectInput
              label="Frequency"
              name="frequency"
              value={formData.frequency}
              onChange={handleInputChange}
              options={[
                { value: 'WEEKLY', label: 'Weekly' },
                { value: 'MONTHLY', label: 'Monthly' },
              ]}
              required
            />

            <FormInput
              label="Duration (months)"
              name="duration"
              type="number"
              value={formData.duration}
              onChange={handleInputChange}
              placeholder="12"
              required
            />

            <div className="md:col-span-2">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  name="isPublic"
                  checked={formData.isPublic}
                  onChange={handleInputChange}
                  className="w-4 h-4 text-green-600 bg-gray-800 border-gray-600 rounded focus:ring-green-500"
                />
                <span className="text-gray-300">Make this group public (others can discover and join)</span>
              </label>
            </div>
          </div>
        </FormModal>

        {/* Join Group Modal */}
        <FormModal
          isOpen={showJoinModal}
          onClose={() => setShowJoinModal(false)}
          title="Join Group Savings"
          onSubmit={handleJoinGroup}
          submitText="Join Group"
          isLoading={loading}
          size="sm"
        >
          <FormInput
            label="Invite Code"
            name="joinCode"
            value={joinCode}
            onChange={(e) => setJoinCode(e.target.value)}
            placeholder="Enter group invite code"
            required
          />
          <p className="text-gray-400 text-sm">
            Enter the invite code shared by the group owner to join their savings group.
          </p>
        </FormModal>
      </div>
    </DashboardLayout>
  );
}
