# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:3001/api
NEXT_PUBLIC_WS_URL=ws://localhost:3001

# App Configuration
NEXT_PUBLIC_APP_URL=https://betterinterest.vercel.app
NEXT_PUBLIC_APP_NAME="BetterInterest"

# Payment Integration
NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY=pk_test_your_paystack_public_key_here

# Authentication
NEXTAUTH_SECRET=your_nextauth_secret_here
NEXTAUTH_URL=http://localhost:3000

# Database (if using local database)
DATABASE_URL=postgresql://username:password@localhost:5432/savings_platform

# File Upload
NEXT_PUBLIC_MAX_FILE_SIZE=5242880
NEXT_PUBLIC_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# Real-time Features
NEXT_PUBLIC_ENABLE_WEBSOCKET=true
NEXT_PUBLIC_NOTIFICATION_SOUND=true

# Analytics (Optional)
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX

# Error Tracking (Optional)
NEXT_PUBLIC_SENTRY_DSN=https://your-sentry-dsn-here

# Feature Flags
NEXT_PUBLIC_ENABLE_GROUP_SAVINGS=true
NEXT_PUBLIC_ENABLE_TARGET_SAVINGS=true
NEXT_PUBLIC_ENABLE_KYC_VERIFICATION=true
NEXT_PUBLIC_ENABLE_ADMIN_DASHBOARD=true

# Development
NODE_ENV=development
NEXT_PUBLIC_DEBUG=false
