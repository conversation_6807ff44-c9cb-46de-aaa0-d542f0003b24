"use client";

import { motion } from "framer-motion";
import {
  FiSmartphone,
  FiMonitor,
  FiCreditCard,
  FiBarChart,
  FiShield,
  FiSettings,
  FiTrendingUp,
  FiDollarSign,
  FiPieChart,
  FiTarget
} from "react-icons/fi";

interface AppCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  type: 'mobile' | 'desktop' | 'web';
  features: string[];
  delay?: number;
}

const AppCard = ({ title, description, icon, type, features, delay = 0 }: AppCardProps) => {
  const getDeviceFrame = () => {
    switch (type) {
      case 'mobile':
        return (
          <div className="relative w-48 h-80 mx-auto">
            <div className="screen-mockup w-full h-full">
              <div className="screen-content">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-6 h-6 bg-green-400 rounded-lg flex items-center justify-center">
                    {icon}
                  </div>
                  <div className="flex space-x-1">
                    <div className="w-1 h-1 bg-green-400 rounded-full"></div>
                    <div className="w-1 h-1 bg-green-400 rounded-full"></div>
                    <div className="w-1 h-1 bg-green-400 rounded-full"></div>
                  </div>
                </div>
                <div className="space-y-2">
                  {features.slice(0, 3).map((feature, i) => (
                    <div key={i} className="h-2 bg-green-400/20 rounded shimmer-advanced"></div>
                  ))}
                </div>
                <div className="mt-4 space-y-1">
                  <div className="h-8 bg-green-400/30 rounded"></div>
                  <div className="h-8 bg-green-400/20 rounded"></div>
                </div>
              </div>
            </div>
          </div>
        );
      case 'desktop':
        return (
          <div className="relative w-64 h-40 mx-auto">
            <div className="bg-gray-900 rounded-lg border-2 border-gray-700 overflow-hidden">
              <div className="bg-gray-800 px-3 py-2 flex items-center space-x-2">
                <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              </div>
              <div className="p-4 h-32">
                <div className="flex items-center space-x-2 mb-3">
                  <div className="w-4 h-4 bg-green-400 rounded flex items-center justify-center text-xs">
                    {icon}
                  </div>
                  <div className="h-2 bg-green-400/30 rounded flex-1"></div>
                </div>
                <div className="grid grid-cols-2 gap-2">
                  {features.slice(0, 4).map((_, i) => (
                    <div key={i} className="h-4 bg-green-400/20 rounded shimmer-advanced"></div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        );
      case 'web':
        return (
          <div className="relative w-72 h-48 mx-auto">
            <div className="bg-black rounded-lg border border-green-400/30 overflow-hidden">
              <div className="bg-gray-900/50 px-4 py-2 border-b border-green-400/20">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                  <div className="h-1 bg-green-400/40 rounded flex-1 max-w-32"></div>
                </div>
              </div>
              <div className="p-4">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-6 h-6 bg-green-400 rounded-lg flex items-center justify-center">
                    {icon}
                  </div>
                  <div className="space-y-1">
                    <div className="h-2 bg-green-400/40 rounded w-24"></div>
                    <div className="h-1 bg-green-400/20 rounded w-16"></div>
                  </div>
                </div>
                <div className="grid grid-cols-3 gap-2 mb-3">
                  {features.slice(0, 6).map((_, i) => (
                    <div key={i} className="h-6 bg-green-400/10 rounded shimmer-advanced"></div>
                  ))}
                </div>
                <div className="h-4 bg-green-400/30 rounded"></div>
              </div>
            </div>
          </div>
        );
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay }}
      className="app-card hover-lift-advanced p-6 rounded-2xl"
    >
      {getDeviceFrame()}
      <div className="mt-6 text-center">
        <h3 className="text-xl font-bold text-white mb-2">{title}</h3>
        <p className="text-gray-300 text-sm mb-4">{description}</p>
        <div className="flex flex-wrap justify-center gap-2">
          {features.map((feature, i) => (
            <span
              key={i}
              className="px-2 py-1 bg-green-400/10 text-green-400 text-xs rounded-full border border-green-400/20"
            >
              {feature}
            </span>
          ))}
        </div>
      </div>
    </motion.div>
  );
};

export default function AppCards() {
  const apps = [
    {
      title: "Mobile Savings App",
      description: "Track and manage your savings on the go with our intuitive mobile application.",
      icon: <FiSmartphone className="w-4 h-4 text-white" />,
      type: 'mobile' as const,
      features: ["Real-time tracking", "Push notifications", "Biometric security", "Offline mode"]
    },
    {
      title: "Desktop Dashboard",
      description: "Comprehensive financial overview with advanced analytics and reporting tools.",
      icon: <FiBarChart className="w-4 h-4 text-white" />,
      type: 'desktop' as const,
      features: ["Advanced charts", "Export reports", "Multi-account view", "Custom widgets"]
    },
    {
      title: "Web Platform",
      description: "Access your financial data anywhere with our responsive web platform.",
      icon: <FiMonitor className="w-4 h-4 text-white" />,
      type: 'web' as const,
      features: ["Cross-platform", "Real-time sync", "Secure access", "Cloud backup", "API access", "Team sharing"]
    },
    {
      title: "Investment Tracker",
      description: "Monitor your investment portfolio with real-time market data and insights.",
      icon: <FiTrendingUp className="w-4 h-4 text-white" />,
      type: 'mobile' as const,
      features: ["Portfolio tracking", "Market alerts", "Performance metrics", "Risk analysis"]
    },
    {
      title: "Budget Planner",
      description: "Plan and manage your budget with intelligent spending categorization.",
      icon: <FiPieChart className="w-4 h-4 text-white" />,
      type: 'desktop' as const,
      features: ["Smart categories", "Spending limits", "Goal tracking", "Expense insights"]
    },
    {
      title: "Savings Goals",
      description: "Set and achieve your financial goals with personalized saving strategies.",
      icon: <FiTarget className="w-4 h-4 text-white" />,
      type: 'web' as const,
      features: ["Goal setting", "Progress tracking", "Milestone rewards", "Auto-save", "Visual progress", "Sharing"]
    }
  ];

  return (
    <section className="py-20 px-6 relative z-10">
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
            Experience Koja Save
            <span className="gradient-text block">Across All Platforms</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Our comprehensive suite of applications ensures you can manage your finances 
            seamlessly across all your devices with a consistent, intuitive experience.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {apps.map((app, index) => (
            <AppCard
              key={index}
              {...app}
              delay={index * 0.1}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
