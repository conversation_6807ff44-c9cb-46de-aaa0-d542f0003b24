{"name": "betterinterest-app", "version": 2, "framework": "nextjs", "buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm install", "devCommand": "npm run dev", "env": {"NEXT_PUBLIC_APP_NAME": "BetterInterest", "NEXT_PUBLIC_DEMO_MODE": "true", "NEXT_PUBLIC_ENABLE_WEBSOCKET": "true", "NEXT_PUBLIC_NOTIFICATION_SOUND": "true"}, "build": {"env": {"NODE_ENV": "production"}}, "functions": {"app/**/*.tsx": {"runtime": "nodejs18.x"}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}], "redirects": [{"source": "/login", "destination": "/auth/login", "permanent": true}, {"source": "/signup", "destination": "/auth/signup", "permanent": true}, {"source": "/register", "destination": "/auth/signup", "permanent": true}], "rewrites": [{"source": "/api/:path*", "destination": "https://api.betterinterest.com/api/:path*"}]}