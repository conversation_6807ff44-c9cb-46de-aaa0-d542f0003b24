"use client";

import { useEffect } from "react";
import { motion } from "framer-motion";
import AOS from "aos";
import "aos/dist/aos.css";
import Link from "next/link";
import {
  FiSave,
  FiArrowLeft,
  FiCheck,
  FiX,
  FiStar,
  FiTrendingUp,
  FiShield,
  FiHeadphones,
  FiPackage,
} from "react-icons/fi";
import PageLayout from "../../components/PageLayout";
import EnhancedHero from "../../components/EnhancedHero";

export default function PackagesPage() {
  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
      easing: "ease-out-cubic",
    });
  }, []);

  const packages = [
    {
      name: "Basic",
      price: "Free",
      period: "Forever",
      description: "Perfect for getting started with smart savings",
      features: [
        { name: "Basic savings tracking", included: true },
        { name: "Mobile app access", included: true },
        { name: "Email support", included: true },
        { name: "Up to 2 savings goals", included: true },
        { name: "Monthly reports", included: true },
        { name: "Advanced analytics", included: false },
        { name: "Investment portfolios", included: false },
        { name: "Priority support", included: false },
        { name: "Personal advisor", included: false },
      ],

      popular: false,
      color: "gray",
    },
    {
      name: "Pro",
      price: "₦2,500",
      period: "per month",
      description: "Ideal for serious savers who want to grow their wealth",
      features: [
        { name: "Everything in Basic", included: true },
        { name: "Advanced analytics", included: true },
        { name: "Investment portfolios", included: true },
        { name: "Priority support", included: true },
        { name: "Unlimited savings goals", included: true },
        { name: "Weekly reports", included: true },
        { name: "Round-up savings", included: true },
        { name: "Goal tracking", included: true },
        { name: "Personal advisor", included: false },
      ],

      popular: true,
      color: "green",
    },
    {
      name: "Premium",
      price: "₦5,000",
      period: "per month",
      description:
        "For those who want the complete financial management experience",
      features: [
        { name: "Everything in Pro", included: true },
        { name: "Personal financial advisor", included: true },
        { name: "Custom investment strategies", included: true },
        { name: "Tax optimization", included: true },
        { name: "Daily reports", included: true },
        { name: "Premium support (24/7)", included: true },
        { name: "Family account management", included: true },
        { name: "Advanced risk management", included: true },
        { name: "Exclusive investment opportunities", included: true },
      ],

      popular: false,
      color: "purple",
    },
  ];

  const features = [
    {
      icon: <FiTrendingUp className="w-8 h-8" data-oid="ymqquky" />,
      title: "Higher Returns",
      description:
        "Our Pro and Premium packages offer access to investment portfolios with higher potential returns.",
    },
    {
      icon: <FiShield className="w-8 h-8" data-oid="gk47a_8" />,
      title: "Enhanced Security",
      description:
        "Premium security features and insurance coverage for higher-tier packages.",
    },
    {
      icon: <FiHeadphones className="w-8 h-8" data-oid="j2sikv_" />,
      title: "Priority Support",
      description:
        "Get faster response times and dedicated support channels with paid packages.",
    },
  ];

  const faqs = [
    {
      question: "Can I change my package anytime?",
      answer:
        "Yes, you can upgrade or downgrade your package at any time. Changes take effect immediately.",
    },
    {
      question: "Is there a long-term commitment?",
      answer:
        "No, all our packages are month-to-month with no long-term contracts or cancellation fees.",
    },
    {
      question: "What payment methods do you accept?",
      answer:
        "We accept bank transfers, debit cards, and mobile money payments including Paystack integration.",
    },
    {
      question: "Do you offer refunds?",
      answer:
        "Yes, we offer a 30-day money-back guarantee for all paid packages if you're not satisfied.",
    },
  ];

  return (
    <PageLayout>
      {/* Enhanced Hero Section */}
      <EnhancedHero
        icon={<FiPackage className="w-12 h-12 text-white" />}
        title="Choose Your"
        highlightText="Savings Package"
        description="Start free and upgrade as your savings grow. Every package is designed to maximize your financial potential with features that scale with your success."
        primaryButton={{
          text: "Start Free Trial",
          href: "/signup"
        }}
        secondaryButton={{
          text: "Compare Features",
          href: "#packages"
        }}
        features={[
          { icon: <FiSave />, label: "Smart Savings" },
          { icon: <FiTrendingUp />, label: "Growth Tracking" },
          { icon: <FiShield />, label: "Secure Platform" },
          { icon: <FiHeadphones />, label: "24/7 Support" }
        ]}
        stats={[
          { value: "3", label: "Packages" },
          { value: "Free", label: "Trial" },
          { value: "30-Day", label: "Guarantee" },
          { value: "24/7", label: "Support" }
        ]}
      />

      {/* Pricing Cards */}
      <section className="relative z-10 px-6 py-20" data-oid="sk0d9vs">
        <div className="max-w-7xl mx-auto" data-oid="uhz8w1b">
          <div
            className="grid grid-cols-1 lg:grid-cols-3 gap-8"
            data-oid="czgakx."
          >
            {packages.map((pkg, index) => (
              <div
                key={index}
                data-aos="fade-up"
                data-aos-delay={index * 100}
                className={`app-card hover-lift-advanced p-8 relative ${
                  pkg.popular ? "app-card-premium scale-105" : ""
                }`}
                data-oid="h8gxay8"
              >
                {pkg.popular && (
                  <div
                    className="absolute -top-4 left-1/2 transform -translate-x-1/2"
                    data-oid="-ejkq8l"
                  >
                    <span
                      className="bg-gradient-to-r from-green-400 to-green-600 px-6 py-2 rounded-full text-sm font-semibold text-white flex items-center gap-2"
                      data-oid="fx7541z"
                    >
                      <FiStar className="w-4 h-4" data-oid="mj151rk" />
                      Most Popular
                    </span>
                  </div>
                )}

                <div className="text-center mb-8" data-oid="2.0.pfa">
                  <h3
                    className="text-2xl font-bold mb-2 text-white"
                    data-oid="f0:7tz5"
                  >
                    {pkg.name}
                  </h3>
                  <div className="mb-4" data-oid="v9da4o6">
                    <span
                      className="text-4xl font-bold text-green-400"
                      data-oid="lfv2eee"
                    >
                      {pkg.price}
                    </span>
                    {pkg.price !== "Free" && (
                      <span className="text-gray-400 ml-2" data-oid="nbz.lwr">
                        /{pkg.period.split(" ")[1]}
                      </span>
                    )}
                  </div>
                  <p className="text-gray-300" data-oid="idnfbic">
                    {pkg.description}
                  </p>
                </div>

                <ul className="space-y-4 mb-8" data-oid="ita0t5y">
                  {pkg.features.map((feature, featureIndex) => (
                    <li
                      key={featureIndex}
                      className="flex items-center gap-3"
                      data-oid=".vdnkpd"
                    >
                      {feature.included ? (
                        <FiCheck
                          className="w-5 h-5 text-green-400 flex-shrink-0"
                          data-oid="htx-9e-"
                        />
                      ) : (
                        <FiX
                          className="w-5 h-5 text-gray-500 flex-shrink-0"
                          data-oid="4aizr1m"
                        />
                      )}
                      <span
                        className={`${feature.included ? "text-white" : "text-gray-500"}`}
                        data-oid="_:gkrs:"
                      >
                        {feature.name}
                      </span>
                    </li>
                  ))}
                </ul>

                <button
                  className={`w-full py-4 rounded-lg font-semibold transition-all hover-lift magnetic ${
                    pkg.popular
                      ? "btn-advanced text-white"
                      : "border border-green-400 text-white hover:bg-green-400 hover:text-black"
                  }`}
                  data-oid="8hrwn2p"
                >
                  {pkg.price === "Free" ? "Get Started Free" : "Choose Plan"}
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Package Benefits */}
      <section className="relative z-10 px-6 py-20" data-oid="d80_hlz">
        <div className="max-w-7xl mx-auto" data-oid=".uhi74:">
          <div
            className="text-center mb-16"
            data-aos="fade-up"
            data-oid="4dblhf_"
          >
            <h2
              className="text-4xl md:text-5xl font-bold mb-6 text-white"
              data-oid=":mg6v4g"
            >
              Why Upgrade Your{" "}
              <span className="text-green-400" data-oid="anhd-.k">
                Package?
              </span>
            </h2>
            <p
              className="text-xl text-gray-300 max-w-3xl mx-auto"
              data-oid="zc0v4j9"
            >
              Unlock additional features and benefits that help you save more
              and grow your wealth faster.
            </p>
          </div>

          <div
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
            data-oid="ka72obr"
          >
            {features.map((feature, index) => (
              <div
                key={index}
                data-aos="fade-up"
                data-aos-delay={index * 100}
                className="app-card hover-lift-advanced p-8 text-center card-stack"
                data-oid="sy5u0nj"
              >
                <div
                  className="text-green-400 mb-6 flex justify-center group-hover:animate-pulse-green transition-all"
                  data-oid=":9u.zul"
                >
                  {feature.icon}
                </div>
                <h3
                  className="text-xl font-semibold mb-4 text-white"
                  data-oid="kr7wsrn"
                >
                  {feature.title}
                </h3>
                <p className="text-gray-300" data-oid="0egp5q3">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="relative z-10 px-6 py-20" data-oid="65sn1xc">
        <div className="max-w-4xl mx-auto" data-oid="gz45tb1">
          <div
            className="text-center mb-16"
            data-aos="fade-up"
            data-oid="psh5lm6"
          >
            <h2
              className="text-4xl md:text-5xl font-bold mb-6 text-white"
              data-oid="4y:8h:6"
            >
              Frequently Asked{" "}
              <span className="text-green-400" data-oid="4hxtwjs">
                Questions
              </span>
            </h2>
          </div>

          <div className="space-y-6" data-oid="wg5yv.w">
            {faqs.map((faq, index) => (
              <div
                key={index}
                data-aos="fade-up"
                data-aos-delay={index * 100}
                className="app-card hover-lift-advanced p-6"
                data-oid="n_l_3zy"
              >
                <h3
                  className="text-xl font-semibold mb-3 text-white"
                  data-oid="i87x6a."
                >
                  {faq.question}
                </h3>
                <p className="text-gray-300" data-oid="cn6s0d4">
                  {faq.answer}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative z-10 px-6 py-20" data-oid="u:l:sln">
        <div
          className="max-w-4xl mx-auto text-center"
          data-aos="fade-up"
          data-oid="ffn4c2y"
        >
          <div
            className="glass-advanced rounded-2xl p-12 app-card-premium"
            data-oid="y:ch._9"
          >
            <div className="w-16 h-16 bg-gradient-to-r from-green-400 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <FiPackage className="w-8 h-8 text-white" />
            </div>
            <h2
              className="text-4xl md:text-5xl font-bold mb-6 text-white text-glow"
              data-oid="xog7890"
            >
              Ready to Start{" "}
              <span className="gradient-text" data-oid="3cvryf0">
                Saving?
              </span>
            </h2>
            <p className="text-xl text-gray-300 mb-8" data-oid="qn9hv3_">
              Join thousands of users who are already building their financial
              future with Koja Save. Start free and upgrade anytime.
            </p>
            <div
              className="flex flex-col sm:flex-row gap-4 justify-center mb-8"
              data-oid=":r-2m6a"
            >
              <Link
                href="/signup"
                className="btn-advanced px-8 py-4 rounded-lg text-lg font-semibold text-white hover-lift"
                data-oid="9y-a0mn"
              >
                Start Free Trial
              </Link>
              <Link
                href="/contact"
                className="px-8 py-4 border border-green-400 rounded-lg text-lg font-semibold text-white hover:bg-green-400 hover:text-black transition-all hover-lift magnetic"
                data-oid="fof6w:z"
              >
                Contact Sales
              </Link>
            </div>

            {/* Trust indicators */}
            <div className="grid grid-cols-3 gap-6 max-w-md mx-auto">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">10K+</div>
                <div className="text-sm text-gray-400">Users</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">₦50M+</div>
                <div className="text-sm text-gray-400">Saved</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">4.9★</div>
                <div className="text-sm text-gray-400">Rating</div>
              </div>
            </div>
          </div>
        </div>
      </section>

    </PageLayout>
  );
}
