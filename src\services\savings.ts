import { 
  SavingsPlan, 
  SavingsGoal, 
  SavingsTransaction,
  CreateSavingsPlanData,
  CreateSavingsGoalData,
  SavingsStats,
  InterestCalculation
} from '../types/savings';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;

class SavingsService {
  private getAuthHeaders() {
    const token = localStorage.getItem('auth_token');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` })
    };
  }

  // Savings Plans
  async getSavingsPlans(): Promise<SavingsPlan[]> {
    const response = await fetch(`${API_BASE_URL}/savings`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error('Failed to fetch savings plans');
    }

    return response.json();
  }

  async getUserSavingsPlans(): Promise<SavingsPlan[]> {
    // Use /api/savings/my (returns { plans, summary })
    const response = await fetch(`${API_BASE_URL}/api/savings/my`, {
      headers: this.getAuthHeaders()
    });
    if (!response.ok) {
      throw new Error('Failed to fetch user savings plans');
    }
    const data = await response.json();
    return data.plans || [];
  }

  async getSavingsPlan(id: string): Promise<SavingsPlan> {
    const response = await fetch(`${API_BASE_URL}/savings/${id}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error('Failed to fetch savings plan');
    }

    return response.json();
  }

  async joinSavingsPlan(planId: string): Promise<SavingsPlan> {
    const response = await fetch(`${API_BASE_URL}/savings/join`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ planId })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to join savings plan');
    }

    return response.json();
  }

  async createSavingsPlan(data: CreateSavingsPlanData): Promise<SavingsPlan> {
    const response = await fetch(`${API_BASE_URL}/savings/create`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to create savings plan');
    }

    return response.json();
  }

  async updateSavingsPlan(id: string, data: Partial<CreateSavingsPlanData>): Promise<SavingsPlan> {
    const response = await fetch(`${API_BASE_URL}/savings/${id}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update savings plan');
    }

    return response.json();
  }

  async pauseSavingsPlan(id: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/savings/plans/${id}/pause`, {
      method: 'POST',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error('Failed to pause savings plan');
    }
  }

  async resumeSavingsPlan(id: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/savings/plans/${id}/resume`, {
      method: 'POST',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error('Failed to resume savings plan');
    }
  }

  async deleteSavingsPlan(id: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/savings/${id}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error('Failed to delete savings plan');
    }
  }

  // Savings Goals
  async getSavingsGoals(): Promise<SavingsGoal[]> {
    const response = await fetch(`${API_BASE_URL}/savings/goals`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error('Failed to fetch savings goals');
    }

    return response.json();
  }

  async getSavingsGoal(id: string): Promise<SavingsGoal> {
    const response = await fetch(`${API_BASE_URL}/savings/goals/${id}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error('Failed to fetch savings goal');
    }

    return response.json();
  }

  async createSavingsGoal(data: CreateSavingsGoalData): Promise<SavingsGoal> {
    const response = await fetch(`${API_BASE_URL}/savings/goals`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to create savings goal');
    }

    return response.json();
  }

  async updateSavingsGoal(id: string, data: Partial<CreateSavingsGoalData>): Promise<SavingsGoal> {
    const response = await fetch(`${API_BASE_URL}/savings/goals/${id}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update savings goal');
    }

    return response.json();
  }

  async deleteSavingsGoal(id: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/savings/goals/${id}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error('Failed to delete savings goal');
    }
  }

  // Transactions
  async getSavingsTransactions(planId?: string, goalId?: string): Promise<SavingsTransaction[]> {
    const params = new URLSearchParams();
    if (planId) params.append('planId', planId);
    if (goalId) params.append('goalId', goalId);

    const response = await fetch(`${API_BASE_URL}/savings/transactions?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error('Failed to fetch transactions');
    }

    return response.json();
  }

  async makeContribution(planId: string, amount: number): Promise<SavingsTransaction> {
    const response = await fetch(`${API_BASE_URL}/savings/plans/${planId}/contribute`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ amount })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to make contribution');
    }

    return response.json();
  }

  async makeGoalContribution(goalId: string, amount: number): Promise<SavingsTransaction> {
    const response = await fetch(`${API_BASE_URL}/savings/goals/${goalId}/contribute`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ amount })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to make goal contribution');
    }

    return response.json();
  }

  // Statistics
  async getSavingsStats(): Promise<SavingsStats> {
    const response = await fetch(`${API_BASE_URL}/savings/stats`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error('Failed to fetch savings statistics');
    }

    return response.json();
  }

  // Interest Calculation
  async calculateInterest(
    principal: number,
    rate: number,
    time: number,
    frequency: number
  ): Promise<InterestCalculation> {
    const response = await fetch(`${API_BASE_URL}/savings/calculate-interest`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ principal, rate, time, frequency })
    });

    if (!response.ok) {
      throw new Error('Failed to calculate interest');
    }

    return response.json();
  }

  // Utility functions
  calculateCompoundInterest(
    principal: number,
    rate: number,
    time: number,
    frequency: number = 12
  ): number {
    const r = rate / 100;
    const n = frequency;
    const t = time / 12; // Convert months to years
    
    return principal * Math.pow((1 + r / n), n * t);
  }

  calculateMonthlyContribution(
    targetAmount: number,
    currentAmount: number,
    months: number,
    interestRate: number = 0
  ): number {
    const remaining = targetAmount - currentAmount;
    
    if (interestRate === 0) {
      return remaining / months;
    }

    const r = interestRate / 100 / 12;
    const n = months;
    
    return (remaining * r) / (Math.pow(1 + r, n) - 1);
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN'
    }).format(amount);
  }

  calculateProgress(current: number, target: number): number {
    return Math.min((current / target) * 100, 100);
  }
}

export const savingsService = new SavingsService();
