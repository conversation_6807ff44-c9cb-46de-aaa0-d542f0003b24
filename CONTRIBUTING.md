# Contributing to Latest Green Better Interest App

Thank you for your interest in contributing to our fintech savings platform! This document provides guidelines and information for contributors.

## 🤝 **How to Contribute**

### **Getting Started**

1. **Fork the repository**
   ```bash
   git clone https://github.com/koja-pay/latestgreenbetterinterestapp.git
   cd latestgreenbetterinterestapp
   ```

2. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **Install dependencies**
   ```bash
   npm install
   ```

4. **Set up environment**
   ```bash
   cp .env.example .env.local
   ```

### **Development Workflow**

1. **Make your changes**
   - Follow the coding standards outlined below
   - Write tests for new functionality
   - Update documentation as needed

2. **Test your changes**
   ```bash
   npm run test
   npm run lint
   npm run type-check
   ```

3. **Commit your changes**
   ```bash
   git add .
   git commit -m "feat: add new feature description"
   ```

4. **Push to your fork**
   ```bash
   git push origin feature/your-feature-name
   ```

5. **Create a Pull Request**
   - Use the PR template
   - Provide clear description of changes
   - Link any related issues

## 📝 **Coding Standards**

### **TypeScript**
- Use TypeScript for all new code
- Define proper interfaces and types
- Avoid `any` type unless absolutely necessary
- Use strict mode settings

### **React Components**
- Use functional components with hooks
- Follow the component structure:
  ```tsx
  // Imports
  import React from 'react';
  
  // Types
  interface ComponentProps {
    // props definition
  }
  
  // Component
  export function Component({ prop }: ComponentProps) {
    // hooks
    // handlers
    // render
  }
  ```

### **Styling**
- Use Tailwind CSS classes
- Follow mobile-first responsive design
- Use consistent spacing and colors from design system
- Implement dark mode support

### **File Naming**
- Use PascalCase for components: `UserProfile.tsx`
- Use camelCase for utilities: `formatCurrency.ts`
- Use kebab-case for pages: `user-profile.tsx`

### **Code Organization**
```
src/
├── components/
│   ├── ui/           # Reusable UI components
│   ├── dashboard/    # Dashboard-specific components
│   └── admin/        # Admin-specific components
├── hooks/            # Custom React hooks
├── services/         # API services
├── types/            # TypeScript definitions
└── utils/            # Utility functions
```

## 🧪 **Testing Guidelines**

### **Unit Tests**
- Write tests for all utility functions
- Test component behavior, not implementation
- Use React Testing Library for component tests
- Aim for 80%+ code coverage

### **Integration Tests**
- Test user workflows
- Test API integrations
- Test real-time features

### **E2E Tests**
- Test critical user journeys
- Test payment flows
- Test admin workflows

## 🎨 **Design Guidelines**

### **UI/UX Principles**
- Follow the established design system
- Maintain consistency across components
- Ensure accessibility (WCAG 2.1 AA)
- Optimize for mobile-first experience

### **Color Palette**
- Primary: Green (#10B981)
- Background: Black (#000000)
- Surface: Gray-800 (#1F2937)
- Text: White (#FFFFFF)

### **Typography**
- Use Inter font family
- Maintain proper heading hierarchy
- Ensure readable line heights

## 🔒 **Security Guidelines**

### **Authentication**
- Never expose sensitive tokens in client code
- Use proper JWT handling
- Implement proper session management

### **Data Handling**
- Validate all user inputs
- Sanitize data before processing
- Use proper error handling without exposing internals

### **API Security**
- Use HTTPS for all API calls
- Implement proper CORS policies
- Rate limit API endpoints

## 📋 **Pull Request Guidelines**

### **PR Title Format**
Use conventional commits format:
- `feat:` - New feature
- `fix:` - Bug fix
- `docs:` - Documentation changes
- `style:` - Code style changes
- `refactor:` - Code refactoring
- `test:` - Adding tests
- `chore:` - Maintenance tasks

### **PR Description Template**
```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed

## Screenshots (if applicable)
Add screenshots for UI changes

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Tests added/updated
- [ ] Documentation updated
```

## 🐛 **Bug Reports**

### **Before Reporting**
- Check existing issues
- Reproduce the bug
- Gather relevant information

### **Bug Report Template**
```markdown
## Bug Description
Clear description of the bug

## Steps to Reproduce
1. Go to '...'
2. Click on '...'
3. See error

## Expected Behavior
What should happen

## Actual Behavior
What actually happens

## Environment
- OS: [e.g., macOS, Windows, Linux]
- Browser: [e.g., Chrome, Firefox, Safari]
- Version: [e.g., 1.0.0]

## Additional Context
Any other relevant information
```

## 💡 **Feature Requests**

### **Feature Request Template**
```markdown
## Feature Description
Clear description of the proposed feature

## Problem Statement
What problem does this solve?

## Proposed Solution
How should this feature work?

## Alternatives Considered
Other solutions you've considered

## Additional Context
Any other relevant information
```

## 🏷️ **Issue Labels**

- `bug` - Something isn't working
- `enhancement` - New feature or request
- `documentation` - Improvements to documentation
- `good first issue` - Good for newcomers
- `help wanted` - Extra attention needed
- `priority: high` - High priority issue
- `priority: low` - Low priority issue

## 📚 **Resources**

### **Documentation**
- [Next.js Documentation](https://nextjs.org/docs)
- [React Documentation](https://react.dev)
- [TypeScript Documentation](https://www.typescriptlang.org/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)

### **Tools**
- [VS Code](https://code.visualstudio.com/) - Recommended editor
- [React Developer Tools](https://react.dev/learn/react-developer-tools)
- [TypeScript ESLint](https://typescript-eslint.io/)

## 🎯 **Development Priorities**

### **High Priority**
- Security improvements
- Performance optimizations
- Bug fixes
- Accessibility improvements

### **Medium Priority**
- New features
- UI/UX enhancements
- Code refactoring
- Test coverage improvements

### **Low Priority**
- Documentation updates
- Code style improvements
- Minor feature enhancements

## 📞 **Getting Help**

- **GitHub Discussions** - For questions and general discussion
- **GitHub Issues** - For bug reports and feature requests
- **Code Review** - All PRs require review before merging

## 🙏 **Recognition**

Contributors will be recognized in:
- README.md contributors section
- Release notes for significant contributions
- GitHub contributor graphs

Thank you for contributing to Latest Green Better Interest App! 🚀
