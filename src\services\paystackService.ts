import { 
  PaystackRecipient, 
  PaystackTransfer, 
  BankAccountDetails,
  WithdrawalError 
} from '../types/automaticWithdrawal';

interface PaystackConfig {
  publicKey: string;
  secretKey: string;
  baseUrl: string;
}

interface CreateRecipientRequest {
  type: 'nuban' | 'mobile_money' | 'basa';
  name: string;
  account_number: string;
  bank_code: string;
  currency?: string;
  description?: string;
  metadata?: Record<string, any>;
}

interface InitiateTransferRequest {
  source: 'balance';
  amount: number; // In kobo for NGN
  recipient: string; // Recipient code
  reason: string;
  currency?: string;
  reference?: string;
}

interface BankListResponse {
  status: boolean;
  message: string;
  data: Array<{
    id: number;
    name: string;
    slug: string;
    code: string;
    longcode: string;
    gateway: string;
    pay_with_bank: boolean;
    active: boolean;
    country: string;
    currency: string;
    type: string;
  }>;
}

interface AccountVerificationResponse {
  status: boolean;
  message: string;
  data: {
    account_number: string;
    account_name: string;
    bank_id: number;
  };
}

class PaystackService {
  private config: PaystackConfig;

  constructor() {
    this.config = {
      publicKey: process.env.NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY || '',
      secretKey: process.env.PAYSTACK_SECRET_KEY || '',
      baseUrl: 'https://api.paystack.co'
    };
  }

  private getHeaders() {
    return {
      'Authorization': `Bearer ${this.config.secretKey}`,
      'Content-Type': 'application/json'
    };
  }

  private async makeRequest<T>(
    endpoint: string, 
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    data?: any
  ): Promise<T> {
    const url = `${this.config.baseUrl}${endpoint}`;
    
    const options: RequestInit = {
      method,
      headers: this.getHeaders(),
    };

    if (data && (method === 'POST' || method === 'PUT')) {
      options.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(url, options);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return result;
    } catch (error) {
      console.error(`Paystack API Error (${method} ${endpoint}):`, error);
      throw error;
    }
  }

  // Bank Operations
  async getBankList(): Promise<BankListResponse> {
    return this.makeRequest<BankListResponse>('/bank');
  }

  async verifyAccountNumber(
    accountNumber: string,
    bankCode: string
  ): Promise<AccountVerificationResponse> {
    // Call backend API route instead of Paystack directly
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/paystack/resolve-account`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ account_number: accountNumber, bank_code: bankCode })
    });
    const result = await response.json();
    return result;
  }

  // Recipient Management
  async createTransferRecipient(
    recipientData: CreateRecipientRequest
  ): Promise<{ status: boolean; message: string; data: PaystackRecipient }> {
    return this.makeRequest<{ status: boolean; message: string; data: PaystackRecipient }>(
      '/transferrecipient',
      'POST',
      recipientData
    );
  }

  async getTransferRecipient(
    recipientCode: string
  ): Promise<{ status: boolean; message: string; data: PaystackRecipient }> {
    return this.makeRequest<{ status: boolean; message: string; data: PaystackRecipient }>(
      `/transferrecipient/${recipientCode}`
    );
  }

  async updateTransferRecipient(
    recipientCode: string,
    updateData: Partial<CreateRecipientRequest>
  ): Promise<{ status: boolean; message: string; data: PaystackRecipient }> {
    return this.makeRequest<{ status: boolean; message: string; data: PaystackRecipient }>(
      `/transferrecipient/${recipientCode}`,
      'PUT',
      updateData
    );
  }

  async deleteTransferRecipient(
    recipientCode: string
  ): Promise<{ status: boolean; message: string }> {
    return this.makeRequest<{ status: boolean; message: string }>(
      `/transferrecipient/${recipientCode}`,
      'DELETE'
    );
  }

  // Transfer Operations
  async initiateTransfer(
    transferData: InitiateTransferRequest
  ): Promise<{ status: boolean; message: string; data: PaystackTransfer }> {
    return this.makeRequest<{ status: boolean; message: string; data: PaystackTransfer }>(
      '/transfer',
      'POST',
      transferData
    );
  }

  async getTransfer(
    transferCode: string
  ): Promise<{ status: boolean; message: string; data: PaystackTransfer }> {
    return this.makeRequest<{ status: boolean; message: string; data: PaystackTransfer }>(
      `/transfer/${transferCode}`
    );
  }

  async verifyTransfer(
    reference: string
  ): Promise<{ status: boolean; message: string; data: PaystackTransfer }> {
    return this.makeRequest<{ status: boolean; message: string; data: PaystackTransfer }>(
      `/transfer/verify/${reference}`
    );
  }

  // Balance Operations
  async getBalance(): Promise<{ 
    status: boolean; 
    message: string; 
    data: Array<{ currency: string; balance: number }> 
  }> {
    return this.makeRequest<{ 
      status: boolean; 
      message: string; 
      data: Array<{ currency: string; balance: number }> 
    }>('/balance');
  }

  // Utility Methods
  convertToKobo(amount: number): number {
    return Math.round(amount * 100);
  }

  convertFromKobo(amount: number): number {
    return amount / 100;
  }

  generateReference(prefix: string = 'AUTO'): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `${prefix}-${timestamp}-${random}`;
  }

  // Validation Methods
  validateBankAccount(bankDetails: BankAccountDetails): { isValid: boolean; errors: WithdrawalError[] } {
    const errors: WithdrawalError[] = [];

    if (!bankDetails.accountNumber || !/^\d{10}$/.test(bankDetails.accountNumber)) {
      errors.push({
        code: 'INVALID_ACCOUNT_NUMBER',
        message: 'Account number must be exactly 10 digits'
      });
    }

    if (!bankDetails.bankCode || bankDetails.bankCode.length < 3) {
      errors.push({
        code: 'INVALID_BANK_CODE',
        message: 'Bank code is required'
      });
    }

    if (!bankDetails.accountName || bankDetails.accountName.trim().length < 2) {
      errors.push({
        code: 'INVALID_ACCOUNT_NAME',
        message: 'Account name must be at least 2 characters'
      });
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  validateTransferAmount(amount: number, currency: string = 'NGN'): { isValid: boolean; errors: WithdrawalError[] } {
    const errors: WithdrawalError[] = [];
    const minAmount = currency === 'NGN' ? 100 : 1; // ₦100 or $1 minimum
    const maxAmount = currency === 'NGN' ? ******** : 100000; // ₦10M or $100K maximum

    if (amount < minAmount) {
      errors.push({
        code: 'AMOUNT_TOO_LOW',
        message: `Minimum transfer amount is ${currency === 'NGN' ? '₦' : '$'}${minAmount}`
      });
    }

    if (amount > maxAmount) {
      errors.push({
        code: 'AMOUNT_TOO_HIGH',
        message: `Maximum transfer amount is ${currency === 'NGN' ? '₦' : '$'}${maxAmount.toLocaleString()}`
      });
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Error Handling
  handlePaystackError(error: any): WithdrawalError {
    if (error.response?.data?.message) {
      return {
        code: 'PAYSTACK_API_ERROR',
        message: error.response.data.message,
        details: error.response.data
      };
    }

    if (error.message) {
      return {
        code: 'PAYSTACK_ERROR',
        message: error.message
      };
    }

    return {
      code: 'UNKNOWN_ERROR',
      message: 'An unknown error occurred while processing the request'
    };
  }

  // Webhook Verification
  verifyWebhookSignature(payload: string, signature: string): boolean {
    const crypto = require('crypto');
    const hash = crypto
      .createHmac('sha512', this.config.secretKey)
      .update(payload, 'utf8')
      .digest('hex');
    
    return hash === signature;
  }
}

// Export singleton instance
export const paystackService = new PaystackService();
export default paystackService;
