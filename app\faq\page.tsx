"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import {
  FiArrowLeft,
  FiChevronDown,
  FiChevronUp,
  FiSearch,
  FiHelpCircle,
  FiDollarSign,
  FiShield,
  FiUsers,
  FiCreditCard,
  FiSettings
} from 'react-icons/fi';
import { BackgroundAnimation } from '../../src/components/ui/BackgroundAnimation';
import { BetterInterestLogo } from '../../src/components/ui/BetterInterestLogo';
import { Card3D } from '../../src/components/ui/Card3D';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
}

const faqData: FAQItem[] = [
  {
    id: '1',
    question: 'How does BetterInterest work?',
    answer: 'BetterInterest is a digital savings platform that helps you earn higher interest rates on your savings. You can create individual or group savings plans, set targets, and earn competitive interest rates while your money grows safely.',
    category: 'General'
  },
  {
    id: '2',
    question: 'What interest rates do you offer?',
    answer: 'Our interest rates range from 8.5% to 15% annually, depending on your savings plan type, duration, and amount. Premium and Elite plans offer higher rates for larger deposits and longer terms.',
    category: 'Interest & Returns'
  },
  {
    id: '3',
    question: 'Is my money safe with BetterInterest?',
    answer: 'Yes, your funds are secured through bank-grade security measures, encryption, and regulatory compliance. We partner with licensed financial institutions to ensure your money is protected.',
    category: 'Security'
  },
  {
    id: '4',
    question: 'Can I withdraw my money anytime?',
    answer: 'You can withdraw your money, but early withdrawal may incur penalties depending on your savings plan terms. Emergency withdrawals are available with reduced penalties for urgent situations.',
    category: 'Withdrawals'
  },
  {
    id: '5',
    question: 'How do group savings work?',
    answer: 'Group savings allow you to save with friends, family, or colleagues towards common goals. Members contribute regularly, and the group earns higher interest rates. You can create or join existing groups.',
    category: 'Group Savings'
  },
  {
    id: '6',
    question: 'What payment methods do you accept?',
    answer: 'We accept bank transfers, debit cards, and mobile money payments. You can link multiple payment methods to your account for convenient deposits.',
    category: 'Payments'
  },
  {
    id: '7',
    question: 'How do I verify my account (KYC)?',
    answer: 'Account verification requires uploading a valid government-issued ID, proof of address, and a selfie. The process typically takes 24-48 hours for approval.',
    category: 'Account'
  },
  {
    id: '8',
    question: 'Are there any fees?',
    answer: 'We charge minimal fees for certain services like external transfers (1.5%) and early withdrawals (5%). Account maintenance and deposits are free.',
    category: 'Fees'
  },
  {
    id: '9',
    question: 'How is interest calculated and paid?',
    answer: 'Interest is calculated daily and compounded monthly. Payments are made directly to your account at the end of each month or at maturity, depending on your plan.',
    category: 'Interest & Returns'
  },
  {
    id: '10',
    question: 'Can I have multiple savings plans?',
    answer: 'Yes, you can create multiple individual savings plans and participate in multiple group savings simultaneously. Each plan can have different goals and terms.',
    category: 'General'
  }
];

const categories = [
  { name: 'All', icon: FiHelpCircle, color: 'text-green-400' },
  { name: 'General', icon: FiHelpCircle, color: 'text-blue-400' },
  { name: 'Interest & Returns', icon: FiDollarSign, color: 'text-green-400' },
  { name: 'Security', icon: FiShield, color: 'text-red-400' },
  { name: 'Group Savings', icon: FiUsers, color: 'text-purple-400' },
  { name: 'Payments', icon: FiCreditCard, color: 'text-yellow-400' },
  { name: 'Account', icon: FiSettings, color: 'text-gray-400' }
];

export default function FAQPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const filteredFAQs = faqData.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'All' || faq.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const toggleExpanded = (id: string) => {
    setExpandedItems(prev =>
      prev.includes(id)
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-green-900 to-black text-white">
      <BackgroundAnimation variant="default" />
      
      {/* Header */}
      <header className="relative z-50 px-6 py-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <Link href="/" className="flex items-center">
            <BetterInterestLogo size="md" variant="light" />
          </Link>
          <Link
            href="/"
            className="inline-flex items-center text-gray-400 hover:text-white transition-colors"
          >
            <FiArrowLeft className="mr-2" />
            Back to Home
          </Link>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 max-w-6xl mx-auto px-6 py-12">
        {/* Page Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <div className="flex items-center justify-center mb-4">
            <FiHelpCircle className="text-green-400 text-4xl mr-3" />
            <h1 className="text-4xl font-bold">Frequently Asked Questions</h1>
          </div>
          <p className="text-gray-300 text-lg max-w-2xl mx-auto">
            Find answers to common questions about BetterInterest savings platform
          </p>
        </motion.div>

        {/* Search and Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-8"
        >
          <Card3D className="bg-gray-900/50 border-gray-800 p-6">
            {/* Search */}
            <div className="mb-6">
              <div className="relative max-w-md mx-auto">
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search FAQs..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:border-green-500 focus:outline-none text-white"
                />
              </div>
            </div>

            {/* Category Filters */}
            <div className="flex flex-wrap gap-2 justify-center">
              {categories.map((category) => {
                const Icon = category.icon;
                const isActive = selectedCategory === category.name;
                return (
                  <button
                    key={category.name}
                    onClick={() => setSelectedCategory(category.name)}
                    className={`flex items-center px-4 py-2 rounded-lg transition-all duration-200 ${
                      isActive
                        ? 'bg-green-600 text-white'
                        : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                    }`}
                  >
                    <Icon className={`mr-2 ${isActive ? 'text-white' : category.color}`} />
                    {category.name}
                  </button>
                );
              })}
            </div>
          </Card3D>
        </motion.div>

        {/* FAQ Items */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="space-y-4"
        >
          {filteredFAQs.length === 0 ? (
            <Card3D className="bg-gray-900/50 border-gray-800 p-8 text-center">
              <FiHelpCircle className="text-gray-500 text-4xl mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-400 mb-2">No FAQs found</h3>
              <p className="text-gray-500">
                Try adjusting your search or selecting a different category
              </p>
            </Card3D>
          ) : (
            filteredFAQs.map((faq, index) => {
              const isExpanded = expandedItems.includes(faq.id);
              return (
                <motion.div
                  key={faq.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card3D className="bg-gray-900/50 border-gray-800 overflow-hidden">
                    <button
                      onClick={() => toggleExpanded(faq.id)}
                      className="w-full p-6 text-left hover:bg-gray-800/30 transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center mb-2">
                            <span className="text-xs bg-green-600 text-white px-2 py-1 rounded-full mr-3">
                              {faq.category}
                            </span>
                          </div>
                          <h3 className="text-lg font-semibold text-white">
                            {faq.question}
                          </h3>
                        </div>
                        <motion.div
                          animate={{ rotate: isExpanded ? 180 : 0 }}
                          transition={{ duration: 0.2 }}
                          className="ml-4"
                        >
                          <FiChevronDown className="text-gray-400 text-xl" />
                        </motion.div>
                      </div>
                    </button>

                    <AnimatePresence>
                      {isExpanded && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: 'auto', opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3 }}
                          className="overflow-hidden"
                        >
                          <div className="px-6 pb-6 border-t border-gray-700">
                            <p className="text-gray-300 leading-relaxed pt-4">
                              {faq.answer}
                            </p>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </Card3D>
                </motion.div>
              );
            })
          )}
        </motion.div>

        {/* Contact Support */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="mt-12"
        >
          <Card3D className="bg-gradient-to-r from-green-900/50 to-blue-900/50 border-green-800 p-8 text-center">
            <FiHelpCircle className="text-green-400 text-4xl mx-auto mb-4" />
            <h3 className="text-2xl font-bold text-white mb-2">Still have questions?</h3>
            <p className="text-gray-300 mb-6">
              Our support team is here to help you 24/7
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="inline-flex items-center justify-center px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
              >
                <FiHelpCircle className="mr-2" />
                Contact Support
              </Link>
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center justify-center px-6 py-3 bg-gray-800 hover:bg-gray-700 text-white rounded-lg transition-colors"
              >
                Email Us
              </a>
            </div>
          </Card3D>
        </motion.div>
      </main>
    </div>
  );
}
