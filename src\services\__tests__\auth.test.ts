import { authService } from '../auth';
import { ApiError, ValidationError, AuthenticationError } from '../errorHandler';

// Mock fetch globally
global.fetch = jest.fn();

const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

describe('AuthService', () => {
  beforeEach(() => {
    mockFetch.mockClear();
    localStorage.clear();
  });

  describe('login', () => {
    it('should login successfully and store tokens', async () => {
      const mockResponse = {
        user: {
          id: '1',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          role: 'USER',
          isVerified: true,
          kycStatus: 'PENDING',
          balance: 0,
          totalSavings: 0,
          totalEarnings: 0,
          isActive: true,
          createdAt: '2023-01-01T00:00:00Z',
          updatedAt: '2023-01-01T00:00:00Z'
        },
        token: 'mock-jwt-token',
        refreshToken: 'mock-refresh-token',
        expiresIn: 3600
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
        headers: new Headers({ 'content-type': 'application/json' })
      } as Response);

      const credentials = {
        email: '<EMAIL>',
        password: 'password123'
      };

      const result = await authService.login(credentials);

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/auth/login',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(credentials)
        })
      );

      expect(result).toEqual(mockResponse);
      expect(localStorage.getItem('auth_token')).toBe('mock-jwt-token');
      expect(localStorage.getItem('refresh_token')).toBe('mock-refresh-token');
    });

    it('should throw ValidationError for invalid credentials', async () => {
      const mockErrorResponse = {
        success: false,
        message: 'Validation failed',
        errors: {
          email: ['Email is required'],
          password: ['Password must be at least 8 characters']
        }
      };

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 422,
        json: async () => mockErrorResponse,
        headers: new Headers({ 'content-type': 'application/json' })
      } as Response);

      const credentials = {
        email: '',
        password: '123'
      };

      await expect(authService.login(credentials)).rejects.toThrow(ValidationError);
    });

    it('should throw AuthenticationError for invalid login', async () => {
      const mockErrorResponse = {
        success: false,
        message: 'Invalid email or password',
        code: 'INVALID_CREDENTIALS'
      };

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: async () => mockErrorResponse,
        headers: new Headers({ 'content-type': 'application/json' })
      } as Response);

      const credentials = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      };

      await expect(authService.login(credentials)).rejects.toThrow(AuthenticationError);
    });
  });

  describe('signup', () => {
    it('should signup successfully', async () => {
      const mockResponse = {
        user: {
          id: '1',
          email: '<EMAIL>',
          firstName: 'Jane',
          lastName: 'Doe',
          role: 'USER',
          isVerified: false,
          kycStatus: 'PENDING',
          balance: 0,
          totalSavings: 0,
          totalEarnings: 0,
          isActive: true,
          createdAt: '2023-01-01T00:00:00Z',
          updatedAt: '2023-01-01T00:00:00Z'
        },
        token: 'mock-jwt-token',
        refreshToken: 'mock-refresh-token',
        expiresIn: 3600
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
        headers: new Headers({ 'content-type': 'application/json' })
      } as Response);

      const signupData = {
        firstName: 'Jane',
        lastName: 'Doe',
        email: '<EMAIL>',
        phoneNumber: '+2348012345678',
        password: 'password123',
        confirmPassword: 'password123',
        agreeToTerms: true
      };

      const result = await authService.signup(signupData);

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/auth/signup',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(signupData)
        })
      );

      expect(result).toEqual(mockResponse);
      expect(localStorage.getItem('auth_token')).toBe('mock-jwt-token');
    });

    it('should throw ValidationError for invalid signup data', async () => {
      const mockErrorResponse = {
        success: false,
        message: 'Validation failed',
        errors: {
          email: ['Email already exists'],
          password: ['Password must contain at least one uppercase letter']
        }
      };

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 422,
        json: async () => mockErrorResponse,
        headers: new Headers({ 'content-type': 'application/json' })
      } as Response);

      const signupData = {
        firstName: 'Jane',
        lastName: 'Doe',
        email: '<EMAIL>',
        phoneNumber: '+2348012345678',
        password: 'password',
        confirmPassword: 'password',
        agreeToTerms: true
      };

      await expect(authService.signup(signupData)).rejects.toThrow(ValidationError);
    });
  });

  describe('getCurrentUser', () => {
    it('should get current user successfully', async () => {
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: 'USER',
        isVerified: true,
        kycStatus: 'APPROVED',
        balance: 50000,
        totalSavings: 100000,
        totalEarnings: 5000,
        isActive: true,
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z'
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockUser,
        headers: new Headers({ 'content-type': 'application/json' })
      } as Response);

      localStorage.setItem('auth_token', 'mock-jwt-token');

      const result = await authService.getCurrentUser();

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/user/me',
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': 'Bearer mock-jwt-token'
          })
        })
      );

      expect(result).toEqual(mockUser);
    });

    it('should throw AuthenticationError when not authenticated', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: async () => ({ message: 'Authentication required' }),
        headers: new Headers({ 'content-type': 'application/json' })
      } as Response);

      await expect(authService.getCurrentUser()).rejects.toThrow(AuthenticationError);
    });
  });

  describe('logout', () => {
    it('should logout successfully and clear tokens', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true }),
        headers: new Headers({ 'content-type': 'application/json' })
      } as Response);

      localStorage.setItem('auth_token', 'mock-jwt-token');
      localStorage.setItem('refresh_token', 'mock-refresh-token');

      await authService.logout();

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/auth/logout',
        expect.objectContaining({
          method: 'POST'
        })
      );

      expect(localStorage.getItem('auth_token')).toBeNull();
      expect(localStorage.getItem('refresh_token')).toBeNull();
    });

    it('should clear tokens even if API call fails', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      localStorage.setItem('auth_token', 'mock-jwt-token');
      localStorage.setItem('refresh_token', 'mock-refresh-token');

      await authService.logout();

      expect(localStorage.getItem('auth_token')).toBeNull();
      expect(localStorage.getItem('refresh_token')).toBeNull();
    });
  });

  describe('refreshToken', () => {
    it('should refresh token successfully', async () => {
      const mockResponse = {
        token: 'new-jwt-token',
        expiresIn: 3600
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
        headers: new Headers({ 'content-type': 'application/json' })
      } as Response);

      localStorage.setItem('refresh_token', 'mock-refresh-token');

      const result = await authService.refreshToken();

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/auth/refresh',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({ refreshToken: 'mock-refresh-token' })
        })
      );

      expect(result).toEqual(mockResponse);
      expect(localStorage.getItem('auth_token')).toBe('new-jwt-token');
    });

    it('should throw error when no refresh token available', async () => {
      await expect(authService.refreshToken()).rejects.toThrow('No refresh token available');
    });
  });

  describe('token validation', () => {
    it('should correctly identify expired tokens', () => {
      // Create an expired token (exp in the past)
      const expiredPayload = { exp: Math.floor(Date.now() / 1000) - 3600 }; // 1 hour ago
      const expiredToken = `header.${btoa(JSON.stringify(expiredPayload))}.signature`;

      expect(authService.isTokenExpired(expiredToken)).toBe(true);
    });

    it('should correctly identify valid tokens', () => {
      // Create a valid token (exp in the future)
      const validPayload = { exp: Math.floor(Date.now() / 1000) + 3600 }; // 1 hour from now
      const validToken = `header.${btoa(JSON.stringify(validPayload))}.signature`;

      expect(authService.isTokenExpired(validToken)).toBe(false);
    });

    it('should return true for malformed tokens', () => {
      expect(authService.isTokenExpired('invalid-token')).toBe(true);
    });
  });
});
