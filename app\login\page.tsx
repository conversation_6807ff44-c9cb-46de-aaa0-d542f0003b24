"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '../../src/components/ui/Button';
import { Card3D } from '../../src/components/ui/Card3D';
import { BetterInterestLogo } from '../../src/components/ui/BetterInterestLogo';
import { BackgroundAnimation } from '../../src/components/ui/BackgroundAnimation';
import { 
  FiMail, 
  FiLock, 
  FiEye, 
  FiEyeOff, 
  FiArrowRight,
  FiShield,
  FiTrendingUp,
  FiUsers
} from 'react-icons/fi';
import { FaGoogle, FaFacebook, FaApple } from 'react-icons/fa';

export default function LoginPage() {
  const [showPassword, setShowPassword] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    // Simulate login process
    setTimeout(() => {
      setIsLoading(false);
      // Redirect to dashboard
      window.location.href = '/dashboard';
    }, 2000);
  };

  return (
    <div className="min-h-screen bg-theme text-theme relative overflow-hidden">
      {/* Background Animation */}
      <BackgroundAnimation variant="subtle" />
      
      <div className="relative z-10 min-h-screen flex">
        {/* Left Side - Login Form */}
        <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="w-full max-w-md"
          >
            {/* Logo */}
            <div className="text-center mb-8">
              <BetterInterestLogo size="lg" showText showIcon className="mx-auto mb-4" />
              <h1 className="text-3xl font-bold text-theme font-inter mb-2">
                Welcome Back
              </h1>
              <p className="text-theme-secondary font-inter">
                Sign in to your BetterInterest account
              </p>
            </div>

            {/* Login Form */}
            <Card3D elevation={2} className="p-8">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Email Field */}
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-theme mb-2 font-inter">
                    Email Address
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <FiMail className="h-5 w-5 text-theme-secondary" />
                    </div>
                    <input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="block w-full pl-10 pr-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme placeholder-theme-secondary focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand transition-colors font-inter"
                      placeholder="Enter your email"
                      required
                    />
                  </div>
                </div>

                {/* Password Field */}
                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-theme mb-2 font-inter">
                    Password
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <FiLock className="h-5 w-5 text-theme-secondary" />
                    </div>
                    <input
                      id="password"
                      type={showPassword ? 'text' : 'password'}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="block w-full pl-10 pr-12 py-3 border border-theme rounded-lg bg-theme-secondary text-theme placeholder-theme-secondary focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand transition-colors font-inter"
                      placeholder="Enter your password"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    >
                      {showPassword ? (
                        <FiEyeOff className="h-5 w-5 text-theme-secondary hover:text-brand transition-colors" />
                      ) : (
                        <FiEye className="h-5 w-5 text-theme-secondary hover:text-brand transition-colors" />
                      )}
                    </button>
                  </div>
                </div>

                {/* Remember Me & Forgot Password */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <input
                      id="remember-me"
                      type="checkbox"
                      className="h-4 w-4 text-brand focus:ring-brand border-theme-secondary rounded"
                    />
                    <label htmlFor="remember-me" className="ml-2 block text-sm text-theme-secondary font-inter">
                      Remember me
                    </label>
                  </div>
                  <Link
                    href="/forgot-password"
                    className="text-sm text-brand hover:text-brand-dark transition-colors font-inter"
                  >
                    Forgot password?
                  </Link>
                </div>

                {/* Login Button */}
                <Button
                  type="submit"
                  fullWidth
                  size="lg"
                  loading={isLoading}
                  rightIcon={FiArrowRight}
                  className="font-inter"
                >
                  {isLoading ? 'Signing in...' : 'Sign In'}
                </Button>
              </form>

              {/* Divider */}
              <div className="mt-6">
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-theme-secondary" />
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-theme text-theme-secondary font-inter">Or continue with</span>
                  </div>
                </div>
              </div>

              {/* Social Login */}
              <div className="mt-6 grid grid-cols-3 gap-3">
                <button className="w-full inline-flex justify-center py-3 px-4 border border-theme-secondary rounded-lg bg-theme-secondary hover:bg-theme transition-colors">
                  <FaGoogle className="h-5 w-5 text-red-500" />
                </button>
                <button className="w-full inline-flex justify-center py-3 px-4 border border-theme-secondary rounded-lg bg-theme-secondary hover:bg-theme transition-colors">
                  <FaFacebook className="h-5 w-5 text-blue-500" />
                </button>
                <button className="w-full inline-flex justify-center py-3 px-4 border border-theme-secondary rounded-lg bg-theme-secondary hover:bg-theme transition-colors">
                  <FaApple className="h-5 w-5 text-theme" />
                </button>
              </div>

              {/* Sign Up Link */}
              <div className="mt-6 text-center">
                <p className="text-sm text-theme-secondary font-inter">
                  Don't have an account?{' '}
                  <Link
                    href="/signup"
                    className="text-brand hover:text-brand-dark font-medium transition-colors"
                  >
                    Sign up
                  </Link>
                </p>
              </div>
            </Card3D>
          </motion.div>
        </div>

        {/* Right Side - Hero Image & Features */}
        <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-brand/10 to-brand-dark/10 items-center justify-center p-8">
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="max-w-lg text-center"
          >
            {/* Hero Image */}
            <div className="mb-8">
              <Image
                src="/images/nneww.png"
                alt="BetterInterest Financial Success"
                width={400}
                height={300}
                className="mx-auto rounded-2xl shadow-2xl"
                priority
              />
            </div>

            {/* Features */}
            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-theme font-inter mb-6">
                Join thousands who trust BetterInterest
              </h2>
              
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="p-2 rounded-lg bg-brand/20">
                    <FiShield className="w-5 h-5 text-brand" />
                  </div>
                  <div className="text-left">
                    <h3 className="font-semibold text-theme font-inter">Bank-Level Security</h3>
                    <p className="text-sm text-theme-secondary">Your money is protected with 256-bit encryption</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="p-2 rounded-lg bg-brand/20">
                    <FiTrendingUp className="w-5 h-5 text-brand" />
                  </div>
                  <div className="text-left">
                    <h3 className="font-semibold text-theme font-inter">Better Returns</h3>
                    <p className="text-sm text-theme-secondary">Earn up to 15% annual interest on your savings</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="p-2 rounded-lg bg-brand/20">
                    <FiUsers className="w-5 h-5 text-brand" />
                  </div>
                  <div className="text-left">
                    <h3 className="font-semibold text-theme font-inter">50K+ Happy Users</h3>
                    <p className="text-sm text-theme-secondary">Join our growing community of smart savers</p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
