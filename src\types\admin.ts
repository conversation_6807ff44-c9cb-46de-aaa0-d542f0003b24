export interface AdminDashboardStats {
  users: {
    total: number;
    active: number;
    newThisMonth: number;
    kycPending: number;
    kycApproved: number;
    kycRejected: number;
  };
  
  savings: {
    totalPlans: number;
    activePlans: number;
    completedPlans: number;
    totalSavingsAmount: number;
    totalInterestPaid: number;
    averagePlanValue: number;
  };
  
  groups: {
    totalGroups: number;
    activeGroups: number;
    totalMembers: number;
    totalContributions: number;
    completionRate: number;
  };
  
  transactions: {
    totalTransactions: number;
    totalVolume: number;
    depositsThisMonth: number;
    withdrawalsThisMonth: number;
    successRate: number;
    averageTransactionValue: number;
  };
  
  deposits: {
    totalDeposits: number;
    totalAmount: number;
    pendingDeposits: number;
    failedDeposits: number;
    successRate: number;
    averageDepositAmount: number;
  };
  
  withdrawals: {
    totalWithdrawals: number;
    totalAmount: number;
    pendingWithdrawals: number;
    pendingAmount: number;
    totalPenalties: number;
    averageProcessingTime: number;
  };
  
  kyc: {
    totalSubmissions: number;
    pendingReview: number;
    approvedThisMonth: number;
    rejectedThisMonth: number;
    averageReviewTime: number;
  };
  
  notifications: {
    totalSent: number;
    sentThisMonth: number;
    deliveryRate: number;
    unreadCount: number;
  };
}

export interface AdminAnalytics {
  userGrowth: {
    period: string;
    newUsers: number;
    activeUsers: number;
    churnRate: number;
  }[];
  
  savingsGrowth: {
    period: string;
    totalSavings: number;
    newPlans: number;
    completedPlans: number;
    interestPaid: number;
  }[];
  
  transactionVolume: {
    period: string;
    deposits: number;
    withdrawals: number;
    contributions: number;
    volume: number;
  }[];
  
  revenueMetrics: {
    period: string;
    fees: number;
    penalties: number;
    interest: number;
    totalRevenue: number;
  }[];
  
  userEngagement: {
    dailyActiveUsers: number;
    weeklyActiveUsers: number;
    monthlyActiveUsers: number;
    averageSessionDuration: number;
    retentionRate: number;
  };
  
  planPerformance: {
    planType: string;
    count: number;
    totalValue: number;
    completionRate: number;
    averageDuration: number;
  }[];
}

export interface AdminUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'SUPER_ADMIN' | 'ADMIN' | 'SUPPORT' | 'FINANCE' | 'COMPLIANCE';
  permissions: AdminPermission[];
  isActive: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AdminPermission {
  id: string;
  name: string;
  description: string;
  resource: string;
  action: 'CREATE' | 'READ' | 'UPDATE' | 'DELETE' | 'APPROVE' | 'REJECT';
}

export interface AdminActivity {
  id: string;
  adminId: string;
  admin: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  action: string;
  resource: string;
  resourceId?: string;
  description: string;
  metadata?: Record<string, any>;
  ipAddress: string;
  userAgent: string;
  createdAt: string;
}

export interface AdminSettings {
  id: string;
  key: string;
  value: string | number | boolean | object;
  type: 'STRING' | 'NUMBER' | 'BOOLEAN' | 'JSON';
  category: 'GENERAL' | 'FINANCIAL' | 'SECURITY' | 'NOTIFICATIONS' | 'FEATURES';
  description: string;
  isEditable: boolean;
  isPublic: boolean;
  validationRules?: {
    min?: number;
    max?: number;
    pattern?: string;
    required?: boolean;
    options?: string[];
  };
  updatedBy?: string;
  updatedAt: string;
}

export interface SystemHealth {
  status: 'HEALTHY' | 'WARNING' | 'CRITICAL';
  uptime: number;
  version: string;
  environment: 'DEVELOPMENT' | 'STAGING' | 'PRODUCTION';
  
  services: {
    database: {
      status: 'UP' | 'DOWN' | 'DEGRADED';
      responseTime: number;
      connections: number;
      maxConnections: number;
    };
    
    paystack: {
      status: 'UP' | 'DOWN' | 'DEGRADED';
      responseTime: number;
      lastSuccessfulCall: string;
      errorRate: number;
    };
    
    email: {
      status: 'UP' | 'DOWN' | 'DEGRADED';
      responseTime: number;
      deliveryRate: number;
      queueSize: number;
    };
    
    sms: {
      status: 'UP' | 'DOWN' | 'DEGRADED';
      responseTime: number;
      deliveryRate: number;
      queueSize: number;
    };
  };
  
  metrics: {
    cpuUsage: number;
    memoryUsage: number;
    diskUsage: number;
    networkLatency: number;
    errorRate: number;
    requestsPerMinute: number;
  };
  
  alerts: {
    id: string;
    level: 'INFO' | 'WARNING' | 'ERROR' | 'CRITICAL';
    message: string;
    timestamp: string;
  }[];
}

export interface AdminReport {
  id: string;
  name: string;
  type: 'USER_ACTIVITY' | 'FINANCIAL_SUMMARY' | 'TRANSACTION_REPORT' | 'KYC_REPORT' | 'SAVINGS_REPORT' | 'CUSTOM';
  description: string;
  parameters: Record<string, any>;
  format: 'PDF' | 'EXCEL' | 'CSV';
  status: 'PENDING' | 'GENERATING' | 'COMPLETED' | 'FAILED';
  fileUrl?: string;
  fileSize?: number;
  generatedBy: string;
  generatedAt?: string;
  expiresAt?: string;
  createdAt: string;
}

export interface CreateAdminReportData {
  name: string;
  type: 'USER_ACTIVITY' | 'FINANCIAL_SUMMARY' | 'TRANSACTION_REPORT' | 'KYC_REPORT' | 'SAVINGS_REPORT' | 'CUSTOM';
  description?: string;
  parameters: Record<string, any>;
  format: 'PDF' | 'EXCEL' | 'CSV';
  scheduleType?: 'ONCE' | 'DAILY' | 'WEEKLY' | 'MONTHLY';
  scheduleTime?: string;
  recipients?: string[];
}

export interface AdminSearchFilters {
  search?: string;
  role?: 'SUPER_ADMIN' | 'ADMIN' | 'SUPPORT' | 'FINANCE' | 'COMPLIANCE';
  isActive?: boolean;
  dateFrom?: string;
  dateTo?: string;
  sortBy?: 'createdAt' | 'lastLoginAt' | 'firstName' | 'email';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface AdminActivityFilters {
  search?: string;
  adminId?: string;
  action?: string;
  resource?: string;
  dateFrom?: string;
  dateTo?: string;
  sortBy?: 'createdAt' | 'action' | 'resource';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}
