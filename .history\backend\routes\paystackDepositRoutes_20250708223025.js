const express = require('express');
const axios = require('axios');
const router = express.Router();

const PAYSTACK_SECRET_KEY = process.env.PAYSTACK_SECRET_KEY;
const PAYSTACK_BASE_URL = process.env.PAYSTACK_BASE_URL || 'https://api.paystack.co';

// Initialize a Paystack payment (get payment link)
router.post('/initialize', async (req, res) => {
  const { amount, email, metadata } = req.body;
  // Accept userId from metadata or legacy user_id
  let userId = metadata?.userId || req.body.user_id;
  console.log('[Paystack] POST /deposit/initialize', {
    amount, email, userId, PAYSTACK_SECRET_KEY: !!PAYSTACK_SECRET_KEY, PAYSTACK_BASE_URL
  });
  if (!amount || !email) {
    return res.status(400).json({ status: false, message: 'Amount and email are required' });
  }
  try {
    // Set redirect_url to frontend deposit page (Paystack will append ?reference=...)
    const redirect_url = `${process.env.FRONTEND_URL || 'http://localhost:8030'}/user/payments`;
    const response = await axios.post(`${PAYSTACK_BASE_URL}/transaction/initialize`, {
      amount: Math.round(Number(amount)),
      email,
      metadata: { userId },
      currency: 'NGN',
      redirect_url,
    }, {
      headers: {
        Authorization: `Bearer ${PAYSTACK_SECRET_KEY}`,
        'Content-Type': 'application/json',
      },
    });
    res.json(response.data);
  } catch (error) {
    console.error('[Paystack] Error initializing deposit:', error.response?.data || error.message);
    res.status(500).json({ status: false, message: 'Failed to initialize deposit', error: error.message, details: error.response?.data });
  }
});

// Verify a Paystack payment
router.get('/verify/:reference', async (req, res) => {
  const { reference } = req.params;
  console.log('[Paystack] GET /deposit/verify', { reference, PAYSTACK_SECRET_KEY: !!PAYSTACK_SECRET_KEY, PAYSTACK_BASE_URL });
  if (!reference) {
    return res.status(400).json({ status: false, message: 'Reference is required' });
  }
  try {
    const response = await axios.get(`${PAYSTACK_BASE_URL}/transaction/verify/${reference}`, {
      headers: {
        Authorization: `Bearer ${PAYSTACK_SECRET_KEY}`,
      },
    });
    // Update user balance, deposit, and transaction if payment is successful
    const data = response.data;
    if (data.status && data.data && data.data.status === 'success') {
      const metadata = data.data.metadata || {};
      const userId = metadata.userId || metadata.user_id;
      const amount = data.data.amount;
      if (userId && amount) {
        const User = require('../models/user');
        const Deposit = require('../models/deposit');
        const Transaction = require('../models/transaction');
        const nairaAmount = amount / 100;
        // Log before update
        const userBefore = await User.findById(userId);
        console.log('[Paystack][Verify] User before update:', userBefore);
        // Update user balance
        const user = await User.findByIdAndUpdate(
          userId,
          { $inc: { balance: nairaAmount } },
          { new: true }
        );
        if (user) {
          console.log('[Paystack][Verify] User after update:', user);
        } else {
          console.warn('[Paystack][Verify] User not found for update:', userId);
        }
        // Mark deposit as successful if exists
        const deposit = await Deposit.findOne({ paystackReference: reference, userId });
        if (deposit) {
          if (deposit.status !== 'success') {
            deposit.status = 'success';
            deposit.paystackStatus = 'success';
            await deposit.save();
            console.log('[Paystack][Verify] Deposit marked as success:', deposit);
          } else {
            console.log('[Paystack][Verify] Deposit already marked as success:', deposit);
          }
        } else {
          console.warn('[Paystack][Verify] No deposit record found for reference:', reference, 'userId:', userId);
        }
        // Create transaction record
        const existingTx = await Transaction.findOne({ reference, userId });
        if (!existingTx) {
          const tx = await Transaction.create({
            date: new Date(),
            description: 'Deposit via Paystack (verify endpoint)',
            type: 'deposit',
            amount: nairaAmount,
            userId,
            balanceAfter: user ? user.balance : 0,
            reference,
          });
          console.log('[Paystack][Verify] Transaction record created:', tx);
        } else {
          console.log('[Paystack][Verify] Transaction already exists for reference:', reference);
        }
      } else {
        console.warn('[Paystack][Verify] Missing userId or amount in verify payload:', { userId, amount });
      }
    }
    // Also return updated total deposits for the user if possible
    let totalDeposits = null;
    try {
      if (data.status && data.data && data.data.status === 'success') {
        const Transaction = require('../models/transaction');
        const metadata = data.data.metadata || {};
        const userId = metadata.userId || metadata.user_id;
        if (userId) {
          const totalTx = await Transaction.aggregate([
            { $match: { userId: require('mongoose').Types.ObjectId(userId), type: 'deposit' } },
            { $group: { _id: null, total: { $sum: '$amount' } } }
          ]);
          totalDeposits = totalTx[0] ? totalTx[0].total : 0;
        }
      }
    } catch (aggErr) {
      console.error('[Paystack][Verify] Error calculating total deposits:', aggErr);
    }
    res.json({ ...data, totalDeposits });
  } catch (error) {
    console.error('[Paystack] Error verifying deposit:', error.response?.data || error.message);
    res.status(500).json({ status: false, message: 'Failed to verify deposit', error: error.message, details: error.response?.data });
  }
});

module.exports = router;
