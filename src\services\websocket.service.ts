import { io, Socket } from 'socket.io-client';
import { authService } from './auth.service';

export interface WebSocketEvent {
  type: string;
  data: any;
  timestamp: string;
  userId?: string;
}

export interface NotificationEvent extends WebSocketEvent {
  type: 'notification';
  data: {
    id: string;
    title: string;
    message: string;
    type: 'DEPOSIT' | 'WITHDRAWAL' | 'CONTRIBUTION' | 'SYSTEM' | 'SECURITY' | 'PROMOTIONAL';
    priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
    actionRequired?: boolean;
    actionUrl?: string;
    actionText?: string;
  };
}

export interface TransactionEvent extends WebSocketEvent {
  type: 'transaction';
  data: {
    id: string;
    type: 'DEPOSIT' | 'WITHDRAWAL' | 'TRANSFER' | 'CONTRIBUTION';
    amount: number;
    status: 'PENDING' | 'COMPLETED' | 'FAILED';
    reference: string;
    description: string;
  };
}

export interface BalanceUpdateEvent extends WebSocketEvent {
  type: 'balance_update';
  data: {
    newBalance: number;
    previousBalance: number;
    change: number;
    reason: string;
  };
}

export interface GroupSavingsEvent extends WebSocketEvent {
  type: 'group_savings';
  data: {
    groupId: string;
    groupName: string;
    eventType: 'MEMBER_JOINED' | 'MEMBER_LEFT' | 'CONTRIBUTION_MADE' | 'PAYOUT_RECEIVED' | 'GOAL_ACHIEVED';
    memberName?: string;
    amount?: number;
    message: string;
  };
}

export interface KYCUpdateEvent extends WebSocketEvent {
  type: 'kyc_update';
  data: {
    status: 'PENDING' | 'UNDER_REVIEW' | 'APPROVED' | 'REJECTED';
    level: 'BASIC' | 'INTERMEDIATE' | 'ADVANCED';
    message: string;
    rejectionReason?: string;
  };
}

export interface SystemMaintenanceEvent extends WebSocketEvent {
  type: 'system_maintenance';
  data: {
    type: 'SCHEDULED' | 'EMERGENCY' | 'COMPLETED';
    startTime?: string;
    endTime?: string;
    message: string;
    affectedServices: string[];
  };
}

export type AllWebSocketEvents = 
  | NotificationEvent 
  | TransactionEvent 
  | BalanceUpdateEvent 
  | GroupSavingsEvent 
  | KYCUpdateEvent 
  | SystemMaintenanceEvent;

class WebSocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private eventListeners: Map<string, Set<(event: AllWebSocketEvents) => void>> = new Map();
  private connectionListeners: Set<(connected: boolean) => void> = new Set();
  private isConnected = false;

  constructor() {
    this.initializeConnection();
  }

  private initializeConnection() {
    const token = authService.getToken();
    if (!token) {
      console.warn('No auth token available for WebSocket connection');
      return;
    }

    const wsUrl = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3001';
    
    this.socket = io(wsUrl, {
      auth: {
        token
      },
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true
    });

    this.setupEventHandlers();
  }

  private setupEventHandlers() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('WebSocket connected');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.notifyConnectionListeners(true);
    });

    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason);
      this.isConnected = false;
      this.notifyConnectionListeners(false);
      
      if (reason === 'io server disconnect') {
        // Server initiated disconnect, try to reconnect
        this.handleReconnection();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      this.isConnected = false;
      this.notifyConnectionListeners(false);
      this.handleReconnection();
    });

    // Handle all event types
    this.socket.on('notification', (data) => this.handleEvent({ type: 'notification', data, timestamp: new Date().toISOString() }));
    this.socket.on('transaction', (data) => this.handleEvent({ type: 'transaction', data, timestamp: new Date().toISOString() }));
    this.socket.on('balance_update', (data) => this.handleEvent({ type: 'balance_update', data, timestamp: new Date().toISOString() }));
    this.socket.on('group_savings', (data) => this.handleEvent({ type: 'group_savings', data, timestamp: new Date().toISOString() }));
    this.socket.on('kyc_update', (data) => this.handleEvent({ type: 'kyc_update', data, timestamp: new Date().toISOString() }));
    this.socket.on('system_maintenance', (data) => this.handleEvent({ type: 'system_maintenance', data, timestamp: new Date().toISOString() }));

    // Handle authentication errors
    this.socket.on('auth_error', (error) => {
      console.error('WebSocket authentication error:', error);
      this.disconnect();
      // Trigger re-authentication
      authService.refreshToken().then(() => {
        this.reconnect();
      }).catch(() => {
        // Redirect to login if refresh fails
        window.location.href = '/auth/login';
      });
    });
  }

  private handleEvent(event: AllWebSocketEvents) {
    const listeners = this.eventListeners.get(event.type);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.error('Error in WebSocket event listener:', error);
        }
      });
    }

    // Also notify global listeners
    const globalListeners = this.eventListeners.get('*');
    if (globalListeners) {
      globalListeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.error('Error in global WebSocket event listener:', error);
        }
      });
    }
  }

  private handleReconnection() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff

    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);
    
    setTimeout(() => {
      this.reconnect();
    }, delay);
  }

  private notifyConnectionListeners(connected: boolean) {
    this.connectionListeners.forEach(listener => {
      try {
        listener(connected);
      } catch (error) {
        console.error('Error in connection listener:', error);
      }
    });
  }

  // Public methods
  public connect() {
    if (!this.socket || !this.socket.connected) {
      this.initializeConnection();
    }
  }

  public disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.isConnected = false;
  }

  public reconnect() {
    this.disconnect();
    this.connect();
  }

  public isSocketConnected(): boolean {
    return this.isConnected && this.socket?.connected === true;
  }

  // Event subscription methods
  public on<T extends AllWebSocketEvents>(
    eventType: T['type'] | '*', 
    listener: (event: T) => void
  ): () => void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, new Set());
    }
    
    const listeners = this.eventListeners.get(eventType)!;
    listeners.add(listener as any);

    // Return unsubscribe function
    return () => {
      listeners.delete(listener as any);
      if (listeners.size === 0) {
        this.eventListeners.delete(eventType);
      }
    };
  }

  public off(eventType: string, listener?: (event: AllWebSocketEvents) => void) {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      if (listener) {
        listeners.delete(listener);
      } else {
        listeners.clear();
      }
      
      if (listeners.size === 0) {
        this.eventListeners.delete(eventType);
      }
    }
  }

  // Connection status subscription
  public onConnectionChange(listener: (connected: boolean) => void): () => void {
    this.connectionListeners.add(listener);
    
    // Immediately notify with current status
    listener(this.isConnected);
    
    // Return unsubscribe function
    return () => {
      this.connectionListeners.delete(listener);
    };
  }

  // Emit events to server
  public emit(eventType: string, data: any) {
    if (this.socket && this.socket.connected) {
      this.socket.emit(eventType, data);
    } else {
      console.warn('Cannot emit event: WebSocket not connected');
    }
  }

  // Join/leave rooms for group-specific events
  public joinRoom(roomId: string) {
    this.emit('join_room', { roomId });
  }

  public leaveRoom(roomId: string) {
    this.emit('leave_room', { roomId });
  }

  // Send typing indicators for chat features
  public sendTyping(roomId: string, isTyping: boolean) {
    this.emit('typing', { roomId, isTyping });
  }

  // Request real-time data updates
  public requestBalanceUpdate() {
    this.emit('request_balance_update', {});
  }

  public requestNotificationUpdate() {
    this.emit('request_notification_update', {});
  }

  // Heartbeat to keep connection alive
  public sendHeartbeat() {
    this.emit('heartbeat', { timestamp: new Date().toISOString() });
  }

  // Start periodic heartbeat
  public startHeartbeat(interval: number = 30000) {
    setInterval(() => {
      if (this.isSocketConnected()) {
        this.sendHeartbeat();
      }
    }, interval);
  }
}

// Create singleton instance
export const webSocketService = new WebSocketService();

// Auto-start heartbeat
webSocketService.startHeartbeat();

export default webSocketService;
