"use client";

import { useAuth } from "../../src/hooks/use-auth";
import Link from "next/link";
import { motion } from "framer-motion";
import { FiUser, FiShield, FiLogOut, FiHome, FiCopy, FiCheck, FiDollarSign, FiTrendingUp, FiTarget, FiSettings, FiCreditCard, FiPieChart } from "react-icons/fi";
import { useState } from "react";
import { PrimaryButton, OutlineButton } from "../../src/components/ui/AnimatedButton";

export default function TestAuthPage() {
  const { user, isAuthenticated, isLoading, logout } = useAuth();
  const [copiedCredential, setCopiedCredential] = useState<string | null>(null);

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedCredential(type);
      setTimeout(() => setCopiedCredential(null), 2000);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  const demoCredentials = [
    {
      type: "Regular User",
      email: "<EMAIL>",
      password: "Demo123!",
      description: "Access to user dashboard, savings plans, and personal features",
      color: "from-green-400 to-green-600"
    },
    {
      type: "Premium User",
      email: "<EMAIL>",
      password: "Premium123!",
      description: "Access to premium features, investment portfolios, and advanced analytics",
      color: "from-blue-400 to-blue-600"
    },
    {
      type: "Admin User",
      email: "<EMAIL>",
      password: "Admin123!",
      description: "Full admin access to all features, user management, and system settings",
      color: "from-purple-400 to-purple-600"
    },
    {
      type: "Test User",
      email: "<EMAIL>",
      password: "Test123!",
      description: "Testing account with sample data and transactions",
      color: "from-orange-400 to-orange-600"
    }
  ];

  const dashboardPages = [
    {
      name: "Main Dashboard",
      path: "/dashboard",
      icon: <FiHome className="w-5 h-5" />,
      description: "Overview of savings, goals, and recent activity"
    },
    {
      name: "Savings Plans",
      path: "/dashboard/savings-plans",
      icon: <FiDollarSign className="w-5 h-5" />,
      description: "Manage your savings goals and automated plans"
    },
    {
      name: "Investments",
      path: "/dashboard/investments",
      icon: <FiTrendingUp className="w-5 h-5" />,
      description: "Portfolio management and investment tracking"
    },
    {
      name: "Goals",
      path: "/dashboard/goals",
      icon: <FiTarget className="w-5 h-5" />,
      description: "Set and track your financial goals"
    },
    {
      name: "Transactions",
      path: "/dashboard/transactions",
      icon: <FiCreditCard className="w-5 h-5" />,
      description: "View transaction history and manage payments"
    },
    {
      name: "Analytics",
      path: "/dashboard/analytics",
      icon: <FiPieChart className="w-5 h-5" />,
      description: "Detailed financial analytics and insights"
    },
    {
      name: "Settings",
      path: "/dashboard/settings",
      icon: <FiSettings className="w-5 h-5" />,
      description: "Account settings and preferences"
    }
  ];

  if (isLoading) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto mb-4"></div>
          <p className="text-green-400">Loading authentication status...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 text-white p-8">
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-900/50 border border-gray-800 rounded-lg p-8 mb-8"
        >
          <h1 className="text-4xl font-display font-bold mb-6 text-center text-shadow-green">
            🔐 BetterInterest Demo Access
          </h1>
          <p className="text-center text-gray-300 mb-8 max-w-3xl mx-auto">
            Use these demo credentials to explore all features of the BetterInterest platform.
            Each account type provides different levels of access and functionality.
          </p>

          {/* Demo Credentials Section */}
          <div className="mb-12">
            <h2 className="text-2xl font-bold mb-6 text-center">
              🎯 Demo Credentials
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {demoCredentials.map((credential, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-gray-800/50 border border-gray-700 rounded-lg p-6 hover:border-green-400/50 transition-all"
                >
                  <div className={`inline-block px-3 py-1 rounded-full text-sm font-semibold mb-3 bg-gradient-to-r ${credential.color} text-white`}>
                    {credential.type}
                  </div>

                  <div className="space-y-3 mb-4">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-400">Email:</span>
                      <div className="flex items-center gap-2">
                        <code className="bg-gray-700 px-2 py-1 rounded text-green-400 text-sm">
                          {credential.email}
                        </code>
                        <button
                          onClick={() => copyToClipboard(credential.email, `${credential.type}-email`)}
                          className="text-gray-400 hover:text-green-400 transition-colors"
                        >
                          {copiedCredential === `${credential.type}-email` ? <FiCheck /> : <FiCopy />}
                        </button>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-gray-400">Password:</span>
                      <div className="flex items-center gap-2">
                        <code className="bg-gray-700 px-2 py-1 rounded text-green-400 text-sm">
                          {credential.password}
                        </code>
                        <button
                          onClick={() => copyToClipboard(credential.password, `${credential.type}-password`)}
                          className="text-gray-400 hover:text-green-400 transition-colors"
                        >
                          {copiedCredential === `${credential.type}-password` ? <FiCheck /> : <FiCopy />}
                        </button>
                      </div>
                    </div>
                  </div>

                  <p className="text-gray-300 text-sm mb-4">
                    {credential.description}
                  </p>

                  <div className="flex gap-2">
                    <PrimaryButton
                      href="/auth/login"
                      size="sm"
                      className="flex-1"
                    >
                      Login
                    </PrimaryButton>
                    <OutlineButton
                      href="/auth/signup"
                      size="sm"
                      className="flex-1"
                    >
                      Signup
                    </OutlineButton>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            {/* Authentication Status */}
            <div className="bg-gray-800/50 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <FiShield className="mr-2" />
                Authentication Status
              </h2>
              <div className="space-y-2">
                <p>
                  <span className="text-gray-400">Status:</span>{" "}
                  <span className={isAuthenticated ? "text-green-400" : "text-red-400"}>
                    {isAuthenticated ? "Authenticated" : "Not Authenticated"}
                  </span>
                </p>
                <p>
                  <span className="text-gray-400">Loading:</span>{" "}
                  <span className={isLoading ? "text-yellow-400" : "text-gray-300"}>
                    {isLoading ? "Yes" : "No"}
                  </span>
                </p>
              </div>
            </div>

            {/* User Information */}
            <div className="bg-gray-800/50 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <FiUser className="mr-2" />
                User Information
              </h2>
              {user ? (
                <div className="space-y-2">
                  <p>
                    <span className="text-gray-400">Name:</span>{" "}
                    <span className="text-white">{user.firstName} {user.lastName}</span>
                  </p>
                  <p>
                    <span className="text-gray-400">Email:</span>{" "}
                    <span className="text-white">{user.email}</span>
                  </p>
                  <p>
                    <span className="text-gray-400">Role:</span>{" "}
                    <span className={user.role === 'ADMIN' ? "text-red-400" : "text-green-400"}>
                      {user.role}
                    </span>
                  </p>
                  <p>
                    <span className="text-gray-400">Verified:</span>{" "}
                    <span className={user.isVerified ? "text-green-400" : "text-yellow-400"}>
                      {user.isVerified ? "Yes" : "No"}
                    </span>
                  </p>
                  <p>
                    <span className="text-gray-400">KYC Status:</span>{" "}
                    <span className={
                      user.kycStatus === 'APPROVED' ? "text-green-400" : 
                      user.kycStatus === 'REJECTED' ? "text-red-400" : "text-yellow-400"
                    }>
                      {user.kycStatus}
                    </span>
                  </p>
                </div>
              ) : (
                <p className="text-gray-400">No user data available</p>
              )}
            </div>
          </div>

          {/* Dashboard Navigation */}
          {isAuthenticated && (
            <div className="mb-8">
              <h2 className="text-2xl font-bold mb-6 text-center">
                🚀 Dashboard Pages
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {dashboardPages.map((page, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                  >
                    <Link
                      href={page.path}
                      className="block bg-gray-800/50 border border-gray-700 rounded-lg p-4 hover:border-green-400/50 hover:bg-gray-800/70 transition-all group"
                    >
                      <div className="flex items-center mb-2">
                        <div className="text-green-400 mr-3 group-hover:scale-110 transition-transform">
                          {page.icon}
                        </div>
                        <h3 className="font-semibold text-white group-hover:text-green-400 transition-colors">
                          {page.name}
                        </h3>
                      </div>
                      <p className="text-gray-400 text-sm">
                        {page.description}
                      </p>
                    </Link>
                  </motion.div>
                ))}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-4 justify-center">
            <PrimaryButton href="/">
              <FiHome className="mr-2" />
              Home
            </PrimaryButton>

            {!isAuthenticated ? (
              <>
                <PrimaryButton href="/auth/login">
                  <FiUser className="mr-2" />
                  Login
                </PrimaryButton>
                <OutlineButton href="/auth/signup">
                  <FiUser className="mr-2" />
                  Sign Up
                </OutlineButton>
                <OutlineButton href="/admin/login">
                  <FiShield className="mr-2" />
                  Admin Login
                </OutlineButton>
              </>
            ) : (
              <>
                <PrimaryButton href={user?.role === 'ADMIN' ? '/admin/dashboard' : '/dashboard'}>
                  <FiUser className="mr-2" />
                  Dashboard
                </PrimaryButton>
                <OutlineButton onClick={handleLogout}>
                  <FiLogOut className="mr-2" />
                  Logout
                </OutlineButton>
              </>
            )}
          </div>

          {/* API Status */}
          <div className="mt-8 bg-gray-800/50 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">API Configuration</h2>
            <p className="text-gray-400">
              API Base URL: <span className="text-white">{process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api'}</span>
            </p>
            <p className="text-sm text-yellow-400 mt-2">
              Note: This is a test page to verify authentication flow. The API endpoints are not implemented yet.
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
