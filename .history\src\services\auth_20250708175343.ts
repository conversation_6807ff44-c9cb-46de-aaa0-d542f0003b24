import {
  AuthResponse,
  LoginCredentials,
  SignupData,
  AdminLoginCredentials,
  PasswordResetRequest,
  PasswordResetConfirm,
  ChangePasswordData,
  RefreshTokenResponse,
  User
} from '../types/auth';
import { apiRequest, retryApiRequest } from './errorHandler';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;

class AuthService {
  private getAuthHeaders() {
    const token = localStorage.getItem('auth_token');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` })
    };
  }

  async login(credentials: LoginCredentials): Promise<any> {
    const apiRes = await apiRequest<any>(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(credentials)
    });

    // Log the full backend response for debugging
    console.log('LOGIN RESPONSE:', apiRes);

    // Defensive: check for token in the expected place
    if (apiRes?.data?.token) {
      console.log('Saving auth_token:', apiRes.data.token);
      localStorage.setItem('auth_token', apiRes.data.token);
    } else {
      console.warn('No token found in login response:', apiRes);
    }
    if (apiRes?.data?.refreshToken) {
      localStorage.setItem('refresh_token', apiRes.data.refreshToken);
    }

    // Return the full backend response for now
    return apiRes;
  }

  async signup(signupData: SignupData): Promise<AuthResponse> {
    const apiRes = await apiRequest<AuthResponse>(`${API_BASE_URL}/api/auth/signup`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(signupData)
    });

    // Store token in localStorage
    localStorage.setItem('auth_token', apiRes.data.token);
    if (apiRes.data.refreshToken) {
      localStorage.setItem('refresh_token', apiRes.data.refreshToken);
    }

    return {
      success: apiRes.success,
      message: apiRes.message,
      user: apiRes.data.user,
      token: apiRes.data.token,
      refreshToken: apiRes.data.refreshToken
    };
  }

  async adminLogin(credentials: AdminLoginCredentials): Promise<AuthResponse> {
    const response = await fetch(`${API_BASE_URL}/auth/admin/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(credentials)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Admin login failed');
    }

    const data = await response.json();
    
    // Store token in localStorage
    localStorage.setItem('auth_token', data.token);
    if (data.refreshToken) {
      localStorage.setItem('refresh_token', data.refreshToken);
    }

    return data;
  }

  async logout(): Promise<void> {
    try {
      await fetch(`${API_BASE_URL}/auth/logout`, {
        method: 'POST',
        headers: this.getAuthHeaders()
      });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear tokens regardless of API call success
      localStorage.removeItem('auth_token');
      localStorage.removeItem('refresh_token');
    }
  }

  async getCurrentUser(): Promise<User> {
    const response = await fetch(`${API_BASE_URL}/auth/me`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error('Failed to get current user');
    }

    const data = await response.json();
    return data.data.user; // Extract user from the response structure
  }

  async refreshToken(): Promise<RefreshTokenResponse> {
    const refreshToken = localStorage.getItem('refresh_token');

    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await fetch(`${API_BASE_URL}/auth/refresh`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ refreshToken })
    });

    if (!response.ok) {
      throw new Error('Token refresh failed');
    }

    const data = await response.json();
    localStorage.setItem('auth_token', data.data.token);
    if (data.data.refreshToken) {
      localStorage.setItem('refresh_token', data.data.refreshToken);
    }

    return {
      success: data.success,
      token: data.data.token,
      refreshToken: data.data.refreshToken
    };
  }

  async requestPasswordReset(data: PasswordResetRequest): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/auth/password-reset/request`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Password reset request failed');
    }
  }

  async confirmPasswordReset(data: PasswordResetConfirm): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/auth/password-reset/confirm`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Password reset failed');
    }
  }

  async changePassword(data: ChangePasswordData): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/auth/change-password`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Password change failed');
    }
  }

  getStoredToken(): string | null {
    return localStorage.getItem('auth_token');
  }

  isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp * 1000 < Date.now();
    } catch {
      return true;
    }
  }
}

export const authService = new AuthService();
