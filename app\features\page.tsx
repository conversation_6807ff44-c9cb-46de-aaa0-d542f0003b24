"use client";

import { useEffect } from "react";
import { motion } from "framer-motion";
import AOS from "aos";
import "aos/dist/aos.css";
import Link from "next/link";
import {
  FiSave,
  FiArrowLeft,
  FiTrendingUp,
  FiShield,
  FiSmartphone,
  FiUsers,
  FiCreditCard,
  FiPieChart,
  FiTarget,
  FiBell,
  FiLock,
  FiGlobe,
  FiHeadphones,
  FiZap,
} from "react-icons/fi";
import Background3D from "../../components/Background3D";
import { BackgroundAnimation } from "../../src/components/ui/BackgroundAnimation";
import { PrimaryButton, OutlineButton } from "../../src/components/ui/AnimatedButton";
import { FeatureImage3D, CardImage3D } from "../../src/components/ui/Image3D";

export default function FeaturesPage() {
  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
      easing: "ease-out-cubic",
    });
  }, []);

  const mainFeatures = [
    {
      icon: <FiSave className="w-12 h-12" data-oid="3g.1_o." />,
      title: "Smart Automated Savings",
      description:
        "Our intelligent algorithms analyze your spending patterns and automatically save the perfect amount for you. Set it once and watch your savings grow effortlessly.",
      benefits: [
        "Round-up transactions",
        "Percentage-based saving",
        "Goal-oriented automation",
        "Smart timing optimization",
      ],
      image: "/suite.png"
    },
    {
      icon: <FiTrendingUp className="w-12 h-12" data-oid="-h_mmtx" />,
      title: "Investment Growth Portfolio",
      description:
        "Diversify your savings with our carefully curated investment portfolios designed to maximize returns while minimizing risk.",
      benefits: [
        "Professional portfolio management",
        "Risk-adjusted returns",
        "Diversified investments",
        "Regular rebalancing",
      ],
      image: "/ChatGPT Image Jul 8, 2025, 03_09_19 PM.png"
    },
    {
      icon: <FiShield className="w-12 h-12" data-oid="61r_g.a" />,
      title: "Bank-Level Security",
      description:
        "Your money and data are protected with military-grade encryption, multi-factor authentication, and comprehensive insurance coverage.",
      benefits: [
        "256-bit SSL encryption",
        "Multi-factor authentication",
        "FDIC insurance coverage",
        "24/7 fraud monitoring",
      ],
      image: "/Entering Better Interest Office (1).png"
    },
    {
      icon: <FiSmartphone className="w-12 h-12" data-oid="10f5.yn" />,
      title: "Mobile-First Experience",
      description:
        "Manage your entire financial life from your smartphone with our intuitive, feature-rich mobile application.",
      benefits: [
        "iOS and Android apps",
        "Offline functionality",
        "Biometric authentication",
        "Real-time notifications",
      ],
      image: "/Celebrating with her iPhone 14 Pro.png"
    },
  ];

  const additionalFeatures = [
    {
      icon: <FiPieChart className="w-8 h-8" data-oid="nll5um8" />,
      title: "Advanced Analytics",
      description:
        "Get detailed insights into your spending habits and savings progress with comprehensive analytics and reporting.",
    },
    {
      icon: <FiTarget className="w-8 h-8" data-oid="l-dy-9r" />,
      title: "Goal Tracking",
      description:
        "Set and track multiple savings goals with visual progress indicators and milestone celebrations.",
    },
    {
      icon: <FiBell className="w-8 h-8" data-oid="zic.8nq" />,
      title: "Smart Notifications",
      description:
        "Stay informed with intelligent notifications about your savings progress, investment updates, and financial tips.",
    },
    {
      icon: <FiCreditCard className="w-8 h-8" data-oid="xroy20h" />,
      title: "Multiple Account Integration",
      description:
        "Connect all your bank accounts and credit cards for a complete view of your financial picture.",
    },
    {
      icon: <FiUsers className="w-8 h-8" data-oid="tgbzh58" />,
      title: "Family Savings Plans",
      description:
        "Create shared savings goals and teach financial literacy to your family members.",
    },
    {
      icon: <FiZap className="w-8 h-8" data-oid="bk0snlw" />,
      title: "Instant Transfers",
      description:
        "Move money between accounts instantly with our fast and secure transfer system.",
    },
    {
      icon: <FiLock className="w-8 h-8" data-oid="gi:m:f8" />,
      title: "Privacy Protection",
      description:
        "Your financial data is never sold or shared. Complete privacy and data ownership guaranteed.",
    },
    {
      icon: <FiGlobe className="w-8 h-8" data-oid="dmq5a_-" />,
      title: "Multi-Currency Support",
      description:
        "Save and invest in multiple currencies with real-time exchange rates and conversion.",
    },
    {
      icon: <FiHeadphones className="w-8 h-8" data-oid="18ug1w:" />,
      title: "24/7 Customer Support",
      description:
        "Get help whenever you need it with our round-the-clock customer support team.",
    },
  ];

  return (
    <div
      className="min-h-screen bg-black text-white overflow-hidden relative"
      data-oid="c48_oy7"
    >
      {/* 3D Background */}
      <Background3D />

      {/* Enhanced Animated Background */}
      <div className="fixed inset-0 z-0" data-oid="psuw-be">
        <div className="absolute inset-0 bg-black" data-oid="fj0c4gv"></div>
        <div className="mesh-gradient"></div>
        <div className="mesh-gradient-2"></div>
        <div className="mesh-gradient-3"></div>
        <div className="absolute inset-0 opacity-20" data-oid="u9-2r6l">
          {[...Array(40)].map((_, i) => (
            <div
              key={i}
              className="absolute bg-green-400 rounded-full floating-particle"
              style={{
                width: `${Math.random() * 3 + 1}px`,
                height: `${Math.random() * 3 + 1}px`,
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 8}s`,
                animationDuration: `${8 + Math.random() * 4}s`,
              }}
              data-oid="xsnibal"
            />
          ))}
        </div>
        <div className="absolute inset-0 grid-pattern opacity-5"></div>
      </div>

      {/* Navigation */}
      <nav className="relative z-50 px-6 py-4" data-oid="e53gww8">
        <div
          className="max-w-7xl mx-auto flex items-center justify-between"
          data-oid=".-cnwf8"
        >
          <Link
            href="/"
            className="flex items-center space-x-2 hover:opacity-80 transition-opacity"
            data-oid="xtfy7ih"
          >
            <FiArrowLeft className="w-5 h-5 text-white" data-oid="in6isr_" />
            <span className="text-white" data-oid="_g:jz9t">
              Back to Home
            </span>
          </Link>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center space-x-2"
            data-oid="kwzdsti"
          >
            <div
              className="w-10 h-10 bg-gradient-to-r from-green-400 to-green-600 rounded-lg flex items-center justify-center"
              data-oid="ez:zv.:"
            >
              <FiSave className="w-6 h-6 text-white" data-oid="yqoql90" />
            </div>
            <span className="text-2xl font-bold text-white" data-oid="oh20_es">
              Better Interest
            </span>
          </motion.div>
        </div>
      </nav>

      {/* Enhanced Hero Section */}
      <section className="relative z-10 px-6 py-32 hero-bg" data-oid="ovah0oh">
        <div className="max-w-7xl mx-auto text-center" data-oid=".hc80o:">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8 }}
            className="mb-8"
          >
            <div className="w-24 h-24 bg-gradient-to-r from-green-400 to-green-600 rounded-3xl flex items-center justify-center mx-auto mb-6 card-glow">
              <FiZap className="w-12 h-12 text-white" />
            </div>
          </motion.div>

          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-5xl md:text-7xl font-inter font-bold mb-6 text-white text-shadow-green"
            data-oid="_x:z.p."
          >
            Powerful{" "}
            <span className="gradient-text" data-oid="iv89o-s">
              Features
            </span>
          </motion.h1>

          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto mb-12"
            data-oid="19be.h_"
          >
            Discover all the advanced tools and intelligent features that make Koja Save
            the smartest choice for your financial future. Every feature is designed to
            maximize your savings potential and simplify your financial journey.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16"
          >
            <Link
              href="/signup"
              className="btn-advanced px-8 py-4 rounded-lg text-white font-semibold hover-lift"
            >
              Try Features Free
            </Link>
            <Link
              href="/packages"
              className="px-8 py-4 border border-green-400 rounded-lg text-white hover:bg-green-400 hover:text-black transition-all hover-lift"
            >
              View Pricing
            </Link>
          </motion.div>

          {/* Feature highlights */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto"
          >
            {[
              { icon: <FiSave />, label: "Smart Automation" },
              { icon: <FiShield />, label: "Bank Security" },
              { icon: <FiTrendingUp />, label: "Growth Tracking" },
              { icon: <FiSmartphone />, label: "Mobile First" }
            ].map((item, i) => (
              <div key={i} className="glass-advanced rounded-xl p-4 text-center hover-lift-advanced">
                <div className="text-green-400 text-2xl mb-2">{item.icon}</div>
                <div className="text-sm text-gray-300">{item.label}</div>
              </div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Main Features */}
      <section className="relative z-10 px-6 py-20" data-oid="g8viix4">
        <div className="max-w-7xl mx-auto" data-oid="254vlq5">
          <div className="space-y-20" data-oid="-wrzw_0">
            {mainFeatures.map((feature, index) => (
              <div
                key={index}
                data-aos="fade-up"
                data-aos-delay={index * 100}
                className={`grid lg:grid-cols-2 gap-12 items-center ${
                  index % 2 === 1 ? "lg:grid-flow-col-dense" : ""
                }`}
                data-oid="27ulhdp"
              >
                <div
                  className={index % 2 === 1 ? "lg:col-start-2" : ""}
                  data-oid="2v.-5-v"
                >
                  <div className="text-green-400 mb-6" data-oid="cyqo3v8">
                    {feature.icon}
                  </div>
                  <h3
                    className="text-3xl font-bold mb-4 text-white"
                    data-oid="05dlghf"
                  >
                    {feature.title}
                  </h3>
                  <p className="text-xl text-gray-300 mb-6" data-oid="8giv83g">
                    {feature.description}
                  </p>
                  <ul className="space-y-3" data-oid="b:b28y_">
                    {feature.benefits.map((benefit, benefitIndex) => (
                      <li
                        key={benefitIndex}
                        className="flex items-center gap-3"
                        data-oid="qh41kdu"
                      >
                        <div
                          className="w-2 h-2 bg-green-400 rounded-full"
                          data-oid="k81cjft"
                        ></div>
                        <span className="text-gray-300" data-oid="um81lm8">
                          {benefit}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
                <div
                  className={`relative flex justify-center ${
                    index % 2 === 1 ? "lg:col-start-1" : ""
                  }`}
                  data-oid="7jv1g50"
                >
                  <div className="relative">
                    <FeatureImage3D
                      src={feature.image}
                      alt={`${feature.title} - BetterInterest Feature`}
                      width={500}
                      height={350}
                      className="w-full h-auto rounded-xl mx-auto"
                    />
                    <div className="absolute bottom-4 left-4 bg-black/70 backdrop-blur-sm rounded-lg p-3 z-20">
                      <div className="text-green-400 mb-1">
                        {feature.icon}
                      </div>
                      <h4 className="text-white font-semibold text-sm">{feature.title}</h4>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Additional Features Grid */}
      <section className="relative z-10 px-6 py-20" data-oid="mggu3ke">
        <div className="max-w-7xl mx-auto" data-oid="ytul_ri">
          <div
            className="text-center mb-16"
            data-aos="fade-up"
            data-oid="7-xeret"
          >
            <h2
              className="text-4xl md:text-5xl font-bold mb-6 text-white"
              data-oid="pxbw2m6"
            >
              More{" "}
              <span className="text-green-400" data-oid="y2imm_w">
                Amazing Features
              </span>
            </h2>
            <p
              className="text-xl text-gray-300 max-w-3xl mx-auto"
              data-oid="z1afgzh"
            >
              Every feature is designed to make your financial journey smoother
              and more rewarding.
            </p>
          </div>

          <div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
            data-oid="67um585"
          >
            {additionalFeatures.map((feature, index) => (
              <div
                key={index}
                data-aos="fade-up"
                data-aos-delay={index * 100}
                className="app-card hover-lift-advanced p-6 card-stack"
                data-oid="j6hcow."
              >
                <div
                  className="text-green-400 mb-4 group-hover:animate-pulse-green transition-all notification-dot"
                  data-oid="gguy15w"
                >
                  {feature.icon}
                </div>
                <h3
                  className="text-xl font-semibold mb-3 text-white"
                  data-oid="qnvotxi"
                >
                  {feature.title}
                </h3>
                <p className="text-gray-300 mb-4" data-oid="9ordk0n">
                  {feature.description}
                </p>
                <div className="w-full bg-gray-800 rounded-full h-1">
                  <div
                    className="bg-gradient-to-r from-green-400 to-green-600 h-1 rounded-full"
                    style={{ width: `${Math.random() * 40 + 60}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Feature Gallery */}
      <section className="relative z-10 px-6 py-20">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16" data-aos="fade-up">
            <h2 className="text-4xl md:text-5xl font-inter font-bold mb-6 text-white text-shadow">
              Feature{" "}
              <span className="text-green-400">
                Gallery
              </span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              See our features in action with real user experiences and success stories.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                image: "/Celebrating with Her New iPhone.png",
                title: "Mobile Success",
                description: "Users celebrating their savings milestones",
                features: ["Mobile App", "Goal Tracking", "Notifications"]
              },
              {
                image: "/Celebrating with Her New iPhone (1).png",
                title: "Achievement Unlocked",
                description: "Reaching financial goals with BetterInterest",
                features: ["Smart Savings", "Auto-Investment", "Progress Tracking"]
              },
              {
                image: "/ChatGPT Image Jul 8, 2025, 03_27_34 PM.png",
                title: "Professional Growth",
                description: "Building wealth through consistent saving",
                features: ["Portfolio Management", "Risk Assessment", "Growth Analytics"]
              }
            ].map((item, index) => (
              <div
                key={index}
                data-aos="fade-up"
                data-aos-delay={index * 100}
                className="app-card hover-lift-advanced p-6"
              >
                <div className="mb-6 flex justify-center">
                  <CardImage3D
                    src={item.image}
                    alt={item.title}
                    width={300}
                    height={200}
                    className="w-full h-auto rounded-lg mx-auto"
                  />
                </div>

                <h3 className="text-xl font-semibold mb-3 text-white">
                  {item.title}
                </h3>

                <p className="text-gray-300 mb-4 text-sm">
                  {item.description}
                </p>

                <div className="flex flex-wrap gap-2">
                  {item.features.map((feature, featureIndex) => (
                    <span
                      key={featureIndex}
                      className="inline-block bg-green-500/20 text-green-400 px-2 py-1 rounded-full text-xs font-medium"
                    >
                      {feature}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative z-10 px-6 py-20" data-oid="d8ti-0y">
        <div
          className="max-w-4xl mx-auto text-center"
          data-aos="fade-up"
          data-oid="der8bal"
        >
          <div
            className="glass-advanced rounded-2xl p-12 app-card-premium"
            data-oid="w:vh4c1"
          >
            <div className="w-16 h-16 bg-gradient-to-r from-green-400 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <FiZap className="w-8 h-8 text-white" />
            </div>
            <h2
              className="text-4xl md:text-5xl font-bold mb-6 text-white text-glow"
              data-oid="aeaa65r"
            >
              Ready to Experience These{" "}
              <span className="gradient-text" data-oid="i81c1cq">
                Features?
              </span>
            </h2>
            <p className="text-xl text-gray-300 mb-8" data-oid="oe_tjv-">
              Join thousands of users who are already enjoying the benefits of
              smart saving and intelligent financial management.
            </p>
            <div
              className="flex flex-col sm:flex-row gap-4 justify-center mb-8"
              data-oid="jw2qcfv"
            >
              <Link
                href="/signup"
                className="btn-advanced px-8 py-4 rounded-lg text-lg font-semibold text-white hover-lift"
                data-oid="bg7jd3e"
              >
                Get Started Now
              </Link>
              <Link
                href="/packages"
                className="px-8 py-4 border border-green-400 rounded-lg text-lg font-semibold text-white hover:bg-green-400 hover:text-black transition-all hover-lift magnetic"
                data-oid="pqtwav9"
              >
                View Packages
              </Link>
            </div>

            {/* Feature stats */}
            <div className="grid grid-cols-3 gap-6 max-w-md mx-auto">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">50+</div>
                <div className="text-sm text-gray-400">Features</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">99.9%</div>
                <div className="text-sm text-gray-400">Uptime</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">24/7</div>
                <div className="text-sm text-gray-400">Support</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer
        className="relative z-10 px-6 py-12 border-t border-gray-700"
        data-oid="hwl1dxm"
      >
        <div className="max-w-7xl mx-auto text-center" data-oid="ebiwire">
          <div
            className="flex items-center justify-center space-x-2 mb-4"
            data-oid="a-kkm2_"
          >
            <div
              className="w-8 h-8 bg-gradient-to-r from-green-400 to-green-600 rounded-lg flex items-center justify-center"
              data-oid="o1tj0fv"
            >
              <FiSave className="w-5 h-5 text-white" data-oid="bzb:q5y" />
            </div>
            <span className="text-xl font-bold text-white" data-oid="opy-v69">
              Better Interest
            </span>
          </div>
          <p className="text-gray-400" data-oid="o0uff2y">
            Building the future of personal finance, one save at a time.
          </p>
        </div>
      </footer>
    </div>
  );
}
