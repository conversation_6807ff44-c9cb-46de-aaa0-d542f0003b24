"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../../hooks/use-auth';
import { useTheme } from '../../contexts/ThemeContext';
import {
  FiMenu,
  FiX,
  FiHome,
  FiPieChart,
  FiCreditCard,
  FiUsers,
  FiSettings,
  FiBell,
  FiUser,
  FiLogOut,
  FiShield,
  FiDollarSign,
  FiTrendingUp,
  FiFileText,
  FiUserCheck,
  FiSearch,
  FiTarget,
  FiZap
} from 'react-icons/fi';
import NotificationDropdown from '../navbar/NotificationDropdown';
import SettingsDropdown from '../navbar/SettingsDropdown';
import ProfileDropdown from '../navbar/ProfileDropdown';
import { IconButton } from '../ui/Button';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

interface DashboardLayoutProps {
  children: React.ReactNode;
  title?: string;
}

interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<any>;
  roles?: ('USER' | 'ADMIN')[];
  description?: string;
  badge?: string;
  isNew?: boolean;
}

const userNavItems: NavItem[] = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: FiHome,
    description: 'Overview and quick actions'
  },
  {
    name: 'Group Savings',
    href: '/dashboard/group-savings',
    icon: FiUsers,
    description: 'Collaborative savings',
    isNew: true
  },
  {
    name: 'Investment',
    href: '/dashboard/investment',
    icon: FiTrendingUp,
    description: 'Grow your wealth'
  },
  {
    name: 'Target Savings',
    href: '/dashboard/target-savings',
    icon: FiTarget,
    description: 'Goal-based savings'
  },
  {
    name: 'Individual Savings',
    href: '/dashboard/savings-plans',
    icon: FiPieChart,
    description: 'Normal savings plans'
  },
  {
    name: 'Payments',
    href: '/dashboard/payments',
    icon: FiCreditCard,
    description: 'Payment history'
  },
  {
    name: 'KYC Verification',
    href: '/dashboard/kyc',
    icon: FiUserCheck,
    description: 'Identity verification'
  },
  {
    name: 'Profile',
    href: '/dashboard/profile',
    icon: FiUser,
    description: 'Account settings'
  },
  {
    name: 'Settings',
    href: '/dashboard/settings',
    icon: FiSettings,
    description: 'App preferences'
  },
];

const adminNavItems: NavItem[] = [
  {
    name: 'Admin Dashboard',
    href: '/admin/dashboard',
    icon: FiShield,
    description: 'Admin overview'
  },
  {
    name: 'User Management',
    href: '/admin/users',
    icon: FiUsers,
    description: 'Manage users',
    badge: '12'
  },
  {
    name: 'Payment Management',
    href: '/admin/payments',
    icon: FiDollarSign,
    description: 'Payment oversight'
  },
  {
    name: 'KYC Management',
    href: '/admin/kyc',
    icon: FiUserCheck,
    description: 'Identity verification',
    badge: '5'
  },
  {
    name: 'Analytics',
    href: '/admin/analytics',
    icon: FiTrendingUp,
    description: 'Platform analytics'
  },
  {
    name: 'Notifications',
    href: '/admin/notifications',
    icon: FiBell,
    description: 'System notifications',
    badge: '3'
  },
  {
    name: 'Reports',
    href: '/admin/reports',
    icon: FiFileText,
    description: 'Generate reports'
  },
  {
    name: 'Settings',
    href: '/admin/settings',
    icon: FiSettings,
    description: 'System settings'
  },
];

export default function DashboardLayout({ children, title }: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [isLargeScreen, setIsLargeScreen] = useState(false);
  const router = useRouter();
  const { user, logout, isLoading } = useAuth();
  const { theme, toggleTheme } = useTheme();

  // Check screen size
  useEffect(() => {
    const checkScreenSize = () => {
      setIsLargeScreen(window.innerWidth >= 1024);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  const handleLogout = async () => {
    try {
      await logout();
      router.push('/');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  // Use actual user data or fallback to demo data
  const currentUser = user || {
    firstName: 'Demo',
    lastName: 'User',
    email: '<EMAIL>',
    role: 'USER'
  };

  const navItems = currentUser?.role === 'ADMIN' ? adminNavItems : userNavItems;

  return (
    <div className={`min-h-screen flex transition-colors duration-300 ${
      theme === 'light'
        ? 'bg-gradient-to-br from-gray-50 via-white to-gray-100 text-gray-900'
        : 'bg-gradient-to-br from-gray-900 via-black to-gray-800 text-white'
    }`}>
      {/* Mobile sidebar backdrop */}
      <AnimatePresence>
        {sidebarOpen && !isLargeScreen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-40 bg-black/50"
            onClick={() => setSidebarOpen(false)}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <div
        className={`
          ${isLargeScreen
            ? 'relative translate-x-0'
            : `fixed ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}`
          }
          inset-y-0 left-0
          ${isLargeScreen ? 'z-10' : 'z-50'}
          w-80 backdrop-blur-lg border-r flex flex-col
          transition-transform duration-300 ease-in-out
          ${theme === 'light'
            ? 'bg-white/95 border-gray-200 text-gray-900 shadow-xl'
            : 'bg-gray-900/95 border-gray-700 text-white shadow-2xl'
          }
        `}
        style={{
          borderRadius: '0 20px 20px 0',
          boxShadow: theme === 'light'
            ? '0 4px 20px rgba(0, 0, 0, 0.08), 0 8px 40px rgba(34, 197, 94, 0.12), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
            : '0 4px 20px rgba(0, 0, 0, 0.3), 0 8px 40px rgba(34, 197, 94, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.05)',
          border: '1px solid rgba(34, 197, 94, 0.2)',
        }}
      >
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center justify-between h-16 px-6 border-b border-gray-800">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 flex items-center justify-center">
                <Image
                  src="/logo.svg"
                  alt="Better Interest Logo"
                  width={32}
                  height={32}
                  className="w-8 h-8 object-contain"
                  priority
                />
              </div>
              <span className="text-xl font-bold">BetterInterest</span>
            </div>
            <button
              onClick={() => setSidebarOpen(false)}
              className="lg:hidden p-2 rounded-lg hover:bg-gray-800 transition-colors"
            >
              <FiX className="w-5 h-5" />
            </button>
          </div>

          {/* User info */}
          <div className="p-6 border-b border-gray-800">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center">
                <span className="text-black font-semibold">
                  {currentUser?.firstName?.[0]}{currentUser?.lastName?.[0]}
                </span>
              </div>
              <div>
                <p className="font-medium">{currentUser?.firstName} {currentUser?.lastName}</p>
                <p className="text-sm text-gray-400">{currentUser?.email}</p>
                <span className={`inline-block px-2 py-1 text-xs rounded-full mt-1 ${
                  currentUser?.role === 'ADMIN'
                    ? 'bg-red-500/20 text-red-400'
                    : 'bg-green-500/20 text-green-400'
                }`}>
                  {currentUser?.role}
                </span>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-6 py-6 space-y-1 overflow-y-auto">
            {navItems.map((item, index) => (
              <motion.div
                key={item.name}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <Link
                  href={item.href}
                  className={`
                    flex items-center justify-between px-4 py-3 rounded-lg
                    transition-all duration-200 group relative overflow-hidden
                    font-inter font-medium transform-gpu hover:scale-105 active:scale-95
                    border border-transparent hover:border-brand/20
                    ${theme === 'light'
                      ? 'hover:bg-gray-100 hover:shadow-md'
                      : 'hover:bg-gray-800 hover:shadow-lg'
                    }
                    hover:shadow-brand/10
                  `}
                  onClick={() => setSidebarOpen(false)}
                  style={{
                    boxShadow: '0 2px 8px rgba(34, 197, 94, 0.1)'
                  }}
                >
                  {/* 3D Border Effect */}
                  <div className="absolute left-0 top-0 bottom-0 w-1 bg-transparent group-hover:bg-brand rounded-r-full transition-all duration-200 shadow-lg shadow-brand/50" />

                  {/* Enhanced Background glow effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-brand/5 to-brand/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></div>

                  <div className="flex items-center space-x-3 relative z-10">
                    <div className="relative">
                      <item.icon className="w-5 h-5 text-brand transition-all duration-200 group-hover:drop-shadow-sm group-hover:scale-110" />
                      {item.isNew && (
                        <div className="absolute -top-1 -right-1 w-2 h-2 bg-brand rounded-full animate-pulse shadow-sm"></div>
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <span className={`font-medium transition-colors duration-200 ${
                          theme === 'light'
                            ? 'text-gray-700 group-hover:text-brand'
                            : 'text-gray-300 group-hover:text-white'
                        }`}>
                          {item.name}
                        </span>
                        {item.isNew && (
                          <span className="px-1.5 py-0.5 text-xs bg-brand text-white rounded-full shadow-sm">
                            NEW
                          </span>
                        )}
                      </div>
                      {item.description && (
                        <p className={`text-xs transition-colors duration-200 mt-0.5 ${
                          theme === 'light'
                            ? 'text-gray-500 group-hover:text-gray-600'
                            : 'text-gray-500 group-hover:text-gray-300'
                        }`}>
                          {item.description}
                        </p>
                      )}
                    </div>
                  </div>

                  {item.badge && (
                    <div className="relative z-10">
                      <span className="px-2 py-1 text-xs bg-brand text-white rounded-full shadow-md">
                        {item.badge}
                      </span>
                    </div>
                  )}

                  {/* Hover shine effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 rounded-lg" />

                  {/* Hover arrow */}
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 relative z-10">
                    <svg className="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </Link>
              </motion.div>
            ))}
          </nav>

          {/* Footer Actions */}
          <div className="p-6 border-t border-gray-800 space-y-3">
            {/* Theme Toggle */}
            <div className="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg border border-gray-700">
              <span className="text-sm font-medium text-white">Theme</span>
              <button
                onClick={toggleTheme}
                className="p-2 rounded-lg bg-gray-700 hover:bg-gray-600 transition-all duration-200 border border-gray-600 hover:border-green-500"
                title={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
              >
                {theme === 'light' ? (
                  <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                  </svg>
                ) : (
                  <svg className="w-4 h-4 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                )}
              </button>
            </div>

            {/* Logout */}
            <button
              onClick={handleLogout}
              className="flex items-center space-x-3 w-full px-4 py-3 rounded-lg hover:bg-red-500/20 hover:text-red-400 transition-all duration-200 group"
            >
              <FiLogOut className="w-5 h-5 group-hover:scale-110 transition-transform duration-200" />
              <span className="font-medium">Logout</span>
            </button>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col min-w-0 lg:ml-0">
        {/* Top bar */}
        <header className={`h-16 backdrop-blur-sm border-b flex items-center justify-between px-6 relative ${
          theme === 'light'
            ? 'bg-white/80 border-gray-200'
            : 'bg-gray-900/80 border-gray-800'
        }`}>
          {/* Background gradient */}
          <div className={`absolute inset-0 ${
            theme === 'light'
              ? 'bg-gradient-to-r from-white/50 to-gray-50/50'
              : 'bg-gradient-to-r from-gray-900/50 to-gray-800/50'
          }`}></div>

          <div className="flex items-center space-x-4 relative z-10">
            <motion.button
              onClick={() => setSidebarOpen(true)}
              className={`lg:hidden p-2 rounded-lg transition-all duration-200 hover:scale-105 ${
                theme === 'light'
                  ? 'hover:bg-gray-100 text-gray-700'
                  : 'hover:bg-gray-800 text-gray-300'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <FiMenu className="w-5 h-5" />
            </motion.button>
            {title && (
              <h1 className={`text-xl font-bold font-inter bg-gradient-to-r bg-clip-text text-transparent ${
                theme === 'light'
                  ? 'from-gray-800 to-gray-600'
                  : 'from-white to-gray-300'
              }`}>
                {title}
              </h1>
            )}
          </div>

          <div className="flex items-center space-x-3 relative z-10">
            {/* Search */}
            <IconButton
              icon={FiSearch}
              tooltip="Search"
              className="text-brand"
            />

            {/* Notifications */}
            <NotificationDropdown />

            {/* Settings */}
            <SettingsDropdown />

            {/* Profile */}
            <ProfileDropdown />
          </div>
        </header>

        {/* Page content */}
        <main className={`p-6 flex-1 ${
          theme === 'light'
            ? 'bg-gray-50/50'
            : 'bg-transparent'
        }`}>
          {children}
        </main>
      </div>
    </div>
  );
}
