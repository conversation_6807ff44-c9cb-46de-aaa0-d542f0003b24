import { 
  GroupSavings, 
  GroupMember,
  GroupContribution,
  PayoutSchedule,
  CreateGroupSavingsData, 
  JoinGroupData,
  InviteGroupMemberData,
  GroupContributionData,
  GroupSearchFilters,
  GroupStats,
  ApiResponse
} from '../types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;

class GroupSavingsService {
  private getAuthHeaders() {
    const token = localStorage.getItem('auth_token');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` })
    };
  }

  // Group Management
  // List all public group savings plans
  async getAllGroups(): Promise<GroupSavings[]> {
    const url = `${API_BASE_URL}/api/group-savings/group-plans/public`;
    console.log('[GroupSavingsService] GET', url);
    try {
      const response = await fetch(url, {
        headers: this.getAuthHeaders()
      });
      console.log('[GroupSavingsService] Response status:', response.status);
      const text = await response.text();
      try {
        const data = JSON.parse(text);
        if (!response.ok) {
          console.error('[GroupSavingsService] Error response:', data);
          throw new Error(data.message || 'Failed to fetch group savings');
        }
        return data;
      } catch (jsonErr) {
        console.error('[GroupSavingsService] JSON parse error:', jsonErr, 'Raw response:', text);
        throw new Error('Invalid response from server.');
      }
    } catch (err) {
      console.error('[GroupSavingsService] getAllGroups error:', err);
      throw err;
    }
  }

  async getUserGroups(): Promise<GroupSavings[]> {
    const url = `${API_BASE_URL}/api/group-savings/group-plans/my`;
    console.log('[GroupSavingsService] GET', url);
    try {
      const response = await fetch(url, {
        headers: this.getAuthHeaders()
      });
      console.log('[GroupSavingsService] Response status:', response.status);
      const text = await response.text();
      try {
        const data = JSON.parse(text);
        if (!response.ok) {
          console.error('[GroupSavingsService] Error response:', data);
          throw new Error(data.message || 'Failed to fetch user groups');
        }
        return data;
      } catch (jsonErr) {
        console.error('[GroupSavingsService] JSON parse error:', jsonErr, 'Raw response:', text);
        throw new Error('Invalid response from server.');
      }
    } catch (err) {
      console.error('[GroupSavingsService] getUserGroups error:', err);
      throw err;
    }
  }

  async getGroupById(groupId: string): Promise<GroupSavings> {
    const url = `${API_BASE_URL}/api/group-savings/group-plans/${groupId}`;
    console.log('[GroupSavingsService] GET', url);
    try {
      const response = await fetch(url, {
        headers: this.getAuthHeaders()
      });
      console.log('[GroupSavingsService] Response status:', response.status);
      const text = await response.text();
      try {
        const data = JSON.parse(text);
        if (!response.ok) {
          console.error('[GroupSavingsService] Error response:', data);
          throw new Error(data.message || 'Failed to fetch group details');
        }
        return data;
      } catch (jsonErr) {
        console.error('[GroupSavingsService] JSON parse error:', jsonErr, 'Raw response:', text);
        throw new Error('Invalid response from server.');
      }
    } catch (err) {
      console.error('[GroupSavingsService] getGroupById error:', err);
      throw err;
    }
  }

  async createGroup(data: CreateGroupSavingsData): Promise<GroupSavings> {
    // Map frontend fields to backend expected fields
    const mapped = {
      title: data.name,
      depositFrequency: data.frequency,
      depositAmount: data.contributionAmount,
      targetDate: data.startDate,
      targetAmount: data.targetAmount,
      isPublic: data.isPublic,
      maxMembers: data.maxMembers,
      description: data.description,
      duration: data.duration,
      category: data.category,
      pendingInvites: data.pendingInvites,
    };
    const url = `${API_BASE_URL}/api/group-savings/group-plan`;
    console.log('[GroupSavingsService] POST', url, 'Payload:', mapped);
    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(mapped)
      });
      console.log('[GroupSavingsService] Response status:', response.status);
      const text = await response.text();
      try {
        const resData = JSON.parse(text);
        if (!response.ok) {
          console.error('[GroupSavingsService] Error response:', resData);
          throw new Error(resData.message || 'Failed to create group');
        }
        return resData;
      } catch (jsonErr) {
        console.error('[GroupSavingsService] JSON parse error:', jsonErr, 'Raw response:', text);
        throw new Error('Invalid response from server.');
      }
    } catch (err) {
      console.error('[GroupSavingsService] createGroup error:', err);
      throw err;
    }
  }

  async updateGroup(groupId: string, data: Partial<CreateGroupSavingsData>): Promise<GroupSavings> {
    const url = `${API_BASE_URL}/api/group-savings/${groupId}`;
    console.log('[GroupSavingsService] PUT', url, 'Payload:', data);
    try {
      const response = await fetch(url, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(data)
      });
      console.log('[GroupSavingsService] Response status:', response.status);
      const text = await response.text();
      try {
        const resData = JSON.parse(text);
        if (!response.ok) {
          console.error('[GroupSavingsService] Error response:', resData);
          throw new Error(resData.message || 'Failed to update group');
        }
        return resData;
      } catch (jsonErr) {
        console.error('[GroupSavingsService] JSON parse error:', jsonErr, 'Raw response:', text);
        throw new Error('Invalid response from server.');
      }
    } catch (err) {
      console.error('[GroupSavingsService] updateGroup error:', err);
      throw err;
    }
  }

  async deleteGroup(groupId: string): Promise<void> {
    const url = `${API_BASE_URL}/api/group-savings/${groupId}`;
    console.log('[GroupSavingsService] DELETE', url);
    try {
      const response = await fetch(url, {
        method: 'DELETE',
        headers: this.getAuthHeaders()
      });
      console.log('[GroupSavingsService] Response status:', response.status);
      if (!response.ok) {
        let error;
        try {
          error = await response.json();
        } catch (jsonErr) {
          console.error('[GroupSavingsService] JSON parse error:', jsonErr);
          throw new Error('Invalid response from server.');
        }
        console.error('[GroupSavingsService] Error response:', error);
        throw new Error(error.message || 'Failed to delete group');
      }
    } catch (err) {
      console.error('[GroupSavingsService] deleteGroup error:', err);
      throw err;
    }
  }

  // Group Membership
  async joinGroup(data: JoinGroupData): Promise<{ group: GroupSavings; member: GroupMember }> {
    const response = await fetch(`${API_BASE_URL}/api/group-savings/join`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to join group');
    }

    return response.json();
  }

  async leaveGroup(groupId: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/api/group-savings/${groupId}/leave`, {
      method: 'POST',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to leave group');
    }
  }

  async inviteMember(data: InviteGroupMemberData): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/api/group-savings/invite`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to send invitation');
    }
  }

  async removeMember(groupId: string, memberId: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/api/group-savings/${groupId}/members/${memberId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to remove member');
    }
  }

  async updateMemberRole(groupId: string, memberId: string, role: 'ADMIN' | 'MEMBER'): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/api/group-savings/${groupId}/members/${memberId}/role`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ role })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update member role');
    }
  }

  // Group Contributions
  async makeContribution(data: GroupContributionData): Promise<GroupContribution> {
    const response = await fetch(`${API_BASE_URL}/api/group-savings/contribute`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to make contribution');
    }

    return response.json();
  }

  async getGroupContributions(groupId: string, filters?: {
    userId?: string;
    status?: string;
    dateFrom?: string;
    dateTo?: string;
    page?: number;
    limit?: number;
  }): Promise<{ contributions: GroupContribution[]; total: number; page: number; limit: number }> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/api/group-savings/${groupId}/contributions?${params}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch contributions');
    }

    return response.json();
  }

  async getUserContributions(groupId: string): Promise<GroupContribution[]> {
    const response = await fetch(`${API_BASE_URL}/api/group-savings/${groupId}/contributions/user`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch user contributions');
    }

    return response.json();
  }

  // Payout Management
  async getPayoutSchedule(groupId: string): Promise<PayoutSchedule[]> {
    const response = await fetch(`${API_BASE_URL}/api/group-savings/${groupId}/payout-schedule`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch payout schedule');
    }

    return response.json();
  }

  async updatePayoutSchedule(groupId: string, schedule: Partial<PayoutSchedule>[]): Promise<PayoutSchedule[]> {
    const response = await fetch(`${API_BASE_URL}/api/group-savings/${groupId}/payout-schedule`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ schedule })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update payout schedule');
    }

    return response.json();
  }

  async processPayout(groupId: string, payoutId: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/api/group-savings/${groupId}/payout/${payoutId}/process`, {
      method: 'POST',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to process payout');
    }
  }

  // Group Statistics
  async getGroupStats(): Promise<GroupStats> {
    const response = await fetch(`${API_BASE_URL}/api/group-savings/stats`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch group statistics');
    }

    return response.json();
  }

  async getGroupAnalytics(groupId: string): Promise<any> {
    const response = await fetch(`${API_BASE_URL}/api/group-savings/${groupId}/analytics`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch group analytics');
    }

    return response.json();
  }

  // Fetch active group savings for the logged-in user
  async getActiveGroups(): Promise<GroupSavings[]> {
    const response = await fetch(`${API_BASE_URL}/api/savings/group/active`, {
      headers: this.getAuthHeaders()
    });
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch active group savings');
    }
    const data = await response.json();
    return data.activeGroups || [];
  }

  // Utility Methods
  calculateGroupProgress(group: GroupSavings): number {
    return Math.min((group.currentAmount / group.targetAmount) * 100, 100);
  }

  calculateMemberContributionRate(member: GroupMember, totalContributions: number): number {
    if (totalContributions === 0) return 0;
    return (member.totalContributions / totalContributions) * 100;
  }

  getNextPayoutDate(schedule: PayoutSchedule[]): string | null {
    const upcomingPayouts = schedule
      .filter(p => p.status === 'PENDING')
      .sort((a, b) => new Date(a.scheduledDate).getTime() - new Date(b.scheduledDate).getTime());
    
    return upcomingPayouts.length > 0 ? upcomingPayouts[0].scheduledDate : null;
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN'
    }).format(amount);
  }

  generateInviteCode(): string {
    return Math.random().toString(36).substring(2, 8).toUpperCase();
  }
}

export const groupSavingsService = new GroupSavingsService();
